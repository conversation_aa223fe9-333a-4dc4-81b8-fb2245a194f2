# 🚀 个人空间与团队空间完整功能清单及接口定义

## 📋 功能清单总览

基于对个人空间、团队空间、团队详情页面的详细分析，以下是完整的功能模块和交互操作清单：

## 🏠 个人空间功能模块

### 1. 个人资料管理
- **头像管理**：上传、更换、删除头像
- **基本信息编辑**：姓名（只读）、用户名、部门、位置
- **个人简介编辑**：支持实时编辑，字符限制200字
- **技能标签管理**：添加、删除技能标签，支持不同等级
- **个人设置**：隐私设置、通知设置

### 2. 统计数据展示
- **内容统计**：发布内容数量、总浏览量、总点赞数、总收藏数
- **社交统计**：粉丝数量、关注数量
- **成就展示**：徽章系统、等级显示
- **活跃度指标**：连续学习天数、贡献分数

### 3. 内容管理系统
- **我的内容**：已发布、草稿、已删除内容管理
- **收藏内容**：按类型分类的收藏内容
- **点赞内容**：点赞过的内容历史
- **内容筛选**：按知识类型、时间、状态筛选
- **内容搜索**：标题、标签、内容搜索

### 4. 学习进度管理
- **课程进度**：正在学习的课程列表
- **学习统计**：总学习时长、完成课程数
- **学习计划**：制定和跟踪学习目标
- **证书管理**：获得的证书和认证

### 5. 团队空间管理
- **我的团队**：已加入的团队列表
- **团队角色**：在各团队中的角色和权限
- **团队贡献**：在各团队中的贡献统计
- **团队推荐**：基于兴趣的团队推荐

### 6. 活动历史记录
- **操作记录**：发布、编辑、删除等操作历史
- **互动记录**：点赞、评论、收藏等互动历史
- **学习记录**：课程学习、技能提升记录
- **团队活动**：团队相关活动参与记录

### 7. 社交功能
- **关注系统**：关注/取消关注用户
- **粉丝管理**：查看粉丝列表
- **消息通知**：系统通知、互动通知
- **个人主页分享**：生成分享链接

## 🏢 团队空间列表功能模块

### 1. 团队发现与搜索
- **智能搜索**：团队名称、描述、标签搜索
- **搜索建议**：基于输入的智能建议
- **搜索历史**：保存搜索历史记录
- **热门搜索**：显示热门搜索词

### 2. 团队筛选与排序
- **标签筛选**：按热门标签筛选团队
- **隐私筛选**：公开/私有团队筛选
- **成员数筛选**：按团队规模筛选
- **活跃度筛选**：按最近活动时间筛选
- **多维排序**：按创建时间、成员数、活跃度、推荐度排序

### 3. 团队展示与预览
- **团队卡片**：展示团队基本信息、统计数据
- **团队预览**：快速预览团队详情
- **团队状态**：显示公开/私有、已加入状态
- **团队标签**：显示团队相关标签
- **创建者信息**：显示团队创建者

### 4. 团队交互操作
- **收藏团队**：收藏/取消收藏团队
- **分享团队**：生成团队分享链接
- **加入团队**：直接加入公开团队
- **申请加入**：向私有团队提交申请
- **团队评价**：对团队进行评分和评价

### 5. 团队创建管理
- **创建团队**：填写团队基本信息
- **团队设置**：隐私设置、邀请设置
- **团队头像**：上传团队头像
- **团队标签**：添加团队相关标签
- **创建模板**：使用预设模板快速创建

## 🏗️ 团队详情功能模块

### 1. 团队基本信息
- **团队概览**：名称、描述、头像、标签
- **团队统计**：成员数、内容数、讨论数、浏览量
- **团队状态**：公开/私有、认证状态、活跃状态
- **创建信息**：创建时间、创建者、团队位置
- **团队主题**：自定义主题色彩和样式

### 2. 团队成员管理
- **成员列表**：显示所有团队成员
- **在线成员**：显示当前在线成员
- **成员角色**：管理员、普通成员角色管理
- **成员权限**：不同角色的权限设置
- **成员贡献**：成员贡献度排行榜
- **成员邀请**：邀请新成员加入
- **成员移除**：移除团队成员

### 3. 团队内容管理
- **内容展示**：团队推荐的内容列表
- **内容分类**：按知识类型分类展示
- **内容搜索**：在团队内容中搜索
- **内容推荐**：向团队推荐新内容
- **内容审核**：管理员审核推荐内容
- **内容统计**：内容浏览、点赞、收藏统计

### 4. 团队协作功能
- **团队聊天**：实时团队聊天功能
- **讨论区**：团队讨论话题管理
- **文件共享**：团队文件上传和共享
- **日程管理**：团队活动和会议安排
- **任务管理**：团队任务分配和跟踪
- **公告发布**：团队公告和通知

### 5. 团队管理功能
- **团队设置**：基本信息、隐私设置修改
- **权限管理**：成员权限和角色管理
- **申请审核**：审核加入申请
- **团队分析**：团队数据分析和报告
- **团队备份**：团队数据备份和导出
- **团队解散**：解散团队操作

### 6. 团队切换与导航
- **团队切换**：在多个团队间快速切换
- **团队搜索**：搜索可切换的团队
- **团队收藏**：收藏常用团队
- **最近访问**：显示最近访问的团队
- **团队分组**：按类别分组显示团队

## 📊 现有接口分析

### ✅ 已定义接口
根据现有的`空间接口定义.md`，已经定义了以下接口：

**个人空间API：**
- `PUT /api/v1/users/{userId}/profile` - 更新个人资料
- `GET /api/v1/users/{userId}/profile` - 获取个人信息
- `GET /api/v1/users/{userId}/contents` - 获取个人内容
- `GET /api/v1/users/{userId}/teams` - 获取个人团队
- `GET /api/v1/users/{userId}/learnings` - 获取学习信息

**团队空间API：**
- `POST /api/v1/teams` - 创建团队
- `GET /api/v1/teams/{teamId}` - 获取团队信息
- `GET /api/v1/teams/{teamId}/recommendations` - 获取团队推荐内容
- `GET /api/v1/teams/{teamId}/members` - 获取团队成员

**互动API：**
- `POST /api/v1/teams/{teamId}/applications` - 申请加入团队
- `POST /api/v1/recommendations` - 推荐内容到团队

### ❌ 缺失的关键接口

基于功能分析，以下接口需要补充：

## 🔧 需要新增的接口定义

### 1. 团队列表与搜索接口
```
GET /api/v1/teams - 获取团队列表（支持搜索、筛选、排序）
GET /api/v1/teams/search/suggestions - 获取搜索建议
GET /api/v1/teams/popular-tags - 获取热门标签
```

### 2. 团队收藏与互动接口
```
POST /api/v1/teams/{teamId}/star - 收藏团队
DELETE /api/v1/teams/{teamId}/star - 取消收藏团队
GET /api/v1/users/{userId}/starred-teams - 获取用户收藏的团队
POST /api/v1/teams/{teamId}/share - 分享团队
```

### 3. 团队管理接口
```
PUT /api/v1/teams/{teamId} - 更新团队信息
DELETE /api/v1/teams/{teamId} - 删除团队
PUT /api/v1/teams/{teamId}/settings - 更新团队设置
GET /api/v1/teams/{teamId}/applications - 获取加入申请列表
PUT /api/v1/teams/{teamId}/applications/{applicationId} - 处理加入申请
```

### 4. 团队成员管理接口
```
POST /api/v1/teams/{teamId}/members - 邀请成员
DELETE /api/v1/teams/{teamId}/members/{userId} - 移除成员
PUT /api/v1/teams/{teamId}/members/{userId}/role - 更新成员角色
GET /api/v1/teams/{teamId}/members/online - 获取在线成员
GET /api/v1/teams/{teamId}/contributors - 获取贡献者排行
```

### 5. 个人资料扩展接口
```
POST /api/v1/users/{userId}/avatar - 上传头像
DELETE /api/v1/users/{userId}/avatar - 删除头像
PUT /api/v1/users/{userId}/skills - 更新技能标签
GET /api/v1/users/{userId}/achievements - 获取成就徽章
GET /api/v1/users/{userId}/activities - 获取活动历史
```

### 6. 社交功能接口
```
POST /api/v1/users/{userId}/follow - 关注用户
DELETE /api/v1/users/{userId}/follow - 取消关注
GET /api/v1/users/{userId}/followers - 获取粉丝列表
GET /api/v1/users/{userId}/following - 获取关注列表
GET /api/v1/users/{userId}/notifications - 获取通知列表
```

### 7. 团队协作接口
```
GET /api/v1/teams/{teamId}/activities - 获取团队活动记录
POST /api/v1/teams/{teamId}/discussions - 创建讨论话题
GET /api/v1/teams/{teamId}/discussions - 获取讨论列表
POST /api/v1/teams/{teamId}/announcements - 发布团队公告
GET /api/v1/teams/{teamId}/files - 获取团队文件
POST /api/v1/teams/{teamId}/files - 上传团队文件
```

## 📝 现有接口字段补充

### 个人资料接口字段补充
现有的 `GET /api/v1/users/{userId}/profile` 需要补充以下字段：
```json
{
  "basicInfo": {
    "location": "用户位置",
    "verified": true,
    "createdAt": "注册时间",
    "lastActiveAt": "最后活动时间",
    "skills": ["技能标签数组"],
    "level": "用户等级",
    "contributionScore": "贡献分数"
  },
  "achievements": {
    "level": "用户等级",
    "contributionScore": 1250,
    "consecutiveLearningDays": 21,
    "badges": [
      {
        "id": 1,
        "name": "创作达人",
        "icon": "badge_creator.svg",
        "unlocked": true,
        "unlockedAt": "2025-01-15T10:00:00Z"
      }
    ]
  },
  "social": {
    "isFollowing": false,
    "isFollowedBy": false
  }
}
```

### 团队信息接口字段补充
现有的 `GET /api/v1/teams/{teamId}` 需要补充以下字段：
```json
{
  "basicInfo": {
    "themeColor": "#6366f1",
    "location": "团队位置",
    "verified": true,
    "isActive": true,
    "lastActivityAt": "最后活动时间",
    "tags": ["标签数组"],
    "creatorId": 1,
    "creatorName": "创建者姓名",
    "inviteSetting": "admin_approval"
  },
  "achievements": {
    "discussionsCount": 45,
    "filesCount": 128,
    "contributionScore": 5000
  },
  "userRelation": {
    "isMember": true,
    "isStarred": false,
    "role": "admin",
    "joinedAt": "2025-01-10T10:00:00Z"
  }
}
```

### 团队列表接口参数补充
`GET /api/v1/teams` 需要支持以下查询参数：
```
- search: 搜索关键词
- tags: 标签筛选（支持多个）
- privacy: 隐私筛选（public/private）
- memberCountMin/Max: 成员数范围
- sortBy: 排序字段（created_at/member_count/activity/recommendation）
- sortOrder: 排序方向（asc/desc）
- page/pageSize: 分页参数
- userId: 当前用户ID（用于返回用户相关状态）
```

## 🎯 接口优先级建议

### 高优先级（核心功能）
1. `GET /api/v1/teams` - 团队列表接口（支持搜索筛选）
2. `POST/DELETE /api/v1/teams/{teamId}/star` - 团队收藏功能
3. `GET /api/v1/teams/{teamId}/contributors` - 团队贡献者排行
4. `POST /api/v1/users/{userId}/avatar` - 头像上传功能
5. `PUT /api/v1/users/{userId}/skills` - 技能标签管理

### 中优先级（增强体验）
1. `GET /api/v1/teams/search/suggestions` - 搜索建议
2. `GET /api/v1/teams/{teamId}/activities` - 团队活动记录
3. `GET /api/v1/users/{userId}/achievements` - 成就系统
4. `GET /api/v1/users/{userId}/activities` - 用户活动历史
5. 团队成员管理相关接口

### 低优先级（高级功能）
1. 社交功能接口（关注/粉丝）
2. 团队协作接口（讨论/文件）
3. 通知系统接口
4. 团队分析和报告接口

## 📋 详细接口定义规范

### 1. 团队列表与搜索接口

#### 1.1 获取团队列表
**Endpoint**: `GET /api/v1/teams`
**Description**: 获取团队列表，支持搜索、筛选、排序和分页
**Query Parameters**:
```
- search (string, optional): 搜索关键词
- tags (array, optional): 标签筛选，如 ["AI", "前端"]
- privacy (string, optional): 隐私筛选，enum: ["public", "private", "all"]
- memberCountMin (integer, optional): 最小成员数
- memberCountMax (integer, optional): 最大成员数
- sortBy (string, optional): 排序字段，enum: ["created_at", "member_count", "activity", "recommendation"]
- sortOrder (string, optional): 排序方向，enum: ["asc", "desc"]
- page (integer, optional, default: 1): 页码
- pageSize (integer, optional, default: 20): 每页数量
- userId (integer, optional): 当前用户ID
```

**Response**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "page": 1,
    "pageSize": 20,
    "total": 156,
    "list": [
      {
        "teamId": 1,
        "name": "AI创新探索小组",
        "description": "探索前沿AI技术和落地场景",
        "avatar": "https://example.com/team1.png",
        "isPublic": true,
        "membersCount": 25,
        "contentCount": 48,
        "discussionsCount": 12,
        "tags": ["AI", "机器学习", "深度学习"],
        "createdAt": "2025-01-15T10:00:00Z",
        "lastActivityAt": "2025-07-20T15:30:00Z",
        "creatorName": "张三",
        "themeColor": "#6366f1",
        "verified": true,
        "userRelation": {
          "isMember": false,
          "isStarred": true,
          "hasApplied": false
        }
      }
    ],
    "aggregations": {
      "totalPublic": 120,
      "totalPrivate": 36,
      "popularTags": [
        { "name": "AI", "count": 45 },
        { "name": "前端", "count": 32 }
      ]
    }
  }
}
```

#### 1.2 获取搜索建议
**Endpoint**: `GET /api/v1/teams/search/suggestions`
**Query Parameters**:
```
- q (string, required): 搜索关键词
- limit (integer, optional, default: 10): 建议数量
```

**Response**:
```json
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "text": "AI创新探索小组",
      "type": "team",
      "icon": "fas fa-users",
      "matchType": "name"
    },
    {
      "id": 2,
      "text": "人工智能",
      "type": "tag",
      "icon": "fas fa-tag",
      "matchType": "tag"
    }
  ]
}
```

### 2. 团队收藏接口

#### 2.1 收藏团队
**Endpoint**: `POST /api/v1/teams/{teamId}/star`
**Response**:
```json
{
  "code": 200,
  "message": "收藏成功",
  "data": {
    "isStarred": true,
    "starredAt": "2025-07-21T10:00:00Z"
  }
}
```

#### 2.2 取消收藏团队
**Endpoint**: `DELETE /api/v1/teams/{teamId}/star`
**Response**:
```json
{
  "code": 200,
  "message": "取消收藏成功",
  "data": {
    "isStarred": false
  }
}
```

#### 2.3 获取用户收藏的团队
**Endpoint**: `GET /api/v1/users/{userId}/starred-teams`
**Response**:
```json
{
  "code": 200,
  "data": [
    {
      "teamId": 1,
      "name": "AI创新探索小组",
      "avatar": "https://example.com/team1.png",
      "membersCount": 25,
      "starredAt": "2025-07-20T10:00:00Z"
    }
  ]
}
```

### 3. 团队管理接口

#### 3.1 更新团队信息
**Endpoint**: `PUT /api/v1/teams/{teamId}`
**Request Body**:
```json
{
  "name": "新团队名称",
  "description": "新的团队描述",
  "avatarUrl": "https://example.com/new_avatar.png",
  "privacy": "1",
  "tags": ["AI", "创新"],
  "themeColor": "#8b5cf6",
  "location": "北京"
}
```

#### 3.2 获取加入申请列表
**Endpoint**: `GET /api/v1/teams/{teamId}/applications`
**Query Parameters**:
```
- status (string, optional): 申请状态，enum: ["pending", "approved", "rejected"]
- page (integer, optional): 页码
- pageSize (integer, optional): 每页数量
```

**Response**:
```json
{
  "code": 200,
  "data": {
    "list": [
      {
        "applicationId": 1,
        "userId": 123,
        "userName": "李四",
        "userAvatar": "https://example.com/user.png",
        "reason": "我对AI技术很感兴趣...",
        "status": "pending",
        "appliedAt": "2025-07-20T10:00:00Z"
      }
    ]
  }
}
```

#### 3.3 处理加入申请
**Endpoint**: `PUT /api/v1/teams/{teamId}/applications/{applicationId}`
**Request Body**:
```json
{
  "action": "approve",
  "message": "欢迎加入团队！"
}
```

### 4. 个人资料扩展接口

#### 4.1 上传头像
**Endpoint**: `POST /api/v1/users/{userId}/avatar`
**Content-Type**: `multipart/form-data`
**Request Body**:
```
file: 头像文件
```

**Response**:
```json
{
  "code": 200,
  "message": "头像上传成功",
  "data": {
    "avatarUrl": "https://example.com/avatars/user123.png"
  }
}
```

#### 4.2 更新技能标签
**Endpoint**: `PUT /api/v1/users/{userId}/skills`
**Request Body**:
```json
{
  "skills": [
    {
      "name": "JavaScript",
      "level": "expert"
    },
    {
      "name": "Vue.js",
      "level": "advanced"
    }
  ]
}
```

#### 4.3 获取成就徽章
**Endpoint**: `GET /api/v1/users/{userId}/achievements`
**Response**:
```json
{
  "code": 200,
  "data": {
    "level": 15,
    "contributionScore": 2580,
    "badges": [
      {
        "id": 1,
        "name": "创作达人",
        "description": "发布超过50篇内容",
        "icon": "fas fa-pen",
        "unlocked": true,
        "unlockedAt": "2025-06-15T10:00:00Z",
        "rarity": "rare"
      }
    ],
    "nextBadge": {
      "name": "社区之星",
      "description": "获得1000个点赞",
      "progress": 750,
      "target": 1000
    }
  }
}
```

### 5. 团队协作接口

#### 5.1 获取团队活动记录
**Endpoint**: `GET /api/v1/teams/{teamId}/activities`
**Query Parameters**:
```
- type (string, optional): 活动类型筛选
- page (integer, optional): 页码
- pageSize (integer, optional): 每页数量
```

**Response**:
```json
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": 1,
        "type": "member_joined",
        "userId": 123,
        "userName": "张三",
        "userAvatar": "https://example.com/user.png",
        "description": "张三 加入了团队",
        "createdAt": "2025-07-20T10:00:00Z",
        "metadata": {
          "role": "member"
        }
      }
    ]
  }
}
```

#### 5.2 获取贡献者排行
**Endpoint**: `GET /api/v1/teams/{teamId}/contributors`
**Query Parameters**:
```
- period (string, optional): 统计周期，enum: ["week", "month", "quarter", "year", "all"]
- limit (integer, optional, default: 10): 返回数量
```

**Response**:
```json
{
  "code": 200,
  "data": [
    {
      "userId": 123,
      "userName": "张三",
      "userAvatar": "https://example.com/user.png",
      "contributionScore": 1250,
      "rank": 1,
      "contributions": {
        "contentRecommended": 15,
        "discussionsStarted": 8,
        "commentsPosted": 45
      }
    }
  ]
}
```

## 🔄 接口版本控制建议

1. **版本策略**：使用URL路径版本控制（如 `/api/v1/`）
2. **向后兼容**：新增字段保持向后兼容，废弃字段逐步迁移
3. **错误处理**：统一错误响应格式
4. **限流策略**：对搜索和列表接口实施适当限流
5. **缓存策略**：对热门数据实施缓存机制

## 📈 性能优化建议

1. **分页优化**：大列表接口必须支持分页
2. **字段选择**：支持字段选择参数减少数据传输
3. **批量操作**：支持批量收藏、批量邀请等操作
4. **实时更新**：关键数据支持WebSocket实时推送
5. **CDN加速**：头像、文件等静态资源使用CDN

这个完整的功能清单和接口定义为后台开发提供了详细的技术规范，确保前后端功能的完整对接和优秀的用户体验。
