// API 配置
const API_BASE_URL = 'http://localhost:8000'

export const API_CONFIG = {
  BASE_URL: API_BASE_URL,
  ENDPOINTS: {
    AUTH: {
      LOGIN: `${API_BASE_URL}/api/auth/login`,
      REGISTER: `${API_BASE_URL}/api/auth/register`,
      LOGOUT: `${API_BASE_URL}/api/auth/logout`,
      USER: `${API_BASE_URL}/api/auth/user`,
      REFRESH: `${API_BASE_URL}/api/auth/refresh`,
      OAUTH_GOOGLE: `${API_BASE_URL}/api/auth/oauth/google`,
      OAUTH_GITHUB: `${API_BASE_URL}/api/auth/oauth/github`,
      OAUTH_CALLBACK: (provider) => `${API_BASE_URL}/api/auth/oauth/${provider}/callback`
    }
  }
}

// HTTP 请求工具类
export class ApiClient {
  static async request(url, options = {}) {
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    }

    const token = localStorage.getItem('token')
    if (token) {
      defaultOptions.headers.Authorization = `Bearer ${token}`
    }

    try {
      const response = await fetch(url, { ...defaultOptions, ...options })

      // 无论响应状态如何，都尝试解析JSON
      const data = await response.json().catch(() => ({
        code: response.status,
        message: '请求失败'
      }))

      // 如果响应不成功，但有JSON数据，返回JSON数据（让业务逻辑处理）
      if (!response.ok) {
        if (response.status === 0 || !response.status) {
          throw new Error('Network Error')
        }
        // 返回错误数据而不是抛出异常，让业务逻辑处理
        return data
      }

      return data
    } catch (error) {
      if (error instanceof TypeError && error.message === 'Failed to fetch') {
        throw new Error('Network Error')
      }
      throw error
    }
  }

  static async get(url, options = {}) {
    return this.request(url, { ...options, method: 'GET' })
  }

  static async post(url, data, options = {}) {
    return this.request(url, {
      ...options,
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  static async put(url, data, options = {}) {
    return this.request(url, {
      ...options,
      method: 'PUT',
      body: JSON.stringify(data)
    })
  }

  static async delete(url, options = {}) {
    return this.request(url, { ...options, method: 'DELETE' })
  }
}