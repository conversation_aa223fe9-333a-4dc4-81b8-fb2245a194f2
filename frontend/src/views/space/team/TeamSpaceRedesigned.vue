<template>
  <Layout>
    <div class="team-space-redesigned">
      <div class="container">
        <!-- 🎯 页面头部 - 参考AI工具箱设计 -->
        <div class="page-header">
          <div class="header-content">
            <h1 class="page-title">
              <i class="fas fa-users"></i>
              团队空间
            </h1>
            <p class="page-subtitle">发现优秀团队，开启协作之旅，共创无限可能</p>
          </div>
          
          <!-- 快速操作按钮 -->
          <div class="header-actions">
            <button class="create-team-btn" @click="showCreateModal = true">
              <i class="fas fa-plus"></i>
              创建团队
            </button>
          </div>
        </div>

        <!-- 🔍 搜索和筛选区域 -->
        <div class="search-section">
          <!-- 主搜索框 -->
          <div class="hero-search">
            <div class="search-input-wrapper">
              <i class="fas fa-search search-icon"></i>
              <input 
                type="text" 
                placeholder="搜索团队名称、标签或描述..."
                v-model="searchQuery"
                @input="handleSearch"
                @focus="showSearchSuggestions = true"
                @blur="hideSearchSuggestions"
              >
              <button v-if="searchQuery" class="clear-search" @click="clearSearch">
                <i class="fas fa-times"></i>
              </button>
            </div>
            
            <!-- 搜索建议 -->
            <div v-if="showSearchSuggestions && searchSuggestions.length" class="search-suggestions">
              <div class="suggestions-header">
                <i class="fas fa-lightbulb"></i>
                搜索建议
              </div>
              <div
                v-for="suggestion in searchSuggestions"
                :key="suggestion.id"
                class="suggestion-item"
                @mousedown="applySuggestion(suggestion)"
              >
                <i :class="suggestion.icon"></i>
                <span>{{ suggestion.text }}</span>
                <span class="suggestion-type">{{ suggestion.type }}</span>
              </div>
            </div>
          </div>

          <!-- Tab筛选器 -->
          <div class="filter-tabs">
            <button 
              v-for="tab in filterTabs" 
              :key="tab.key"
              class="tab-btn"
              :class="{ active: activeTab === tab.key }"
              @click="setActiveTab(tab.key)"
            >
              <i :class="tab.icon"></i>
              {{ tab.label }}
              <span v-if="tab.count !== undefined" class="tab-count">{{ tab.count }}</span>
            </button>
          </div>
        </div>

        <!-- 🏷️ 标签筛选和排序 -->
        <div class="filter-bar">
          <div class="filter-left">
            <!-- 标签筛选 -->
            <div class="tag-filters">
              <span class="filter-label">标签筛选:</span>
              <div class="tag-list">
                <button
                  v-for="tag in popularTags"
                  :key="tag.name"
                  class="tag-btn"
                  :class="{ active: selectedTags.includes(tag.name) }"
                  @click="toggleTag(tag.name)"
                >
                  {{ tag.name }}
                  <span class="tag-count">{{ tag.count }}</span>
                </button>
              </div>
              <button v-if="!showAllTags && allTags.length > 8" class="show-more-tags" @click="showAllTags = true">
                <i class="fas fa-chevron-down"></i>
                更多标签
              </button>
            </div>
          </div>

          <div class="filter-right">
            <!-- 排序选择 -->
            <div class="sort-controls">
              <span class="sort-label">排序:</span>
              <select v-model="sortBy" @change="handleSortChange" class="sort-select">
                <option value="members">成员数量</option>
                <option value="articles">文章数量</option>
                <option value="likes">点赞收藏</option>
                <option value="activity">最近活跃</option>
                <option value="created">创建时间</option>
              </select>
            </div>

            <!-- 视图切换 -->
            <div class="view-toggle">
              <button
                class="view-btn"
                :class="{ active: viewMode === 'grid' }"
                @click="setViewMode('grid')"
                title="网格视图"
              >
                <i class="fas fa-th"></i>
              </button>
              <button
                class="view-btn"
                :class="{ active: viewMode === 'list' }"
                @click="setViewMode('list')"
                title="列表视图"
              >
                <i class="fas fa-list"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- 📊 统计信息 -->
        <div class="stats-bar">
          <div class="stats-info">
            <span class="stats-text">
              找到 <strong>{{ filteredTeams.length }}</strong> 个团队
              <span v-if="searchQuery || selectedTags.length || activeTab !== 'all'">
                (已筛选)
              </span>
            </span>
          </div>
          <div class="stats-actions">
            <button v-if="hasActiveFilters" class="clear-filters" @click="clearAllFilters">
              <i class="fas fa-times"></i>
              清除筛选
            </button>
          </div>
        </div>

        <!-- 🎯 团队展示区域 -->
        <div class="teams-section">
          <!-- 加载状态 -->
          <div v-if="loading" class="loading-state">
            <div class="loading-grid" :class="viewMode">
              <div v-for="i in 8" :key="i" class="loading-card"></div>
            </div>
          </div>

          <!-- 团队网格/列表 -->
          <div v-else-if="filteredTeams.length" class="teams-container" :class="viewMode">
            <TeamCard
              v-for="team in paginatedTeams"
              :key="team.id"
              :team="team"
              :view-mode="viewMode"
              @join="handleJoinTeam"
              @star="handleStarTeam"
              @enter="handleEnterTeam"
              @preview="handlePreviewTeam"
            />
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-state">
            <div class="empty-icon">
              <i class="fas fa-search"></i>
            </div>
            <h3 class="empty-title">{{ getEmptyTitle() }}</h3>
            <p class="empty-description">{{ getEmptyDescription() }}</p>
            <div class="empty-actions">
              <button v-if="hasActiveFilters" class="btn-secondary" @click="clearAllFilters">
                清除筛选条件
              </button>
              <button class="btn-primary" @click="showCreateModal = true">
                <i class="fas fa-plus"></i>
                创建新团队
              </button>
            </div>
          </div>

          <!-- 分页控制 -->
          <div v-if="filteredTeams.length > pageSize" class="pagination">
            <button
              class="page-btn"
              :disabled="currentPage === 1"
              @click="setPage(currentPage - 1)"
            >
              <i class="fas fa-chevron-left"></i>
              上一页
            </button>
            
            <div class="page-numbers">
              <button
                v-for="page in visiblePages"
                :key="page"
                class="page-number"
                :class="{ active: page === currentPage }"
                @click="setPage(page)"
              >
                {{ page }}
              </button>
            </div>
            
            <button
              class="page-btn"
              :disabled="currentPage === totalPages"
              @click="setPage(currentPage + 1)"
            >
              下一页
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 🎨 创建团队模态框 -->
      <CreateTeamModal
        :visible="showCreateModal"
        @close="showCreateModal = false"
        @created="handleTeamCreated"
      />

      <!-- 🔍 团队预览模态框 -->
      <TeamPreviewModal
        :visible="!!previewTeam"
        :team="previewTeam"
        @close="closePreview"
        @join="handleJoinTeam"
        @star="handleStarTeam"
      />

      <!-- 📝 加入团队模态框 -->
      <JoinTeamModal
        :visible="!!joinModalTeam"
        :team="joinModalTeam"
        @close="closeJoinModal"
        @joined="handleTeamJoined"
      />
    </div>
  </Layout>
</template>

<script>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '@/stores/toast'
import { useUserStore } from '@/stores/user'
import teamService from '@/services/teamService'
import userService from '@/services/userService'

// 导入组件
import Layout from '@/components/Layout.vue'
import TeamCard from './components/TeamCardRedesigned.vue'
import CreateTeamModal from './components/CreateTeamModal.vue'
import TeamPreviewModal from './components/TeamPreviewModal.vue'
import JoinTeamModal from './components/JoinTeamModal.vue'

export default {
  name: 'TeamSpaceRedesigned',
  components: {
    Layout,
    TeamCard,
    CreateTeamModal,
    TeamPreviewModal,
    JoinTeamModal
  },
  setup() {
    const router = useRouter()
    const toastStore = useToastStore()
    const userStore = useUserStore()

    // ===== 响应式数据 =====
    const loading = ref(false)
    const error = ref(null)
    
    // 团队数据
    const allTeams = ref([])
    const myTeams = ref([])
    
    // 搜索和筛选
    const searchQuery = ref('')
    const showSearchSuggestions = ref(false)
    const searchSuggestions = ref([])
    const activeTab = ref('all')
    const selectedTags = ref([])
    const sortBy = ref('members')
    const viewMode = ref('grid')
    const showAllTags = ref(false)
    
    // 分页
    const currentPage = ref(1)
    const pageSize = ref(12)
    
    // 模态框状态
    const showCreateModal = ref(false)
    const previewTeam = ref(null)
    const joinModalTeam = ref(null)

    // ===== 计算属性 =====
    
    // Tab配置
    const filterTabs = computed(() => [
      { key: 'all', label: '全部团队', icon: 'fas fa-globe', count: allTeams.value.length },
      { key: 'my', label: '我的团队', icon: 'fas fa-user-friends', count: myTeams.value.length },
      { key: 'joined', label: '已加入', icon: 'fas fa-check-circle', count: getJoinedTeamsCount() },
      { key: 'created', label: '我创建的', icon: 'fas fa-crown', count: getCreatedTeamsCount() },
      { key: 'public', label: '公开团队', icon: 'fas fa-unlock', count: getPublicTeamsCount() },
      { key: 'starred', label: '收藏团队', icon: 'fas fa-star', count: getStarredTeamsCount() }
    ])

    // 热门标签
    const popularTags = computed(() => {
      const tagCounts = {}
      allTeams.value.forEach(team => {
        team.tags?.forEach(tag => {
          tagCounts[tag] = (tagCounts[tag] || 0) + 1
        })
      })

      return Object.entries(tagCounts)
        .map(([name, count]) => ({ name, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, showAllTags.value ? undefined : 8)
    })

    const allTags = computed(() => {
      const tagCounts = {}
      allTeams.value.forEach(team => {
        team.tags?.forEach(tag => {
          tagCounts[tag] = (tagCounts[tag] || 0) + 1
        })
      })

      return Object.entries(tagCounts)
        .map(([name, count]) => ({ name, count }))
        .sort((a, b) => b.count - a.count)
    })

    // 筛选后的团队
    const filteredTeams = computed(() => {
      let teams = [...allTeams.value]

      // Tab筛选
      switch (activeTab.value) {
        case 'my':
          teams = myTeams.value
          break
        case 'joined':
          teams = teams.filter(team => team.isMember)
          break
        case 'created':
          teams = teams.filter(team => team.isCreatedByMe)
          break
        case 'public':
          teams = teams.filter(team => team.isPublic)
          break
        case 'starred':
          teams = teams.filter(team => team.isStarred)
          break
      }

      // 搜索筛选
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        teams = teams.filter(team =>
          team.name.toLowerCase().includes(query) ||
          team.description?.toLowerCase().includes(query) ||
          team.tags?.some(tag => tag.toLowerCase().includes(query))
        )
      }

      // 标签筛选
      if (selectedTags.value.length > 0) {
        teams = teams.filter(team =>
          selectedTags.value.some(tag => team.tags?.includes(tag))
        )
      }

      // 排序
      return sortTeams(teams)
    })

    // 分页后的团队
    const paginatedTeams = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return filteredTeams.value.slice(start, end)
    })

    const totalPages = computed(() => {
      return Math.ceil(filteredTeams.value.length / pageSize.value)
    })

    const visiblePages = computed(() => {
      const pages = []
      const total = totalPages.value
      const current = currentPage.value

      if (total <= 7) {
        for (let i = 1; i <= total; i++) {
          pages.push(i)
        }
      } else {
        if (current <= 4) {
          for (let i = 1; i <= 5; i++) pages.push(i)
          pages.push('...')
          pages.push(total)
        } else if (current >= total - 3) {
          pages.push(1)
          pages.push('...')
          for (let i = total - 4; i <= total; i++) pages.push(i)
        } else {
          pages.push(1)
          pages.push('...')
          for (let i = current - 1; i <= current + 1; i++) pages.push(i)
          pages.push('...')
          pages.push(total)
        }
      }

      return pages
    })

    const hasActiveFilters = computed(() => {
      return searchQuery.value || selectedTags.value.length > 0 || activeTab.value !== 'all'
    })

    // ===== 辅助方法 =====
    const getJoinedTeamsCount = () => {
      return allTeams.value.filter(team => team.isMember).length
    }

    const getCreatedTeamsCount = () => {
      return allTeams.value.filter(team => team.isCreatedByMe).length
    }

    const getPublicTeamsCount = () => {
      return allTeams.value.filter(team => team.isPublic).length
    }

    const getStarredTeamsCount = () => {
      return allTeams.value.filter(team => team.isStarred).length
    }

    const sortTeams = (teams) => {
      return [...teams].sort((a, b) => {
        switch (sortBy.value) {
          case 'members':
            return (b.membersCount || 0) - (a.membersCount || 0)
          case 'articles':
            return (b.articlesCount || 0) - (a.articlesCount || 0)
          case 'likes':
            return (b.likesCount || 0) - (a.likesCount || 0)
          case 'activity':
            return new Date(b.lastActivityAt || b.createdAt) - new Date(a.lastActivityAt || a.createdAt)
          case 'created':
            return new Date(b.createdAt) - new Date(a.createdAt)
          default:
            return 0
        }
      })
    }

    // ===== 数据加载 =====
    const loadTeamsData = async () => {
      try {
        loading.value = true
        error.value = null

        const currentUserId = userStore.user?.id || 1

        // 并行获取所有团队和用户团队
        const [allTeamsResponse, userTeamsResponse] = await Promise.all([
          teamService.getAllTeams({ pageSize: 100 }), // 获取更多数据用于前端筛选
          userService.getUserTeams(currentUserId)
        ])

        // 处理团队数据
        allTeams.value = processTeamsData(allTeamsResponse?.list || [])
        myTeams.value = userTeamsResponse?.list || []

        console.log('团队数据加载完成:', {
          allTeams: allTeams.value.length,
          myTeams: myTeams.value.length
        })

      } catch (err) {
        console.error('加载团队数据失败:', err)
        error.value = err.message || '加载失败'
        toastStore.error('加载团队数据失败')
      } finally {
        loading.value = false
      }
    }

    const processTeamsData = (teams) => {
      const currentUserId = userStore.user?.id || 1

      return teams.map(team => {
        const achievements = team.achievements || {}
        const members = team.members || []

        // 判断用户关系
        const userMember = members.find(m => m.userId === currentUserId)
        const isMember = !!userMember
        const isCreatedByMe = team.creatorId === currentUserId || team.createdBy === currentUserId.toString()

        return {
          id: team.teamId || team.id,
          name: team.name || '未命名团队',
          description: team.description || '',
          avatar: team.avatarUrl,

          // 团队属性
          isPublic: team.privacy === '1' || team.privacy === 1,
          isMember,
          isCreatedByMe,
          isStarred: false, // TODO: 从用户数据获取

          // 统计数据
          membersCount: team.memberCount || members.length || 0,
          articlesCount: achievements.articlesRecommended || 0,
          likesCount: achievements.totalLikes || 0,
          viewsCount: achievements.totalViews || 0,

          // 标签和时间
          tags: team.tags || [],
          createdAt: team.createdAt,
          lastActivityAt: team.updatedAt || team.createdAt,

          // 成员信息
          members: members,
          creatorId: team.creatorId || team.createdBy
        }
      })
    }

    return {
      // 响应式数据
      loading,
      error,
      allTeams,
      myTeams,
      searchQuery,
      showSearchSuggestions,
      searchSuggestions,
      activeTab,
      selectedTags,
      sortBy,
      viewMode,
      showAllTags,
      currentPage,
      pageSize,
      showCreateModal,
      previewTeam,
      joinModalTeam,

      // 计算属性
      filterTabs,
      popularTags,
      allTags,
      filteredTeams,
      paginatedTeams,
      totalPages,
      visiblePages,
      hasActiveFilters,

      // 方法
      loadTeamsData,
      getJoinedTeamsCount,
      getCreatedTeamsCount,
      getPublicTeamsCount,
      getStarredTeamsCount

    // ===== 辅助函数 =====

    // 防抖函数
    function debounce(func, wait) {
      let timeout
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout)
          func(...args)
        }
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
      }
    }

    const generateSearchSuggestions = () => {
      if (!searchQuery.value || searchQuery.value.length < 2) {
        searchSuggestions.value = []
        return
      }

      const query = searchQuery.value.toLowerCase()
      const suggestions = []

      // 团队名称建议
      allTeams.value.forEach(team => {
        if (team.name.toLowerCase().includes(query)) {
          suggestions.push({
            id: `team-${team.id}`,
            text: team.name,
            type: '团队',
            icon: 'fas fa-users'
          })
        }
      })

      // 标签建议
      allTags.value.forEach(tag => {
        if (tag.name.toLowerCase().includes(query)) {
          suggestions.push({
            id: `tag-${tag.name}`,
            text: tag.name,
            type: '标签',
            icon: 'fas fa-tag'
          })
        }
      })

      searchSuggestions.value = suggestions.slice(0, 6)
    }

    // ===== 事件处理方法 =====

    // 搜索相关
    const handleSearch = debounce(() => {
      generateSearchSuggestions()
      currentPage.value = 1
    }, 300)

    const clearSearch = () => {
      searchQuery.value = ''
      searchSuggestions.value = []
      showSearchSuggestions.value = false
      currentPage.value = 1
    }

    const hideSearchSuggestions = () => {
      setTimeout(() => {
        showSearchSuggestions.value = false
      }, 200)
    }

    const applySuggestion = (suggestion) => {
      searchQuery.value = suggestion.text
      showSearchSuggestions.value = false
      currentPage.value = 1
    }

    // Tab和筛选
    const setActiveTab = (tab) => {
      activeTab.value = tab
      currentPage.value = 1
    }

    const toggleTag = (tag) => {
      const index = selectedTags.value.indexOf(tag)
      if (index > -1) {
        selectedTags.value.splice(index, 1)
      } else {
        selectedTags.value.push(tag)
      }
      currentPage.value = 1
    }

    const handleSortChange = () => {
      currentPage.value = 1
    }

    const setViewMode = (mode) => {
      viewMode.value = mode
    }

    const clearAllFilters = () => {
      searchQuery.value = ''
      selectedTags.value = []
      activeTab.value = 'all'
      currentPage.value = 1
    }

    // 分页
    const setPage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page
        // 滚动到顶部
        nextTick(() => {
          document.querySelector('.teams-section')?.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          })
        })
      }
    }

    // 团队操作
    const handleJoinTeam = (team) => {
      joinModalTeam.value = team
    }

    const handleStarTeam = async (team) => {
      try {
        if (team.isStarred) {
          await teamService.unstarTeam(team.id)
          team.isStarred = false
          toastStore.success('已取消收藏')
        } else {
          await teamService.starTeam(team.id)
          team.isStarred = true
          toastStore.success('已收藏团队')
        }
      } catch (error) {
        toastStore.error('操作失败: ' + (error.message || '未知错误'))
      }
    }

    const handleEnterTeam = (team) => {
      router.push(`/space/team/${team.id}`)
    }

    const handlePreviewTeam = (team) => {
      previewTeam.value = team
    }

    const closePreview = () => {
      previewTeam.value = null
    }

    const closeJoinModal = () => {
      joinModalTeam.value = null
    }

    const handleTeamCreated = (team) => {
      showCreateModal.value = false
      allTeams.value.unshift(team)
      myTeams.value.unshift(team)
      toastStore.success('团队创建成功！')
    }

    const handleTeamJoined = (team) => {
      closeJoinModal()
      // 更新团队状态
      const teamIndex = allTeams.value.findIndex(t => t.id === team.id)
      if (teamIndex > -1) {
        allTeams.value[teamIndex].isMember = true
        allTeams.value[teamIndex].membersCount += 1
      }
      toastStore.success('成功加入团队！')
    }

    // 空状态文案
    const getEmptyTitle = () => {
      if (searchQuery.value) return '未找到匹配的团队'
      if (selectedTags.value.length) return '该标签下暂无团队'
      if (activeTab.value === 'my') return '您还没有加入任何团队'
      if (activeTab.value === 'starred') return '您还没有收藏任何团队'
      return '暂无团队'
    }

    const getEmptyDescription = () => {
      if (searchQuery.value) return '尝试使用其他关键词搜索，或者创建一个新团队'
      if (selectedTags.value.length) return '尝试选择其他标签，或者创建一个新团队'
      if (activeTab.value === 'my') return '加入感兴趣的团队，开始您的协作之旅'
      if (activeTab.value === 'starred') return '收藏感兴趣的团队，方便快速访问'
      return '创建第一个团队，开启协作之旅'
    }

    // 生命周期
    onMounted(() => {
      loadTeamsData()
    })

    // 监听器
    watch(() => userStore.user, (newUser) => {
      if (newUser) {
        loadTeamsData()
      }
    })

    return {
      // 响应式数据
      loading,
      error,
      allTeams,
      myTeams,
      searchQuery,
      showSearchSuggestions,
      searchSuggestions,
      activeTab,
      selectedTags,
      sortBy,
      viewMode,
      showAllTags,
      currentPage,
      pageSize,
      showCreateModal,
      previewTeam,
      joinModalTeam,

      // 计算属性
      filterTabs,
      popularTags,
      allTags,
      filteredTeams,
      paginatedTeams,
      totalPages,
      visiblePages,
      hasActiveFilters,

      // 方法
      loadTeamsData,
      getJoinedTeamsCount,
      getCreatedTeamsCount,
      getPublicTeamsCount,
      getStarredTeamsCount,
      handleSearch,
      clearSearch,
      hideSearchSuggestions,
      applySuggestion,
      generateSearchSuggestions,
      setActiveTab,
      toggleTag,
      handleSortChange,
      setViewMode,
      clearAllFilters,
      setPage,
      handleJoinTeam,
      handleStarTeam,
      handleEnterTeam,
      handlePreviewTeam,
      closePreview,
      closeJoinModal,
      handleTeamCreated,
      handleTeamJoined,
      getEmptyTitle,
      getEmptyDescription
    }
  }
}
</script>

<style scoped>
/* 🎨 团队空间重新设计 - 参考AI工具箱风格 */

/* ===== 基础布局 ===== */
.team-space-redesigned {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

/* ===== 页面头部 ===== */
.page-header {
  padding: 40px 0 30px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 30px;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 36px;
  font-weight: 800;
  color: #1a202c;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 16px;
}

.page-title i {
  color: #6366f1;
  font-size: 32px;
}

.page-subtitle {
  font-size: 18px;
  color: #64748b;
  margin: 0;
  line-height: 1.6;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.create-team-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.create-team-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
}

/* ===== 搜索区域 ===== */
.search-section {
  margin-bottom: 30px;
}

.hero-search {
  position: relative;
  max-width: 600px;
  margin: 0 auto 30px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 20px;
  color: #9ca3af;
  font-size: 18px;
  z-index: 2;
}

.hero-search input {
  width: 100%;
  padding: 18px 20px 18px 56px;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  font-size: 16px;
  background: white;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.hero-search input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
}

.clear-search {
  position: absolute;
  right: 16px;
  width: 28px;
  height: 28px;
  border: none;
  background: rgba(107, 114, 128, 0.1);
  border-radius: 50%;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.clear-search:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* ===== 搜索建议 ===== */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
  z-index: 100;
  margin-top: 8px;
  overflow: hidden;
  backdrop-filter: blur(20px);
}

.suggestions-header {
  padding: 12px 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.suggestion-item {
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f1f5f9;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover {
  background: #f8fafc;
}

.suggestion-item i {
  color: #6366f1;
  font-size: 14px;
  width: 16px;
}

.suggestion-type {
  margin-left: auto;
  font-size: 11px;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: #f1f5f9;
  padding: 2px 8px;
  border-radius: 8px;
}

/* ===== Tab筛选器 ===== */
.filter-tabs {
  display: flex;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

.tab-btn {
  padding: 12px 20px;
  border: 2px solid #e2e8f0;
  background: white;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.tab-btn:hover {
  border-color: #6366f1;
  color: #6366f1;
  transform: translateY(-1px);
}

.tab-btn.active {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.tab-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 700;
  min-width: 20px;
  text-align: center;
}

.tab-btn:not(.active) .tab-count {
  background: #f1f5f9;
  color: #64748b;
}

/* ===== 筛选栏 ===== */
.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-left {
  flex: 1;
}

.tag-filters {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.filter-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-btn {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 20px;
  font-size: 13px;
  color: #4b5563;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.tag-btn:hover {
  border-color: #6366f1;
  color: #6366f1;
}

.tag-btn.active {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border-color: transparent;
}

.tag-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 1px 6px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 600;
}

.tag-btn:not(.active) .tag-count {
  background: #f3f4f6;
  color: #6b7280;
}

.show-more-tags {
  padding: 6px 12px;
  border: 1px dashed #d1d5db;
  background: transparent;
  border-radius: 20px;
  font-size: 13px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.show-more-tags:hover {
  border-color: #6366f1;
  color: #6366f1;
}

.filter-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sort-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.sort-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  color: #4b5563;
  font-size: 14px;
  cursor: pointer;
  outline: none;
  transition: all 0.2s ease;
}

.sort-select:focus {
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.view-toggle {
  display: flex;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 2px;
}

.view-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: transparent;
  border-radius: 6px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.view-btn:hover {
  color: #6366f1;
}

.view-btn.active {
  background: white;
  color: #6366f1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* ===== 统计栏 ===== */
.stats-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.stats-text {
  font-size: 14px;
  color: #64748b;
}

.stats-text strong {
  color: #1a202c;
  font-weight: 700;
}

.clear-filters {
  padding: 8px 16px;
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.clear-filters:hover {
  background: rgba(239, 68, 68, 0.15);
}

/* ===== 团队展示区域 ===== */
.teams-section {
  margin-bottom: 40px;
}

/* 加载状态 */
.loading-state {
  margin: 40px 0;
}

.loading-grid {
  display: grid;
  gap: 24px;
}

.loading-grid.grid {
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
}

.loading-grid.list {
  grid-template-columns: 1fr;
  gap: 16px;
}

.loading-card {
  height: 280px;
  background: linear-gradient(90deg,
    rgba(226, 232, 240, 0.3) 0%,
    rgba(226, 232, 240, 0.5) 50%,
    rgba(226, 232, 240, 0.3) 100%);
  border-radius: 16px;
  animation: loading-shimmer 1.5s ease-in-out infinite;
}

.loading-grid.list .loading-card {
  height: 120px;
}

@keyframes loading-shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

/* 团队容器 */
.teams-container {
  display: grid;
  gap: 24px;
}

.teams-container.grid {
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
}

.teams-container.list {
  grid-template-columns: 1fr;
  gap: 16px;
}

/* ===== 空状态 ===== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.empty-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: #9ca3af;
  margin-bottom: 24px;
}

.empty-title {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 12px 0;
}

.empty-description {
  font-size: 16px;
  color: #64748b;
  margin: 0 0 32px 0;
  line-height: 1.6;
  max-width: 400px;
}

.empty-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.btn-secondary {
  padding: 12px 24px;
  background: white;
  color: #64748b;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  border-color: #6366f1;
  color: #6366f1;
  transform: translateY(-1px);
}

.btn-primary {
  padding: 12px 24px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
}

/* ===== 分页 ===== */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 40px;
  padding: 20px;
}

.page-btn {
  padding: 10px 16px;
  background: white;
  color: #64748b;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.page-btn:hover:not(:disabled) {
  border-color: #6366f1;
  color: #6366f1;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-number {
  width: 40px;
  height: 40px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #64748b;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-number:hover {
  border-color: #6366f1;
  color: #6366f1;
}

.page-number.active {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border-color: transparent;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

/* ===== 响应式设计 ===== */
@media (max-width: 1200px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
  }

  .filter-bar {
    flex-direction: column;
    gap: 16px;
  }

  .filter-right {
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .page-header {
    padding: 20px 0;
  }

  .page-title {
    font-size: 28px;
  }

  .page-subtitle {
    font-size: 16px;
  }

  .filter-tabs {
    gap: 6px;
  }

  .tab-btn {
    padding: 10px 16px;
    font-size: 13px;
  }

  .teams-container.grid {
    grid-template-columns: 1fr;
  }

  .filter-right {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .sort-controls {
    justify-content: space-between;
  }

  .pagination {
    flex-wrap: wrap;
    gap: 6px;
  }

  .page-btn {
    padding: 8px 12px;
    font-size: 13px;
  }

  .page-number {
    width: 36px;
    height: 36px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 16px 0;
  }

  .page-title {
    font-size: 24px;
  }

  .hero-search input {
    padding: 16px 16px 16px 48px;
    font-size: 15px;
  }

  .filter-tabs {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: 8px;
  }

  .tab-btn {
    flex-shrink: 0;
    padding: 8px 12px;
    font-size: 12px;
  }

  .empty-state {
    padding: 40px 16px;
  }

  .empty-title {
    font-size: 20px;
  }

  .empty-description {
    font-size: 14px;
  }

  .empty-actions {
    flex-direction: column;
    width: 100%;
  }

  .btn-secondary,
  .btn-primary {
    width: 100%;
    justify-content: center;
  }
}
</style>
