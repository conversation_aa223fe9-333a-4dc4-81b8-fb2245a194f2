<template>
  <Layout>
    <div class="team-workspace">
      <!-- 🎨 智能工作台布局 -->
      <div class="workspace-container">
        
        <!-- 左侧：个人化控制面板 -->
        <aside class="personal-dashboard">
          <PersonalDashboard 
            :user="currentUser"
            :my-teams="myTeams"
            :recent-activities="recentActivities"
            :recommendations="personalRecommendations"
            @quick-action="handleQuickAction"
            @filter-change="handleFilterChange"
          />
        </aside>

        <!-- 中间：主要内容区域 -->
        <main class="main-content-area">
          <SmartDiscovery
            :teams="filteredTeams"
            :loading="loading"
            :view-mode="viewMode"
            :search-query="searchQuery"
            :active-filters="activeFilters"
            :sort-options="sortOptions"
            @search="handleSearch"
            @filter="handleFilter"
            @sort="handleSort"
            @view-change="handleViewChange"
            @team-action="handleTeamAction"
          />
        </main>

        <!-- 右侧：上下文信息面板 -->
        <aside class="context-panel" :class="{ 'panel-open': selectedTeam }">
          <ContextPanel
            :selected-team="selectedTeam"
            :team-insights="teamInsights"
            :related-teams="relatedTeams"
            :trending-topics="trendingTopics"
            @close="closeContextPanel"
            @action="handleContextAction"
          />
        </aside>
      </div>

      <!-- 🔍 沉浸式团队预览 -->
      <ImmersivePreview
        v-if="previewTeam"
        :team="previewTeam"
        :user="currentUser"
        @close="closePreview"
        @join="handleJoinTeam"
        @follow="handleFollowTeam"
        @share="handleShareTeam"
      />

      <!-- 📝 智能加入流程 -->
      <SmartJoinFlow
        v-if="joinFlowTeam"
        :team="joinFlowTeam"
        :user="currentUser"
        :suggestions="joinSuggestions"
        @close="closeJoinFlow"
        @submit="handleJoinSubmit"
      />

      <!-- 🎯 个性化设置面板 -->
      <PersonalizationPanel
        v-if="showPersonalization"
        :user-preferences="userPreferences"
        :available-tags="availableTags"
        @close="closePersonalization"
        @save="savePersonalization"
      />
    </div>
  </Layout>
</template>

<script>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '@/stores/toast'
import { useUserStore } from '@/stores/user'
import { useTeamWorkspaceStore } from '@/stores/teamWorkspace'
import teamService from '@/services/teamService'
import userService from '@/services/userService'

// 导入组件
import Layout from '@/components/Layout.vue'
import PersonalDashboard from './components/PersonalDashboard.vue'
import SmartDiscovery from './components/SmartDiscovery.vue'
import ContextPanel from './components/ContextPanel.vue'
import ImmersivePreview from './components/ImmersivePreview.vue'
import SmartJoinFlow from './components/SmartJoinFlow.vue'
import PersonalizationPanel from './components/PersonalizationPanel.vue'

export default {
  name: 'TeamWorkspace',
  components: {
    Layout,
    PersonalDashboard,
    SmartDiscovery,
    ContextPanel,
    ImmersivePreview,
    SmartJoinFlow,
    PersonalizationPanel
  },
  setup() {
    const router = useRouter()
    const toastStore = useToastStore()
    const userStore = useUserStore()
    const workspaceStore = useTeamWorkspaceStore()

    // ===== 响应式数据 =====
    const loading = ref(false)
    const error = ref(null)
    
    // 用户和团队数据
    const currentUser = computed(() => userStore.user)
    const allTeams = ref([])
    const myTeams = ref([])
    const recentActivities = ref([])
    
    // 界面状态
    const viewMode = ref('smart-grid') // smart-grid, timeline, kanban
    const searchQuery = ref('')
    const activeFilters = ref(new Set())
    const sortOptions = ref({ by: 'relevance', order: 'desc' })
    
    // 选中和预览状态
    const selectedTeam = ref(null)
    const previewTeam = ref(null)
    const joinFlowTeam = ref(null)
    
    // 面板状态
    const showPersonalization = ref(false)
    
    // 智能推荐数据
    const personalRecommendations = ref([])
    const teamInsights = ref(null)
    const relatedTeams = ref([])
    const trendingTopics = ref([])
    const joinSuggestions = ref([])
    const userPreferences = ref({})
    const availableTags = ref([])

    // ===== 计算属性 =====
    const filteredTeams = computed(() => {
      let teams = [...allTeams.value]
      
      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        teams = teams.filter(team => 
          team.name.toLowerCase().includes(query) ||
          team.description?.toLowerCase().includes(query) ||
          team.tags?.some(tag => tag.toLowerCase().includes(query))
        )
      }
      
      // 标签过滤
      if (activeFilters.value.size > 0) {
        teams = teams.filter(team => {
          return Array.from(activeFilters.value).some(filter => {
            switch (filter) {
              case 'my-teams': return team.isMember
              case 'starred': return team.isStarred
              case 'public': return team.isPublic
              case 'active': return team.isActive
              default: return team.tags?.includes(filter)
            }
          })
        })
      }
      
      // 智能排序
      return sortTeams(teams)
    })

    // ===== 核心方法 =====
    
    // 数据加载
    const loadWorkspaceData = async () => {
      try {
        loading.value = true
        error.value = null
        
        const currentUserId = currentUser.value?.id || 1
        
        // 并行加载所有数据
        const [
          allTeamsResponse,
          userTeamsResponse,
          activitiesResponse,
          recommendationsResponse,
          preferencesResponse
        ] = await Promise.all([
          teamService.getAllTeams({ pageSize: 50 }),
          userService.getUserTeams(currentUserId),
          userService.getRecentActivities(currentUserId),
          teamService.getPersonalRecommendations(currentUserId),
          userService.getUserPreferences(currentUserId)
        ])
        
        // 处理团队数据
        allTeams.value = processTeamsData(allTeamsResponse?.list || [])
        myTeams.value = userTeamsResponse?.list || []
        recentActivities.value = activitiesResponse?.list || []
        personalRecommendations.value = recommendationsResponse?.list || []
        userPreferences.value = preferencesResponse || {}
        
        // 生成可用标签
        generateAvailableTags()
        
        // 加载趋势话题
        await loadTrendingTopics()
        
      } catch (err) {
        console.error('加载工作台数据失败:', err)
        error.value = err.message || '加载失败'
        toastStore.error('加载工作台数据失败')
      } finally {
        loading.value = false
      }
    }

    // 处理团队数据
    const processTeamsData = (teams) => {
      return teams.map(team => {
        const achievements = team.achievements || {}
        const members = team.members || []
        const currentUserId = currentUser.value?.id || 1
        
        // 判断用户关系
        const userMember = members.find(m => m.userId === currentUserId)
        const isMember = !!userMember
        const isAdmin = userMember?.role === 'admin'
        
        return {
          id: team.teamId || team.id,
          name: team.name || '未命名团队',
          description: team.description || '',
          avatar: team.avatarUrl,
          
          // 团队属性
          isPublic: team.privacy === '1',
          isActive: team.isActive !== false,
          isMember,
          isAdmin,
          isStarred: false, // TODO: 从用户数据获取
          
          // 统计数据
          membersCount: team.memberCount || members.length || 0,
          contentCount: achievements.articlesRecommended || 0,
          viewsCount: achievements.totalViews || 0,
          likesCount: achievements.totalLikes || 0,
          
          // 标签和分类
          tags: team.tags || [],
          category: getCategoryFromTags(team.tags),
          
          // 成员信息
          members: members,
          
          // 时间信息
          createdAt: team.createdAt,
          lastActivityAt: getLastActivity(team, members),
          
          // 智能评分
          relevanceScore: calculateRelevanceScore(team),
          activityScore: calculateActivityScore(achievements, members.length),
          matchScore: calculateMatchScore(team)
        }
      })
    }

    // 智能排序
    const sortTeams = (teams) => {
      const { by, order } = sortOptions.value
      
      return [...teams].sort((a, b) => {
        let comparison = 0
        
        switch (by) {
          case 'relevance':
            comparison = b.relevanceScore - a.relevanceScore
            break
          case 'activity':
            comparison = b.activityScore - a.activityScore
            break
          case 'members':
            comparison = b.membersCount - a.membersCount
            break
          case 'created':
            comparison = new Date(b.createdAt) - new Date(a.createdAt)
            break
          case 'match':
            comparison = b.matchScore - a.matchScore
            break
          default:
            comparison = 0
        }
        
        return order === 'desc' ? comparison : -comparison
      })
    }

    // 计算相关性评分
    const calculateRelevanceScore = (team) => {
      let score = 0
      
      // 基于用户兴趣标签
      const userInterests = userPreferences.value.interests || []
      const matchingTags = team.tags?.filter(tag => userInterests.includes(tag)) || []
      score += matchingTags.length * 20
      
      // 基于团队活跃度
      score += Math.min(team.activityScore || 0, 30)
      
      // 基于团队规模
      score += Math.min((team.membersCount || 0) * 2, 20)
      
      // 基于用户社交网络
      // TODO: 实现基于好友关系的推荐
      
      return Math.min(score, 100)
    }

    // 计算活跃度评分
    const calculateActivityScore = (achievements, memberCount) => {
      const viewsScore = Math.min((achievements.totalViews || 0) / 1000, 40)
      const likesScore = Math.min((achievements.totalLikes || 0) / 100, 30)
      const memberScore = Math.min(memberCount * 2, 30)
      
      return Math.round(viewsScore + likesScore + memberScore)
    }

    // 计算匹配度评分
    const calculateMatchScore = (team) => {
      const userSkills = userPreferences.value.skills || []
      const teamTags = team.tags || []
      const matches = teamTags.filter(tag => userSkills.includes(tag))
      return Math.min(matches.length * 25, 100)
    }

    // 辅助方法
    const getCategoryFromTags = (tags = []) => {
      const categoryMap = {
        'AIGC': 'AI与技术',
        'AI': 'AI与技术',
        '前端': '技术开发',
        '后端': '技术开发',
        '设计': '设计创意',
        '产品': '产品运营',
        '运营': '产品运营',
        '数据': '数据分析'
      }

      for (const tag of tags) {
        if (categoryMap[tag]) return categoryMap[tag]
      }
      return '综合团队'
    }

    const getLastActivity = (team, members) => {
      const activities = [
        team.updatedAt,
        team.createdAt,
        ...members.map(m => m.joinedAt || m.createdAt)
      ].filter(Boolean)

      if (activities.length === 0) return team.createdAt

      return activities.reduce((latest, current) => {
        return new Date(current) > new Date(latest) ? current : latest
      })
    }

    const generateAvailableTags = () => {
      const tagSet = new Set()
      allTeams.value.forEach(team => {
        team.tags?.forEach(tag => tagSet.add(tag))
      })
      availableTags.value = Array.from(tagSet)
    }

    const loadTrendingTopics = async () => {
      try {
        // 模拟趋势话题数据
        trendingTopics.value = [
          { name: 'AIGC', count: 15, trend: 'up' },
          { name: '前端开发', count: 12, trend: 'up' },
          { name: '产品设计', count: 8, trend: 'stable' },
          { name: '数据分析', count: 6, trend: 'down' }
        ]
      } catch (error) {
        console.error('加载趋势话题失败:', error)
      }
    }

    // 事件处理方法
    const handleQuickAction = (action) => {
      switch (action.type) {
        case 'create-team':
          router.push('/space/team/create')
          break
        case 'join-random':
          handleJoinRandomTeam()
          break
        case 'personalize':
          showPersonalization.value = true
          break
        case 'view-my-teams':
          activeFilters.value = new Set(['my-teams'])
          break
      }
    }

    const handleFilterChange = (filters) => {
      activeFilters.value = new Set(filters)
    }

    const handleSearch = (query) => {
      searchQuery.value = query
    }

    const handleFilter = (filter) => {
      if (activeFilters.value.has(filter)) {
        activeFilters.value.delete(filter)
      } else {
        activeFilters.value.add(filter)
      }
      activeFilters.value = new Set(activeFilters.value)
    }

    const handleSort = (options) => {
      sortOptions.value = { ...options }
    }

    const handleViewChange = (mode) => {
      viewMode.value = mode
    }

    const handleTeamAction = async (action) => {
      switch (action.type) {
        case 'preview':
          await openTeamPreview(action.team)
          break
        case 'select':
          selectTeam(action.team)
          break
        case 'join':
          openJoinFlow(action.team)
          break
        case 'star':
          await toggleTeamStar(action.team)
          break
      }
    }

    const handleContextAction = (action) => {
      switch (action.type) {
        case 'view-related':
          // 显示相关团队
          break
        case 'compare':
          // 团队对比功能
          break
        case 'share':
          handleShareTeam(selectedTeam.value)
          break
      }
    }

    // 团队操作方法
    const selectTeam = async (team) => {
      selectedTeam.value = team

      // 加载团队洞察数据
      try {
        const insights = await teamService.getTeamInsights(team.id)
        teamInsights.value = insights

        const related = await teamService.getRelatedTeams(team.id)
        relatedTeams.value = related?.list || []
      } catch (error) {
        console.error('加载团队洞察失败:', error)
      }
    }

    const openTeamPreview = async (team) => {
      previewTeam.value = team

      // 预加载团队详细信息
      try {
        const details = await teamService.getTeamDetails(team.id)
        previewTeam.value = { ...team, ...details }
      } catch (error) {
        console.error('加载团队详情失败:', error)
      }
    }

    const openJoinFlow = async (team) => {
      joinFlowTeam.value = team

      // 生成加入建议
      try {
        const suggestions = await teamService.getJoinSuggestions(team.id, currentUser.value.id)
        joinSuggestions.value = suggestions || []
      } catch (error) {
        console.error('生成加入建议失败:', error)
        joinSuggestions.value = []
      }
    }

    const toggleTeamStar = async (team) => {
      try {
        if (team.isStarred) {
          await teamService.unstarTeam(team.id)
          team.isStarred = false
          toastStore.success('已取消收藏')
        } else {
          await teamService.starTeam(team.id)
          team.isStarred = true
          toastStore.success('已收藏团队')
        }
      } catch (error) {
        toastStore.error('操作失败: ' + (error.message || '未知错误'))
      }
    }

    const handleJoinRandomTeam = () => {
      const publicTeams = allTeams.value.filter(team => team.isPublic && !team.isMember)
      if (publicTeams.length > 0) {
        const randomTeam = publicTeams[Math.floor(Math.random() * publicTeams.length)]
        openJoinFlow(randomTeam)
      } else {
        toastStore.info('暂无可加入的公开团队')
      }
    }

    // 模态框控制方法
    const closeContextPanel = () => {
      selectedTeam.value = null
      teamInsights.value = null
      relatedTeams.value = []
    }

    const closePreview = () => {
      previewTeam.value = null
    }

    const closeJoinFlow = () => {
      joinFlowTeam.value = null
      joinSuggestions.value = []
    }

    const closePersonalization = () => {
      showPersonalization.value = false
    }

    // 业务操作方法
    const handleJoinTeam = async (team) => {
      try {
        if (team.isPublic) {
          await teamService.joinTeam(team.id)
          team.isMember = true
          toastStore.success('成功加入团队')
        } else {
          await teamService.applyToJoinTeam(team.id, {
            reason: '希望加入这个优秀的团队'
          })
          toastStore.success('申请已提交，等待审核')
        }
        closeJoinFlow()
      } catch (error) {
        toastStore.error('操作失败: ' + (error.message || '未知错误'))
      }
    }

    const handleFollowTeam = async (team) => {
      try {
        await teamService.followTeam(team.id)
        toastStore.success('已关注团队')
      } catch (error) {
        toastStore.error('关注失败: ' + (error.message || '未知错误'))
      }
    }

    const handleShareTeam = async (team) => {
      try {
        if (navigator.share) {
          await navigator.share({
            title: team.name,
            text: team.description,
            url: `${window.location.origin}/space/team/${team.id}`
          })
        } else {
          // 复制到剪贴板
          await navigator.clipboard.writeText(`${window.location.origin}/space/team/${team.id}`)
          toastStore.success('链接已复制到剪贴板')
        }
      } catch (error) {
        console.error('分享失败:', error)
      }
    }

    const handleJoinSubmit = async (data) => {
      try {
        await teamService.applyToJoinTeam(joinFlowTeam.value.id, data)
        toastStore.success('申请已提交')
        closeJoinFlow()
      } catch (error) {
        toastStore.error('申请失败: ' + (error.message || '未知错误'))
      }
    }

    const savePersonalization = async (preferences) => {
      try {
        await userService.updateUserPreferences(currentUser.value.id, preferences)
        userPreferences.value = { ...preferences }
        toastStore.success('个性化设置已保存')
        closePersonalization()

        // 重新加载推荐数据
        await loadWorkspaceData()
      } catch (error) {
        toastStore.error('保存失败: ' + (error.message || '未知错误'))
      }
    }

    // 生命周期
    onMounted(() => {
      loadWorkspaceData()
    })

    // 监听器
    watch(currentUser, (newUser) => {
      if (newUser) {
        loadWorkspaceData()
      }
    })

    return {
      // 响应式数据
      loading,
      error,
      currentUser,
      allTeams,
      myTeams,
      recentActivities,
      viewMode,
      searchQuery,
      activeFilters,
      sortOptions,
      selectedTeam,
      previewTeam,
      joinFlowTeam,
      showPersonalization,
      personalRecommendations,
      teamInsights,
      relatedTeams,
      trendingTopics,
      joinSuggestions,
      userPreferences,
      availableTags,

      // 计算属性
      filteredTeams,

      // 方法
      loadWorkspaceData,
      handleQuickAction,
      handleFilterChange,
      handleSearch,
      handleFilter,
      handleSort,
      handleViewChange,
      handleTeamAction,
      handleContextAction,
      closeContextPanel,
      closePreview,
      closeJoinFlow,
      closePersonalization,
      handleJoinTeam,
      handleFollowTeam,
      handleShareTeam,
      handleJoinSubmit,
      savePersonalization
    }
  }
}
</script>

<style scoped>
/* 🎨 智能团队工作台样式系统 */

/* ===== 设计系统变量 ===== */
:root {
  /* 主色调 - 现代蓝紫色系 */
  --primary-50: #f0f4ff;
  --primary-100: #e0e9ff;
  --primary-500: #6366f1;
  --primary-600: #4f46e5;
  --primary-700: #4338ca;
  --primary-900: #312e81;

  /* 中性色系 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* 功能色系 */
  --success-500: #10b981;
  --warning-500: #f59e0b;
  --error-500: #ef4444;
  --info-500: #3b82f6;

  /* 阴影系统 */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);

  /* 间距系统 */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;

  /* 圆角系统 */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* 过渡系统 */
  --transition-fast: 150ms ease-out;
  --transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== 基础布局 ===== */
.team-workspace {
  min-height: 100vh;
  background: linear-gradient(135deg,
    var(--gray-50) 0%,
    var(--primary-50) 50%,
    var(--gray-50) 100%);
  position: relative;
}

.workspace-container {
  display: grid;
  grid-template-columns: 320px 1fr 280px;
  grid-template-areas: "sidebar main context";
  gap: var(--space-6);
  max-width: 1600px;
  margin: 0 auto;
  padding: var(--space-6);
  min-height: calc(100vh - 80px); /* 减去header高度 */
}

/* ===== 个人化控制面板 ===== */
.personal-dashboard {
  grid-area: sidebar;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(12px);
  border-radius: var(--radius-2xl);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-lg);
  position: sticky;
  top: var(--space-6);
  height: fit-content;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
}

/* ===== 主要内容区域 ===== */
.main-content-area {
  grid-area: main;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(8px);
  border-radius: var(--radius-2xl);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

/* ===== 上下文信息面板 ===== */
.context-panel {
  grid-area: context;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(12px);
  border-radius: var(--radius-2xl);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-lg);
  position: sticky;
  top: var(--space-6);
  height: fit-content;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
  transform: translateX(100%);
  opacity: 0;
  transition: all var(--transition-normal);
}

.context-panel.panel-open {
  transform: translateX(0);
  opacity: 1;
}

/* ===== 响应式设计 ===== */
@media (max-width: 1400px) {
  .workspace-container {
    grid-template-columns: 280px 1fr 260px;
    gap: var(--space-4);
    padding: var(--space-4);
  }
}

@media (max-width: 1200px) {
  .workspace-container {
    grid-template-columns: 1fr;
    grid-template-areas:
      "main"
      "sidebar"
      "context";
    gap: var(--space-4);
  }

  .personal-dashboard,
  .context-panel {
    position: static;
    max-height: none;
  }

  .context-panel {
    transform: none;
    opacity: 1;
  }

  .context-panel:not(.panel-open) {
    display: none;
  }
}

@media (max-width: 768px) {
  .workspace-container {
    padding: var(--space-3);
    gap: var(--space-3);
  }

  .personal-dashboard,
  .main-content-area,
  .context-panel {
    border-radius: var(--radius-xl);
  }
}

@media (max-width: 480px) {
  .workspace-container {
    padding: var(--space-2);
    gap: var(--space-2);
  }

  .personal-dashboard,
  .main-content-area,
  .context-panel {
    border-radius: var(--radius-lg);
  }
}

/* ===== 滚动条样式 ===== */
.personal-dashboard::-webkit-scrollbar,
.context-panel::-webkit-scrollbar {
  width: 6px;
}

.personal-dashboard::-webkit-scrollbar-track,
.context-panel::-webkit-scrollbar-track {
  background: transparent;
}

.personal-dashboard::-webkit-scrollbar-thumb,
.context-panel::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 3px;
}

.personal-dashboard::-webkit-scrollbar-thumb:hover,
.context-panel::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* ===== 加载状态 ===== */
.workspace-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: var(--gray-500);
  font-size: 16px;
}

.workspace-loading::before {
  content: '';
  width: 32px;
  height: 32px;
  border: 3px solid var(--gray-200);
  border-top-color: var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: var(--space-3);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* ===== 错误状态 ===== */
.workspace-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: var(--error-500);
  text-align: center;
  padding: var(--space-8);
}

.workspace-error h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 var(--space-2) 0;
}

.workspace-error p {
  font-size: 14px;
  color: var(--gray-500);
  margin: 0;
}

/* ===== 工具提示 ===== */
.tooltip {
  position: relative;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--gray-900);
  color: white;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--transition-fast);
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
}

/* ===== 动画效果 ===== */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-normal);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all var(--transition-normal);
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.scale-enter-active,
.scale-leave-active {
  transition: all var(--transition-fast);
}

.scale-enter-from,
.scale-leave-to {
  opacity: 0;
  transform: scale(0.95);
}
</style>
