<template>
  <Layout>
    <div class="team-space-new">
      <!-- 🎨 英雄区域 -->
      <div class="hero-section">
        <div class="hero-background">
          <div class="gradient-bg"></div>
          <div class="pattern-overlay"></div>
        </div>

        <div class="container">
          <div class="hero-content">
            <div class="hero-text">
              <h1 class="hero-title">团队空间</h1>
              <p class="hero-subtitle">连接团队，激发创新，共创未来</p>
              <div class="hero-stats">
                <div class="stat-item">
                  <div class="stat-number">{{ totalTeams }}</div>
                  <div class="stat-label">活跃团队</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ myTeamsCount }}</div>
                  <div class="stat-label">我的团队</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ totalMembers }}</div>
                  <div class="stat-label">总成员</div>
                </div>
              </div>
            </div>

            <div class="hero-actions">
              <button class="btn btn-primary btn-large" @click="goToCreateTeam">
                <i class="fas fa-plus"></i>
                <span>创建团队空间</span>
              </button>
              <button class="btn btn-outline btn-large" @click="refreshData">
                <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
                <span>刷新数据</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <div class="container">
          <!-- 快速导航 -->
          <div class="quick-nav">
            <div class="nav-cards">
              <div class="nav-card" @click="setActiveFilter('my-teams')">
                <div class="nav-icon">
                  <i class="fas fa-user-friends"></i>
                </div>
                <div class="nav-content">
                  <h3>我的团队</h3>
                  <p>{{ myTeamsCount }} 个团队</p>
                </div>
              </div>

              <div class="nav-card" @click="setActiveFilter('recent')">
                <div class="nav-icon">
                  <i class="fas fa-clock"></i>
                </div>
                <div class="nav-content">
                  <h3>最近访问</h3>
                  <p>{{ recentTeamsCount }} 个团队</p>
                </div>
              </div>

              <div class="nav-card" @click="setActiveFilter('starred')">
                <div class="nav-icon">
                  <i class="fas fa-star"></i>
                </div>
                <div class="nav-content">
                  <h3>收藏团队</h3>
                  <p>{{ starredTeamsCount }} 个团队</p>
                </div>
              </div>

              <div class="nav-card" @click="setActiveFilter('public')">
                <div class="nav-icon">
                  <i class="fas fa-globe"></i>
                </div>
                <div class="nav-content">
                  <h3>公开团队</h3>
                  <p>{{ publicTeamsCount }} 个团队</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 🔍 智能搜索和筛选 -->
          <section class="search-section">
            <div class="search-container">
              <!-- 搜索框 -->
              <div class="search-box">
                <div class="search-input-wrapper">
                  <i class="fas fa-search search-icon"></i>
                  <input
                    v-model="searchQuery"
                    type="text"
                    placeholder="搜索团队名称、标签或描述..."
                    class="search-input"
                    @input="handleSearch"
                  >
                  <button
                    v-if="searchQuery"
                    class="clear-search"
                    @click="clearSearch"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>

                <!-- 搜索建议 -->
                <div v-if="searchSuggestions.length && showSuggestions" class="search-suggestions">
                  <div
                    v-for="suggestion in searchSuggestions"
                    :key="suggestion.id"
                    class="suggestion-item"
                    @click="applySuggestion(suggestion)"
                  >
                    <i :class="suggestion.icon"></i>
                    <span>{{ suggestion.text }}</span>
                  </div>
                </div>
              </div>

              <!-- 智能筛选器 -->
              <div class="filter-tabs">
                <button
                  v-for="filter in filterOptions"
                  :key="filter.key"
                  class="filter-tab"
                  :class="{ active: activeFilter === filter.key }"
                  @click="setActiveFilter(filter.key)"
                >
                  <i :class="filter.icon"></i>
                  <span>{{ filter.label }}</span>
                  <span v-if="filter.count" class="filter-count">{{ filter.count }}</span>
                </button>
              </div>
            </div>
          </section>

          <!-- 个人相关团队 -->
        <section v-if="personalTeams.length" class="teams-section personal-section">
          <div class="section-header">
            <h2 class="section-title">
              <i class="fas fa-user-friends"></i>
              我的团队
            </h2>
            <div class="section-actions">
              <button class="view-toggle" @click="togglePersonalView">
                <i :class="personalViewMode === 'grid' ? 'fas fa-list' : 'fas fa-th'"></i>
              </button>
            </div>
          </div>
          
          <div class="teams-grid" :class="personalViewMode">
            <TeamCard
              v-for="team in personalTeams"
              :key="team.id"
              :team="team"
              :view-mode="personalViewMode"
              @join="handleJoinTeam"
              @star="handleStarTeam"
              @enter="handleEnterTeam"
              @preview="handlePreviewTeam"
            />
          </div>
        </section>

        <!-- 推荐团队 -->
        <section v-if="recommendedTeams.length" class="teams-section recommended-section">
          <div class="section-header">
            <h2 class="section-title">
              <i class="fas fa-magic"></i>
              为你推荐
            </h2>
            <p class="section-subtitle">基于你的兴趣和技能匹配</p>
          </div>
          
          <div class="teams-grid recommended">
            <TeamCard
              v-for="team in recommendedTeams"
              :key="team.id"
              :team="team"
              view-mode="card"
              :show-match-score="true"
              @join="handleJoinTeam"
              @star="handleStarTeam"
              @enter="handleEnterTeam"
              @preview="handlePreviewTeam"
            />
          </div>
        </section>

        <!-- 所有团队 -->
        <section class="teams-section all-teams-section">
          <div class="section-header">
            <h2 class="section-title">
              <i class="fas fa-globe"></i>
              {{ getSectionTitle() }}
            </h2>
            <div class="section-actions">
              <div class="sort-options">
                <select v-model="sortBy" class="sort-select">
                  <option value="activity">最近活跃</option>
                  <option value="members">成员最多</option>
                  <option value="created">最新创建</option>
                  <option value="popular">最受欢迎</option>
                </select>
              </div>
              <button class="view-toggle" @click="toggleMainView">
                <i :class="mainViewMode === 'grid' ? 'fas fa-list' : 'fas fa-th'"></i>
              </button>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="loading-container">
            <div class="skeleton-grid">
              <div v-for="i in 8" :key="i" class="skeleton-card"></div>
            </div>
          </div>

          <!-- 团队网格 -->
          <div v-else-if="filteredTeams.length" class="teams-grid" :class="mainViewMode">
            <TeamCard
              v-for="team in filteredTeams"
              :key="team.id"
              :team="team"
              :view-mode="mainViewMode"
              @join="handleJoinTeam"
              @star="handleStarTeam"
              @enter="handleEnterTeam"
              @preview="handlePreviewTeam"
            />
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-state">
            <div class="empty-icon">
              <i class="fas fa-search"></i>
            </div>
            <h3 class="empty-title">{{ getEmptyTitle() }}</h3>
            <p class="empty-description">{{ getEmptyDescription() }}</p>
            <button v-if="activeFilter !== 'all'" class="empty-action" @click="clearFilters">
              查看所有团队
            </button>
          </div>

          <!-- 加载更多 -->
          <div v-if="hasMore && !loading" class="load-more-container">
            <button class="load-more-btn" @click="loadMore" :disabled="loadingMore">
              <i v-if="loadingMore" class="fas fa-spinner fa-spin"></i>
              <i v-else class="fas fa-chevron-down"></i>
              {{ loadingMore ? '加载中...' : '加载更多' }}
            </button>
          </div>
        </section>
        </div>
      </div>

    <!-- 🔍 团队预览模态框 -->
    <TeamPreview
      v-if="previewTeam"
      :team="previewTeam"
      @close="closePreview"
      @join="handleJoinTeam"
      @enter="handleEnterTeam"
    />

    <!-- 📝 加入团队模态框 -->
    <JoinTeamModal
      v-if="joinModalTeam"
      :team="joinModalTeam"
      @close="closeJoinModal"
      @submit="submitJoinApplication"
    />
    </div>
  </Layout>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '@/stores/toast'
import { useUserStore } from '@/stores/user'
import teamService from '@/services/teamService'
import userService from '@/services/userService'
import Layout from '@/components/Layout.vue'
import TeamCard from './components/TeamCard.vue'
import TeamPreview from './components/TeamPreview.vue'
import JoinTeamModal from './components/JoinTeamModal.vue'

export default {
  name: 'TeamSpaceNew',
  components: {
    Layout,
    TeamCard,
    TeamPreview,
    JoinTeamModal
  },
  setup() {
    const router = useRouter()
    const toastStore = useToastStore()
    const userStore = useUserStore()

    // 响应式数据
    const loading = ref(false)
    const loadingMore = ref(false)
    const searchQuery = ref('')
    const activeFilter = ref('all')
    const sortBy = ref('activity')
    const personalViewMode = ref('grid')
    const mainViewMode = ref('grid')
    
    // 团队数据
    const allTeams = ref([])
    const myTeams = ref([])
    const currentPage = ref(1)
    const hasMore = ref(true)
    
    // 模态框状态
    const previewTeam = ref(null)
    const joinModalTeam = ref(null)
    
    // 搜索相关
    const showSuggestions = ref(false)
    const searchSuggestions = ref([])

    // 统计数据
    const totalTeams = computed(() => allTeams.value.length)
    const myTeamsCount = computed(() => myTeams.value.length)
    const totalMembers = computed(() => {
      return allTeams.value.reduce((sum, team) => sum + (team.membersCount || 0), 0)
    })

    // 筛选选项
    const filterOptions = computed(() => [
      { key: 'all', label: '全部', icon: 'fas fa-globe', count: allTeams.value.length },
      { key: 'my', label: '我的团队', icon: 'fas fa-user-friends', count: myTeams.value.length },
      { key: 'starred', label: '已收藏', icon: 'fas fa-star', count: starredTeams.value.length },
      { key: 'public', label: '公开', icon: 'fas fa-unlock', count: publicTeams.value.length },
      { key: 'recommended', label: '推荐', icon: 'fas fa-magic', count: recommendedTeams.value.length }
    ])

    // 计算属性
    const personalTeams = computed(() => {
      return myTeams.value.slice(0, 6) // 显示前6个
    })

    const recommendedTeams = computed(() => {
      // 基于用户兴趣推荐团队的逻辑
      return allTeams.value
        .filter(team => !team.isMember && team.isPublic)
        .slice(0, 4)
    })

    const starredTeams = computed(() => {
      return allTeams.value.filter(team => team.isStarred)
    })

    const publicTeams = computed(() => {
      return allTeams.value.filter(team => team.isPublic)
    })

    // 添加缺失的计算属性
    const recentTeamsCount = computed(() => {
      // 简化实现，实际应该从本地存储或API获取
      return Math.min(myTeams.value.length, 3)
    })

    const starredTeamsCount = computed(() => {
      return starredTeams.value.length
    })

    const publicTeamsCount = computed(() => {
      return publicTeams.value.length
    })

    const filteredTeams = computed(() => {
      let teams = []
      
      switch (activeFilter.value) {
        case 'my':
          teams = myTeams.value
          break
        case 'starred':
          teams = starredTeams.value
          break
        case 'public':
          teams = publicTeams.value
          break
        case 'recommended':
          teams = recommendedTeams.value
          break
        default:
          teams = allTeams.value
      }

      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        teams = teams.filter(team => 
          team.name.toLowerCase().includes(query) ||
          team.description?.toLowerCase().includes(query) ||
          team.tags?.some(tag => tag.toLowerCase().includes(query))
        )
      }

      // 排序
      return sortTeams(teams)
    })

    // 添加缺失的响应式数据
    const error = ref(null)
    const pageSize = ref(12)
    const totalCount = ref(0)

    // 使用原有的数据加载逻辑
    const loadTeamsData = async (page = 1, append = false) => {
      try {
        if (!append) {
          loading.value = true
          error.value = null
        } else {
          loadingMore.value = true
        }

        console.log('开始加载团队数据...', { page, append })

        // 获取当前用户ID
        const currentUserId = userStore.user?.id || 1

        // 并行获取所有团队和用户团队
        const [allTeamsResponse, userTeamsResponse] = await Promise.all([
          teamService.getAllTeams({ page, pageSize: pageSize.value }),
          userService.getUserTeams(currentUserId)
        ])

        // 处理所有团队数据
        if (allTeamsResponse) {
          const teamsArray = allTeamsResponse?.list || []

          // 更新分页信息
          currentPage.value = allTeamsResponse.page || page
          totalCount.value = allTeamsResponse.total || 0
          hasMore.value = (currentPage.value * pageSize.value) < totalCount.value

          if (Array.isArray(teamsArray) && teamsArray.length > 0) {
            const newTeams = teamsArray.map(team => {
              const achievements = team.achievements || {}
              const members = team.members || []

              // 判断用户与团队的关系
              const userMember = members.find(m => m.userId === currentUserId)
              const isMember = !!userMember
              const isAdmin = userMember?.role === 'admin'
              const isMyTeam = team.creatorId === currentUserId || isMember

              return {
                id: team.teamId || team.id,
                teamId: team.teamId || team.id,
                name: team.name || '未命名团队',
                description: team.description || '',
                avatar: team.avatarUrl,
                avatarUrl: team.avatarUrl,

                // 主题色彩
                themeColor: generateTeamTheme(team.tags, team.privacy),

                // 团队属性
                isPublic: team.privacy === '1' || team.privacy === 1,
                isPrivate: team.privacy === '0' || team.privacy === 0,
                privacy: team.privacy,

                // 用户关系
                isMember,
                isAdmin,
                isMyTeam,
                isStarred: false, // TODO: 从用户收藏数据中获取
                isActive: team.isActive !== false,

                // 推荐和特色标识
                isRecommended: achievements.articlesRecommended > 50 || achievements.totalViews > 10000,
                isFeatured: achievements.totalViews > 50000 || members.length > 15,

                // 统计数据
                membersCount: team.memberCount || members.length || 0,
                contentCount: achievements.articlesRecommended || 0,
                articlesCount: achievements.articlesRecommended || 0,
                viewsCount: achievements.totalViews || 0,
                likesCount: achievements.totalLikes || 0,

                // 团队标签和分类
                tags: team.tags || [],
                category: team.category || getCategoryFromTags(team.tags),

                // 成员信息
                members: members,
                recentMembers: members.slice(0, 5),

                // 时间信息
                createdAt: team.createdAt,
                lastActivityAt: getLastActivity(team, members),

                // 活跃度评分
                activityScore: calculateActivityScore(achievements, members.length),
                matchScore: calculateMatchScore(team)
              }
            })

            // 根据是否追加来更新teams数组
            if (append) {
              allTeams.value.push(...newTeams)
            } else {
              allTeams.value = newTeams
            }

            console.log(`成功加载 ${newTeams.length} 个团队`)
          }
        }

        // 处理用户团队数据
        if (userTeamsResponse) {
          const userTeamsArray = userTeamsResponse?.list || []
          myTeams.value = userTeamsArray.map(team => ({
            ...team,
            id: team.teamId || team.id,
            isMember: true,
            isMyTeam: true
          }))
        }

      } catch (error) {
        console.error('加载团队数据失败:', error)
        error.value = error.message || '加载团队数据失败'
        toastStore.error('加载失败: ' + (error.message || '未知错误'))
      } finally {
        loading.value = false
        loadingMore.value = false
      }
    }

    // 原有的辅助函数
    const generateTeamTheme = (tags = [], privacy = 'private') => {
      const themes = {
        'AIGC': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'AI': 'linear-gradient(135deg, #667eea 0%, #8b5cf6 100%)',
        '前端': 'linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%)',
        '后端': 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
        '设计': 'linear-gradient(135deg, #f59e0b 0%, #f97316 100%)',
        '产品': 'linear-gradient(135deg, #ec4899 0%, #be185d 100%)',
        '运营': 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
        '数据': 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)'
      }

      // 根据标签匹配主题
      for (const tag of tags) {
        if (themes[tag]) {
          return themes[tag]
        }
      }

      // 默认主题
      return privacy === 'public'
        ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        : 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)'
    }

    const getCategoryFromTags = (tags = []) => {
      const categoryMap = {
        'AIGC': 'AI与技术',
        'AI': 'AI与技术',
        '前端': '技术开发',
        '后端': '技术开发',
        '设计': '设计创意',
        '产品': '产品运营',
        '运营': '产品运营',
        '数据': '数据分析'
      }

      for (const tag of tags) {
        if (categoryMap[tag]) {
          return categoryMap[tag]
        }
      }

      return '综合团队'
    }

    const getLastActivity = (team, members) => {
      // 简化的活动时间计算
      const activities = [
        team.updatedAt,
        team.createdAt,
        ...members.map(m => m.joinedAt || m.createdAt)
      ].filter(Boolean)

      if (activities.length === 0) return team.createdAt

      return activities.reduce((latest, current) => {
        return new Date(current) > new Date(latest) ? current : latest
      })
    }

    const calculateMatchScore = (team) => {
      // 基于用户兴趣和团队标签计算匹配度
      const userInterests = userStore.user?.interests || []
      const teamTags = team.tags || []
      const matches = teamTags.filter(tag => userInterests.includes(tag))
      return Math.min(matches.length * 20, 100)
    }

    const calculateActivityScore = (achievements, memberCount) => {
      // 基于团队成就和成员数量计算活跃度
      const viewsScore = Math.min((achievements.totalViews || 0) / 1000, 50)
      const likesScore = Math.min((achievements.totalLikes || 0) / 100, 30)
      const memberScore = Math.min(memberCount * 2, 20)

      return Math.round(viewsScore + likesScore + memberScore)
    }

    const sortTeams = (teams) => {
      return [...teams].sort((a, b) => {
        switch (sortBy.value) {
          case 'members':
            return (b.membersCount || 0) - (a.membersCount || 0)
          case 'created':
            return new Date(b.createdAt) - new Date(a.createdAt)
          case 'popular':
            return (b.viewsCount || 0) - (a.viewsCount || 0)
          case 'activity':
          default:
            return b.activityScore - a.activityScore
        }
      })
    }

    // 搜索相关方法
    const handleSearch = debounce(() => {
      if (searchQuery.value.length > 1) {
        generateSearchSuggestions()
        showSuggestions.value = true
      } else {
        showSuggestions.value = false
      }
    }, 300)

    const generateSearchSuggestions = () => {
      const query = searchQuery.value.toLowerCase()
      const suggestions = []

      // 团队名称建议
      allTeams.value.forEach(team => {
        if (team.name.toLowerCase().includes(query)) {
          suggestions.push({
            id: `team-${team.id}`,
            text: team.name,
            icon: 'fas fa-users',
            type: 'team'
          })
        }
      })

      // 标签建议
      const allTags = [...new Set(allTeams.value.flatMap(team => team.tags || []))]
      allTags.forEach(tag => {
        if (tag.toLowerCase().includes(query)) {
          suggestions.push({
            id: `tag-${tag}`,
            text: tag,
            icon: 'fas fa-tag',
            type: 'tag'
          })
        }
      })

      searchSuggestions.value = suggestions.slice(0, 5)
    }

    const applySuggestion = (suggestion) => {
      searchQuery.value = suggestion.text
      showSuggestions.value = false
    }

    const clearSearch = () => {
      searchQuery.value = ''
      showSuggestions.value = false
    }

    // 筛选和视图方法
    const setActiveFilter = (filter) => {
      activeFilter.value = filter
      currentPage.value = 1
    }

    const togglePersonalView = () => {
      personalViewMode.value = personalViewMode.value === 'grid' ? 'list' : 'grid'
    }

    const toggleMainView = () => {
      mainViewMode.value = mainViewMode.value === 'grid' ? 'list' : 'grid'
    }

    const getSectionTitle = () => {
      const filter = filterOptions.value.find(f => f.key === activeFilter.value)
      return filter ? filter.label : '所有团队'
    }

    const getEmptyTitle = () => {
      switch (activeFilter.value) {
        case 'my': return '您还没有加入任何团队'
        case 'starred': return '您还没有收藏任何团队'
        case 'public': return '暂无公开团队'
        case 'recommended': return '暂无推荐团队'
        default: return '暂无团队'
      }
    }

    const getEmptyDescription = () => {
      switch (activeFilter.value) {
        case 'my': return '创建或加入团队，开始协作之旅'
        case 'starred': return '收藏感兴趣的团队，方便快速访问'
        case 'public': return '等待更多团队公开'
        case 'recommended': return '完善您的个人资料以获得更好的推荐'
        default: return '暂时没有找到符合条件的团队'
      }
    }

    const clearFilters = () => {
      activeFilter.value = 'all'
      searchQuery.value = ''
      showSuggestions.value = false
    }

    // 数据加载方法
    const loadMore = async () => {
      if (hasMore.value && !loadingMore.value) {
        await loadTeamsData(currentPage.value + 1, true)
      }
    }

    const refreshData = async () => {
      currentPage.value = 1
      await loadTeamsData(1, false)
      toastStore.success('数据已刷新')
    }

    // 团队操作方法
    const handleJoinTeam = (team) => {
      joinModalTeam.value = team
    }

    const handleStarTeam = async (team) => {
      try {
        if (team.isStarred) {
          await teamService.unstarTeam(team.id)
          team.isStarred = false
          toastStore.success('已取消收藏')
        } else {
          await teamService.starTeam(team.id)
          team.isStarred = true
          toastStore.success('已收藏团队')
        }
      } catch (error) {
        toastStore.error('操作失败: ' + (error.message || '未知错误'))
      }
    }

    const handleEnterTeam = (team) => {
      router.push(`/space/team/${team.id}`)
    }

    const handlePreviewTeam = (team) => {
      previewTeam.value = team
    }

    const closePreview = () => {
      previewTeam.value = null
    }

    const closeJoinModal = () => {
      joinModalTeam.value = null
    }

    const submitJoinApplication = async (applicationData) => {
      try {
        await teamService.applyToJoinTeam(joinModalTeam.value.id, applicationData)
        toastStore.success('申请已提交，等待审核')
        closeJoinModal()
      } catch (error) {
        toastStore.error('申请失败: ' + (error.message || '未知错误'))
      }
    }

    const goToCreateTeam = () => {
      router.push('/space/team/create')
    }

    // 防抖函数
    function debounce(func, wait) {
      let timeout
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout)
          func(...args)
        }
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
      }
    }

    // 生命周期
    onMounted(() => {
      loadTeamsData()
    })

    // 监听器
    watch(sortBy, () => {
      // 排序变化时不需要重新加载数据，计算属性会自动更新
    })

    return {
      // 响应式数据
      loading,
      loadingMore,
      searchQuery,
      activeFilter,
      sortBy,
      personalViewMode,
      mainViewMode,
      showSuggestions,
      searchSuggestions,
      previewTeam,
      joinModalTeam,
      error,

      // 计算属性
      totalTeams,
      myTeamsCount,
      totalMembers,
      recentTeamsCount,
      starredTeamsCount,
      publicTeamsCount,
      filterOptions,
      personalTeams,
      recommendedTeams,
      filteredTeams,
      hasMore,

      // 方法
      loadTeamsData,
      handleSearch,
      clearSearch,
      applySuggestion,
      setActiveFilter,
      togglePersonalView,
      toggleMainView,
      getSectionTitle,
      getEmptyTitle,
      getEmptyDescription,
      clearFilters,
      loadMore,
      refreshData,
      handleJoinTeam,
      handleStarTeam,
      handleEnterTeam,
      handlePreviewTeam,
      closePreview,
      closeJoinModal,
      submitJoinApplication,
      goToCreateTeam
    }
  }
}
</script>

<style scoped>
/* 🎨 现代化团队空间样式系统 */

/* ===== 优化的色彩系统 ===== */
:root {
  /* 主色调 - 更柔和的蓝紫色系 */
  --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
  --primary-light: linear-gradient(135deg, #e0e7ff 0%, #ede9fe 100%);
  --primary-dark: linear-gradient(135deg, #4338ca 0%, #7c3aed 100%);

  /* 辅助色调 */
  --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --error-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  --info-gradient: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);

  /* 中性色调 - 更温暖的灰色系 */
  --text-primary: #1f2937;
  --text-secondary: #4b5563;
  --text-muted: #6b7280;
  --text-light: #9ca3af;
  --text-lighter: #d1d5db;

  /* 背景色调 - 更温暖的白色系 */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --bg-quaternary: #e5e7eb;

  /* 边框色调 */
  --border-light: #e5e7eb;
  --border-medium: #d1d5db;
  --border-dark: #9ca3af;

  /* 阴影系统 - 更柔和的阴影 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.15);

  /* 圆角系统 */
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 20px;
  --radius-3xl: 24px;

  /* 动画系统 */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  /* 特殊效果 */
  --glass-bg: rgba(255, 255, 255, 0.8);
  --glass-border: rgba(255, 255, 255, 0.2);
  --backdrop-blur: blur(12px);
}

/* ===== 基础布局 ===== */
.team-space-new {
  min-height: 100vh;
  background: linear-gradient(135deg,
    var(--bg-secondary) 0%,
    var(--bg-tertiary) 50%,
    var(--bg-secondary) 100%);
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

/* ===== 英雄区域 ===== */
.hero-section {
  position: relative;
  padding: 80px 0 60px;
  overflow: hidden;
  background: linear-gradient(135deg,
    rgba(99, 102, 241, 0.05) 0%,
    rgba(139, 92, 246, 0.05) 50%,
    rgba(168, 85, 247, 0.05) 100%);
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.gradient-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-light);
  opacity: 0.3;
}

.pattern-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
  background-size: 100px 100px;
}

.hero-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 48px;
}

.hero-text {
  flex: 1;
  max-width: 600px;
}

.hero-title {
  font-size: 48px;
  font-weight: 800;
  color: var(--text-primary);
  margin: 0 0 16px 0;
  line-height: 1.1;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
}

.hero-subtitle {
  font-size: 20px;
  color: var(--text-secondary);
  margin: 0 0 32px 0;
  line-height: 1.5;
  font-weight: 400;
}

.hero-stats {
  display: flex;
  gap: 32px;
  margin-bottom: 40px;
}

.stat-item {
  text-align: center;
  padding: 20px 24px;
  background: var(--glass-bg);
  border-radius: var(--radius-xl);
  border: 1px solid var(--glass-border);
  backdrop-filter: var(--backdrop-blur);
  transition: var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.9);
}

.stat-number {
  font-size: 28px;
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: 6px;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 13px;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

.hero-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.btn {
  padding: 14px 28px;
  border: none;
  border-radius: var(--radius-xl);
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  text-decoration: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.btn.btn-large {
  padding: 16px 32px;
  font-size: 16px;
}

.btn.btn-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-md);
  border: 1px solid transparent;
}

.btn.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn.btn-primary:hover::before {
  left: 100%;
}

.btn.btn-primary:hover {
  box-shadow: var(--shadow-lg);
}

.btn.btn-outline {
  background: var(--glass-bg);
  color: var(--text-secondary);
  border: 1px solid var(--border-light);
  backdrop-filter: var(--backdrop-blur);
}

.btn.btn-outline:hover {
  background: rgba(255, 255, 255, 0.95);
  color: var(--text-primary);
  box-shadow: var(--shadow-md);
  border-color: var(--border-medium);
}

/* ===== 搜索和筛选区域 ===== */
.search-section {
  padding: 24px 0;
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
}

.search-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.search-box {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 16px;
  color: var(--text-muted);
  font-size: 16px;
  z-index: 2;
}

.search-input {
  width: 100%;
  padding: 16px 16px 16px 48px;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  font-size: 16px;
  background: var(--bg-primary);
  transition: var(--transition-normal);
  outline: none;
}

.search-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.clear-search {
  position: absolute;
  right: 12px;
  width: 32px;
  height: 32px;
  border: none;
  background: var(--bg-tertiary);
  border-radius: 50%;
  color: var(--text-muted);
  cursor: pointer;
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-search:hover {
  background: var(--border-medium);
  color: var(--text-secondary);
}

.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  z-index: 10;
  margin-top: 4px;
  overflow: hidden;
}

.suggestion-item {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: var(--transition-fast);
  border-bottom: 1px solid var(--border-light);
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover {
  background: var(--bg-secondary);
}

.suggestion-item i {
  color: var(--text-muted);
  width: 16px;
}

.filter-tabs {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.filter-tab {
  padding: 10px 16px;
  border: 1px solid var(--border-light);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
}

.filter-tab:hover {
  background: var(--bg-secondary);
  border-color: var(--border-medium);
}

.filter-tab.active {
  background: var(--primary-gradient);
  color: white;
  border-color: transparent;
  box-shadow: var(--shadow-sm);
}

.filter-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 600;
}

.filter-tab.active .filter-count {
  background: rgba(255, 255, 255, 0.3);
}

/* ===== 主要内容区域 ===== */
.main-content {
  padding: 40px 0;
}

/* ===== 快速导航 ===== */
.quick-nav {
  margin-bottom: 48px;
}

.nav-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.nav-card {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: 24px;
  cursor: pointer;
  transition: var(--transition-normal);
  backdrop-filter: var(--backdrop-blur);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  overflow: hidden;
}

.nav-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-light);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.nav-card:hover::before {
  opacity: 0.5;
}

.nav-card:hover {
  box-shadow: var(--shadow-md);
  border-color: rgba(99, 102, 241, 0.2);
}

.nav-icon {
  width: 56px;
  height: 56px;
  border-radius: var(--radius-lg);
  background: var(--primary-gradient);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  box-shadow: var(--shadow-md);
  position: relative;
  z-index: 1;
}

.nav-content {
  flex: 1;
  position: relative;
  z-index: 1;
}

.nav-content h3 {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.nav-content p {
  font-size: 14px;
  color: var(--text-muted);
  margin: 0;
}

.teams-section {
  margin-bottom: 48px;
}

.teams-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid var(--border-light);
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-title i {
  color: #667eea;
  font-size: 20px;
}

.section-subtitle {
  font-size: 14px;
  color: var(--text-muted);
  margin: 4px 0 0 32px;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sort-select {
  padding: 8px 12px;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  font-size: 14px;
  color: var(--text-secondary);
  cursor: pointer;
  outline: none;
  transition: var(--transition-fast);
}

.sort-select:focus {
  border-color: #667eea;
}

.view-toggle {
  width: 40px;
  height: 40px;
  border: 1px solid var(--border-light);
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
  color: var(--text-muted);
  cursor: pointer;
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-toggle:hover {
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

/* ===== 团队网格布局 ===== */
.teams-grid {
  display: grid;
  gap: 24px;
}

.teams-grid.grid {
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
}

.teams-grid.list {
  grid-template-columns: 1fr;
  gap: 16px;
}

.teams-grid.recommended {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

/* ===== 加载状态 ===== */
.loading-container {
  padding: 40px 0;
}

.skeleton-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.skeleton-card {
  height: 280px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--radius-lg);
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* ===== 空状态 ===== */
.empty-state {
  text-align: center;
  padding: 80px 20px;
  color: var(--text-muted);
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 24px;
  opacity: 0.5;
}

.empty-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-secondary);
  margin: 0 0 12px 0;
}

.empty-description {
  font-size: 16px;
  margin: 0 0 24px 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.empty-action {
  padding: 12px 24px;
  background: var(--primary-gradient);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
}

.empty-action:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* ===== 加载更多 ===== */
.load-more-container {
  text-align: center;
  padding: 32px 0;
}

.load-more-btn {
  padding: 12px 32px;
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  font-size: 14px;
  font-weight: 600;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition-normal);
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.load-more-btn:hover:not(:disabled) {
  background: var(--bg-secondary);
  border-color: var(--border-medium);
  transform: translateY(-1px);
}

.load-more-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* ===== 响应式设计 ===== */
@media (max-width: 1024px) {
  .header-content {
    grid-template-columns: 1fr;
    gap: 24px;
    text-align: center;
  }

  .quick-stats {
    justify-content: center;
  }

  .teams-grid.grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .smart-header {
    padding: 20px 0;
  }

  .page-title {
    font-size: 24px;
  }

  .quick-stats {
    flex-wrap: wrap;
    gap: 12px;
  }

  .stat-item {
    padding: 12px 16px;
  }

  .filter-tabs {
    gap: 6px;
  }

  .filter-tab {
    padding: 8px 12px;
    font-size: 13px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .teams-grid.grid {
    grid-template-columns: 1fr;
  }

  .skeleton-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .search-input {
    padding: 14px 14px 14px 44px;
    font-size: 14px;
  }

  .quick-actions {
    flex-direction: column;
    width: 100%;
  }

  .action-btn {
    justify-content: center;
  }
}
</style>
