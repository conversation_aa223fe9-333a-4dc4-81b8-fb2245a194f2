<template>
  <Layout>
    <div class="create-team-page">
      <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
          <div class="header-content">
            <button class="back-btn" @click="goBack">
              <i class="fas fa-arrow-left"></i>
            </button>
            <div class="header-text">
              <h1>创建团队空间</h1>
              <p>创建一个专属的团队协作空间，与团队成员分享知识和资源</p>
            </div>
          </div>
        </div>

        <!-- 创建表单 -->
        <div class="create-form-container">
          <div class="form-card">
            <form @submit.prevent="createTeam" class="team-form">
              <!-- 基本信息 -->
              <div class="form-section">
                <h3 class="section-title">基本信息</h3>
                
                <div class="form-group">
                  <label class="form-label" for="teamName">
                    团队名称 <span class="required">*</span>
                  </label>
                  <input
                    id="teamName"
                    type="text"
                    v-model="formData.name"
                    class="form-input"
                    placeholder="请输入团队名称"
                    maxlength="50"
                    required
                  >
                  <div class="input-hint">{{ formData.name.length }}/50</div>
                </div>

                <div class="form-group">
                  <label class="form-label" for="teamDescription">
                    团队描述 <span class="required">*</span>
                  </label>
                  <textarea
                    id="teamDescription"
                    v-model="formData.description"
                    class="form-textarea"
                    placeholder="简单介绍一下这个团队的目标和用途..."
                    rows="4"
                    maxlength="200"
                    required
                  ></textarea>
                  <div class="input-hint">{{ formData.description.length }}/200</div>
                </div>

                <div class="form-group">
                  <label class="form-label">团队头像</label>
                  <div class="avatar-upload">
                    <div class="avatar-preview" @click="selectAvatar">
                      <img v-if="formData.avatarUrl" :src="formData.avatarUrl" alt="团队头像">
                      <div v-else class="avatar-placeholder">
                        <i class="fas fa-camera"></i>
                        <span>上传头像</span>
                      </div>
                    </div>
                    <input
                      ref="avatarInput"
                      type="file"
                      accept="image/*"
                      @change="handleAvatarChange"
                      style="display: none"
                    >
                    <div class="avatar-tips">
                      <p>建议尺寸：200x200px，支持 JPG、PNG 格式</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 隐私设置 -->
              <div class="form-section">
                <h3 class="section-title">隐私设置</h3>
                
                <div class="form-group">
                  <div class="radio-group">
                    <label class="radio-option">
                      <input
                        type="radio"
                        v-model="formData.privacy"
                        value="1"
                      >
                      <div class="radio-content">
                        <div class="radio-header">
                          <i class="fas fa-globe"></i>
                          <span class="radio-title">公开团队</span>
                        </div>
                        <p class="radio-description">任何人都可以查看团队内容，但只有成员可以发布内容</p>
                      </div>
                    </label>

                    <label class="radio-option">
                      <input
                        type="radio"
                        v-model="formData.privacy"
                        value="0"
                      >
                      <div class="radio-content">
                        <div class="radio-header">
                          <i class="fas fa-lock"></i>
                          <span class="radio-title">私有团队</span>
                        </div>
                        <p class="radio-description">只有团队成员可以查看和访问团队内容</p>
                      </div>
                    </label>
                  </div>
                </div>
              </div>

              <!-- 团队标签 -->
              <div class="form-section">
                <h3 class="section-title">团队标签</h3>

                <div class="form-group">
                  <label class="form-label">
                    添加标签 <span class="optional">(可选)</span>
                  </label>

                  <!-- 预设标签选择 -->
                  <div class="preset-tags">
                    <span class="preset-tags-label">常用标签：</span>
                    <button
                      v-for="presetTag in presetTags"
                      :key="presetTag"
                      type="button"
                      class="preset-tag-btn"
                      :class="{ selected: formData.tags.includes(presetTag) }"
                      @click="togglePresetTag(presetTag)"
                    >
                      {{ presetTag }}
                    </button>
                  </div>

                  <div class="tags-input-container">
                    <div class="tags-display">
                      <span
                        v-for="(tag, index) in formData.tags"
                        :key="index"
                        class="tag-item"
                      >
                        {{ tag }}
                        <button type="button" @click="removeTag(index)" class="tag-remove">
                          <i class="fas fa-times"></i>
                        </button>
                      </span>
                    </div>
                    <input
                      type="text"
                      v-model="newTag"
                      @keydown.enter.prevent="addTag"
                      @keydown="handleTagInput"
                      class="tag-input"
                      placeholder="输入自定义标签后按回车添加"
                      maxlength="20"
                    >
                  </div>
                  <div class="input-hint">用标签描述团队的技术领域或兴趣方向，最多5个标签</div>
                </div>
              </div>

              <!-- 团队设置 -->
              <div class="form-section">
                <h3 class="section-title">邀请设置</h3>

                <div class="form-group">
                  <div class="radio-group">
                    <label class="radio-option">
                      <input
                        type="radio"
                        v-model="formData.inviteSetting"
                        value="admin_approval"
                      >
                      <div class="radio-content">
                        <div class="radio-header">
                          <i class="fas fa-user-shield"></i>
                          <span class="radio-title">管理员审批</span>
                        </div>
                        <p class="radio-description">只有管理员可以邀请新成员，加入需要审批</p>
                      </div>
                    </label>

                    <label class="radio-option">
                      <input
                        type="radio"
                        v-model="formData.inviteSetting"
                        value="member_invite"
                      >
                      <div class="radio-content">
                        <div class="radio-header">
                          <i class="fas fa-users"></i>
                          <span class="radio-title">成员邀请</span>
                        </div>
                        <p class="radio-description">团队成员可以直接邀请其他人加入团队</p>
                      </div>
                    </label>
                  </div>
                </div>
              </div>

              <!-- 表单操作 -->
              <div class="form-actions">
                <button type="button" class="btn btn-outline" @click="goBack">
                  取消
                </button>
                <button type="submit" class="btn btn-primary" :disabled="!isFormValid || isSubmitting">
                  <i v-if="isSubmitting" class="fas fa-spinner fa-spin"></i>
                  {{ isSubmitting ? '创建中...' : '创建团队' }}
                </button>
              </div>
            </form>
          </div>

          <!-- 侧边栏提示 -->
          <div class="sidebar-tips">
            <div class="tip-card">
              <div class="tip-icon">
                <i class="fas fa-lightbulb"></i>
              </div>
              <h4>创建团队小贴士</h4>
              <ul class="tip-list">
                <li>选择一个简洁明了的团队名称</li>
                <li>详细的描述有助于成员了解团队目标</li>
                <li>合适的头像能让团队更具辨识度</li>
                <li>根据团队性质选择合适的隐私设置</li>
              </ul>
            </div>

            <div class="tip-card">
              <div class="tip-icon">
                <i class="fas fa-users"></i>
              </div>
              <h4>团队管理</h4>
              <ul class="tip-list">
                <li>创建后您将成为团队所有者</li>
                <li>可以随时邀请成员加入团队</li>
                <li>可以设置不同的成员角色和权限</li>
                <li>团队设置可以在创建后修改</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import Layout from '../../../components/Layout.vue'
import { useToastStore } from '../../../stores/toast'
import teamService from '../../../services/teamService'

export default {
  name: 'CreateTeamSpace',
  components: {
    Layout
  },
  setup() {
    const router = useRouter()
    const toastStore = useToastStore()
    const avatarInput = ref(null)
    const isSubmitting = ref(false)
    const newTag = ref('')

    // 预设标签
    const presetTags = ref([
      'AIGC', '大语言模型', 'Agent', '多模态',
      'Python', 'JavaScript', 'Vue', 'React',
      '前端开发', '后端开发', '全栈开发', '移动开发',
      '数据分析', '机器学习', '深度学习', '算法',
      '产品设计', 'UI/UX', '项目管理', '敏捷开发'
    ])

    const formData = ref({
      name: '',
      description: '',
      avatarUrl: '',
      privacy: '1', // 0=私有团队, 1=公开团队，默认公开
      inviteSetting: 'admin_approval', // 符合API规范: "admin_approval" | "member_invite"
      tags: []
    })

    const isFormValid = computed(() => {
      return formData.value.name.trim().length > 0 &&
             formData.value.description.trim().length > 0
    })

    const goBack = () => {
      router.go(-1)
    }

    const selectAvatar = () => {
      avatarInput.value?.click()
    }

    const handleAvatarChange = (event) => {
      const file = event.target.files[0]
      if (file) {
        if (file.size > 5 * 1024 * 1024) { // 5MB限制
          toastStore.error('图片大小不能超过5MB')
          return
        }

        const reader = new FileReader()
        reader.onload = (e) => {
          formData.value.avatarUrl = e.target.result
        }
        reader.readAsDataURL(file)
      }
    }

    const handleTagInput = (event) => {
      if (event.key === ',' || event.key === 'Enter') {
        event.preventDefault()
        addTag()
      }
    }

    const addTag = () => {
      const tag = newTag.value.trim()
      if (tag && !formData.value.tags.includes(tag) && formData.value.tags.length < 5) {
        formData.value.tags.push(tag)
        newTag.value = ''
      } else if (formData.value.tags.length >= 5) {
        toastStore.error('最多只能添加5个标签')
      } else if (formData.value.tags.includes(tag)) {
        toastStore.error('标签已存在')
      }
    }

    const removeTag = (index) => {
      formData.value.tags.splice(index, 1)
    }

    const togglePresetTag = (tag) => {
      const index = formData.value.tags.indexOf(tag)
      if (index > -1) {
        // 如果标签已存在，则移除
        formData.value.tags.splice(index, 1)
      } else {
        // 如果标签不存在且未达到上限，则添加
        if (formData.value.tags.length < 5) {
          formData.value.tags.push(tag)
        } else {
          toastStore.error('最多只能添加5个标签')
        }
      }
    }

    const createTeam = async () => {
      // 详细的表单验证
      if (!formData.value.name.trim()) {
        toastStore.error('请输入团队名称')
        return
      }

      if (!formData.value.description.trim()) {
        toastStore.error('请输入团队描述')
        return
      }

      isSubmitting.value = true

      try {
        // 准备符合API规范的数据
        const teamData = {
          name: formData.value.name.trim(),
          description: formData.value.description.trim(),
          avatarUrl: formData.value.avatarUrl || '',
          privacy: formData.value.privacy, // 0=私有, 1=公开
          inviteSetting: formData.value.inviteSetting,
          tags: formData.value.tags
        }

        // 调用API创建团队
        const result = await teamService.createTeam(teamData)

        toastStore.success('团队创建成功！')

        // 跳转到团队详情页
        if (result && result.teamId) {
          router.push(`/team-space/${result.teamId}`)
        } else {
          router.push('/team-space')
        }
      } catch (error) {
        console.error('创建团队失败:', error)
        toastStore.error('创建失败: ' + (error.message || '未知错误'))
      } finally {
        isSubmitting.value = false
      }
    }

    return {
      formData,
      isFormValid,
      isSubmitting,
      avatarInput,
      newTag,
      presetTags,
      goBack,
      selectAvatar,
      handleAvatarChange,
      handleTagInput,
      addTag,
      removeTag,
      togglePresetTag,
      createTeam
    }
  }
}
</script>

<style scoped>
.create-team-page {
  padding: 20px 0;
  background: #f8f9fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-btn {
  width: 40px;
  height: 40px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.header-text h1 {
  font-size: 28px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 8px 0;
}

.header-text p {
  color: #6b7280;
  font-size: 16px;
  margin: 0;
}

.create-form-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
}

.form-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.team-form {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.form-section {
  border-bottom: 1px solid #f3f4f6;
  padding-bottom: 30px;
}

.form-section:last-of-type {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 20px 0;
}

.form-group {
  margin-bottom: 20px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.required {
  color: #ef4444;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: white;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.input-hint {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 4px;
  text-align: right;
}

/* 头像上传样式 */
.avatar-upload {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.avatar-preview {
  width: 100px;
  height: 100px;
  border-radius: 12px;
  border: 2px dashed #d1d5db;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.2s ease;
}

.avatar-preview:hover {
  border-color: #3b82f6;
}

.avatar-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 12px;
  gap: 8px;
}

.avatar-placeholder i {
  font-size: 24px;
}

.avatar-tips {
  flex: 1;
}

.avatar-tips p {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
}

/* 标签输入样式 */
.tags-input-container {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 8px;
  background: white;
  min-height: 80px;
}

.tags-display {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.tag-item {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: #3b82f6;
  color: white;
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.tag-remove {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.tag-remove:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tag-input {
  border: none;
  outline: none;
  padding: 4px 0;
  font-size: 14px;
  flex: 1;
  min-width: 120px;
}

.tag-input::placeholder {
  color: #9ca3af;
}

.optional {
  color: #6b7280;
  font-weight: normal;
}

/* 预设标签样式 */
.preset-tags {
  margin-bottom: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.preset-tags-label {
  display: block;
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.preset-tag-btn {
  display: inline-block;
  margin: 0 6px 6px 0;
  padding: 4px 12px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 16px;
  font-size: 12px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.preset-tag-btn:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

.preset-tag-btn.selected {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

/* 单选框样式 */
.radio-group {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.radio-option {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.radio-option:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.radio-option input[type="radio"] {
  margin-top: 2px;
}

.radio-content {
  flex: 1;
}

.radio-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.radio-title {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
}

.radio-description {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
}

/* 复选框样式 */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.checkbox-option {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
}

.checkbox-content {
  flex: 1;
}

.checkbox-title {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #111827;
  margin-bottom: 2px;
}

.checkbox-description {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
}

/* 表单操作 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #f3f4f6;
}

/* 侧边栏提示 */
.sidebar-tips {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tip-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.tip-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f0f4ff;
  color: #4338ca;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  font-size: 18px;
}

.tip-card h4 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 12px 0;
}

.tip-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tip-list li {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
  padding-left: 16px;
  position: relative;
}

.tip-list li:last-child {
  margin-bottom: 0;
}

.tip-list li::before {
  content: '•';
  color: #3b82f6;
  position: absolute;
  left: 0;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .create-form-container {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .sidebar-tips {
    order: -1;
  }
}

@media (max-width: 768px) {
  .create-team-page {
    padding: 10px 0;
  }

  .page-header {
    padding: 20px;
    margin-bottom: 20px;
  }

  .header-content {
    gap: 15px;
  }

  .header-text h1 {
    font-size: 24px;
  }

  .form-card {
    padding: 20px;
  }

  .team-form {
    gap: 25px;
  }

  .form-section {
    padding-bottom: 25px;
  }

  .section-title {
    font-size: 18px;
  }

  .avatar-upload {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .radio-group,
  .checkbox-group {
    gap: 12px;
  }

  .form-actions {
    flex-direction: column-reverse;
  }

  .form-actions .btn {
    width: 100%;
  }
}
</style>
