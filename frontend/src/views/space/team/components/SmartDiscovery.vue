<template>
  <div class="smart-discovery">
    <!-- 智能搜索栏 -->
    <div class="search-header">
      <div class="search-container">
        <div class="search-input-wrapper">
          <i class="fas fa-search search-icon"></i>
          <input
            v-model="localSearchQuery"
            type="text"
            placeholder="搜索团队、技能、项目..."
            class="search-input"
            @input="handleSearchInput"
            @focus="showSearchSuggestions = true"
            @blur="hideSearchSuggestions"
          >
          <button
            v-if="localSearchQuery"
            class="clear-search"
            @click="clearSearch"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <!-- 搜索建议 -->
        <div v-if="showSearchSuggestions && searchSuggestions.length" class="search-suggestions">
          <div
            v-for="suggestion in searchSuggestions"
            :key="suggestion.id"
            class="suggestion-item"
            @mousedown="applySuggestion(suggestion)"
          >
            <i :class="suggestion.icon"></i>
            <span>{{ suggestion.text }}</span>
            <span class="suggestion-type">{{ suggestion.type }}</span>
          </div>
        </div>
      </div>

      <!-- 视图控制 -->
      <div class="view-controls">
        <div class="sort-dropdown">
          <select v-model="localSortBy" @change="handleSortChange" class="sort-select">
            <option value="relevance">智能推荐</option>
            <option value="activity">最近活跃</option>
            <option value="members">成员最多</option>
            <option value="created">最新创建</option>
            <option value="match">匹配度</option>
          </select>
        </div>
        
        <div class="view-mode-toggle">
          <button
            v-for="mode in viewModes"
            :key="mode.key"
            class="view-mode-btn"
            :class="{ active: viewMode === mode.key }"
            @click="$emit('view-change', mode.key)"
            :title="mode.label"
          >
            <i :class="mode.icon"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 智能内容区域 -->
    <div class="discovery-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-grid">
          <div v-for="i in 8" :key="i" class="loading-card"></div>
        </div>
      </div>

      <!-- 团队网格 -->
      <div v-else-if="teams.length" class="teams-container" :class="viewMode">
        <!-- 智能网格视图 -->
        <div v-if="viewMode === 'smart-grid'" class="smart-grid">
          <SmartTeamCard
            v-for="team in teams"
            :key="team.id"
            :team="team"
            :view-mode="viewMode"
            @action="handleTeamAction"
          />
        </div>

        <!-- 时间线视图 -->
        <div v-else-if="viewMode === 'timeline'" class="timeline-view">
          <TimelineTeamCard
            v-for="team in teams"
            :key="team.id"
            :team="team"
            @action="handleTeamAction"
          />
        </div>

        <!-- 看板视图 -->
        <div v-else-if="viewMode === 'kanban'" class="kanban-view">
          <KanbanColumn
            v-for="category in teamCategories"
            :key="category.key"
            :title="category.label"
            :teams="getTeamsByCategory(category.key)"
            @team-action="handleTeamAction"
          />
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <div class="empty-icon">
          <i class="fas fa-search"></i>
        </div>
        <h3 class="empty-title">未找到匹配的团队</h3>
        <p class="empty-description">
          尝试调整搜索条件或筛选器，或者创建一个新的团队
        </p>
        <button class="empty-action" @click="$emit('team-action', { type: 'create' })">
          <i class="fas fa-plus"></i>
          创建新团队
        </button>
      </div>
    </div>

    <!-- 智能推荐浮层 -->
    <div v-if="showRecommendations" class="recommendations-overlay">
      <div class="recommendations-panel">
        <div class="panel-header">
          <h4>智能推荐</h4>
          <button class="close-btn" @click="showRecommendations = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="recommendations-content">
          <div
            v-for="recommendation in intelligentRecommendations"
            :key="recommendation.id"
            class="recommendation-card"
            @click="handleRecommendationClick(recommendation)"
          >
            <div class="recommendation-avatar">
              <img v-if="recommendation.avatar" :src="recommendation.avatar" :alt="recommendation.name" />
              <div v-else class="avatar-placeholder">
                <i class="fas fa-users"></i>
              </div>
            </div>
            <div class="recommendation-info">
              <h5>{{ recommendation.name }}</h5>
              <p>{{ recommendation.reason }}</p>
              <div class="recommendation-score">
                匹配度: {{ recommendation.matchScore }}%
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, watch } from 'vue'
import SmartTeamCard from './SmartTeamCard.vue'
import TimelineTeamCard from './TimelineTeamCard.vue'
import KanbanColumn from './KanbanColumn.vue'

export default {
  name: 'SmartDiscovery',
  components: {
    SmartTeamCard,
    TimelineTeamCard,
    KanbanColumn
  },
  props: {
    teams: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    viewMode: {
      type: String,
      default: 'smart-grid'
    },
    searchQuery: {
      type: String,
      default: ''
    },
    activeFilters: {
      type: Set,
      default: () => new Set()
    },
    sortOptions: {
      type: Object,
      default: () => ({ by: 'relevance', order: 'desc' })
    }
  },
  emits: ['search', 'filter', 'sort', 'view-change', 'team-action'],
  setup(props, { emit }) {
    // 响应式数据
    const localSearchQuery = ref(props.searchQuery)
    const localSortBy = ref(props.sortOptions.by)
    const showSearchSuggestions = ref(false)
    const showRecommendations = ref(false)
    const searchSuggestions = ref([])
    const intelligentRecommendations = ref([])

    // 视图模式选项
    const viewModes = ref([
      { key: 'smart-grid', label: '智能网格', icon: 'fas fa-th' },
      { key: 'timeline', label: '时间线', icon: 'fas fa-stream' },
      { key: 'kanban', label: '看板', icon: 'fas fa-columns' }
    ])

    // 团队分类
    const teamCategories = ref([
      { key: 'ai-tech', label: 'AI与技术' },
      { key: 'design', label: '设计创意' },
      { key: 'product', label: '产品运营' },
      { key: 'development', label: '技术开发' },
      { key: 'other', label: '其他' }
    ])

    // 计算属性
    const getTeamsByCategory = (category) => {
      return props.teams.filter(team => {
        switch (category) {
          case 'ai-tech':
            return team.tags?.some(tag => ['AI', 'AIGC', '机器学习'].includes(tag))
          case 'design':
            return team.tags?.some(tag => ['设计', 'UI', 'UX'].includes(tag))
          case 'product':
            return team.tags?.some(tag => ['产品', '运营', '市场'].includes(tag))
          case 'development':
            return team.tags?.some(tag => ['前端', '后端', '开发'].includes(tag))
          default:
            return !team.category || team.category === '综合团队'
        }
      })
    }

    // 方法
    const handleSearchInput = debounce(() => {
      emit('search', localSearchQuery.value)
      generateSearchSuggestions()
    }, 300)

    const generateSearchSuggestions = () => {
      if (!localSearchQuery.value || localSearchQuery.value.length < 2) {
        searchSuggestions.value = []
        return
      }

      const query = localSearchQuery.value.toLowerCase()
      const suggestions = []

      // 团队名称建议
      props.teams.forEach(team => {
        if (team.name.toLowerCase().includes(query)) {
          suggestions.push({
            id: `team-${team.id}`,
            text: team.name,
            type: '团队',
            icon: 'fas fa-users'
          })
        }
      })

      // 标签建议
      const allTags = [...new Set(props.teams.flatMap(team => team.tags || []))]
      allTags.forEach(tag => {
        if (tag.toLowerCase().includes(query)) {
          suggestions.push({
            id: `tag-${tag}`,
            text: tag,
            type: '标签',
            icon: 'fas fa-tag'
          })
        }
      })

      searchSuggestions.value = suggestions.slice(0, 6)
    }

    const applySuggestion = (suggestion) => {
      localSearchQuery.value = suggestion.text
      emit('search', suggestion.text)
      showSearchSuggestions.value = false
    }

    const clearSearch = () => {
      localSearchQuery.value = ''
      emit('search', '')
      searchSuggestions.value = []
    }

    const hideSearchSuggestions = () => {
      setTimeout(() => {
        showSearchSuggestions.value = false
      }, 200)
    }

    const handleSortChange = () => {
      emit('sort', { by: localSortBy.value, order: 'desc' })
    }

    const handleTeamAction = (action) => {
      emit('team-action', action)
    }

    const handleRecommendationClick = (recommendation) => {
      emit('team-action', { type: 'preview', team: recommendation })
      showRecommendations.value = false
    }

    // 防抖函数
    function debounce(func, wait) {
      let timeout
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout)
          func(...args)
        }
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
      }
    }

    // 监听器
    watch(() => props.searchQuery, (newQuery) => {
      localSearchQuery.value = newQuery
    })

    watch(() => props.sortOptions.by, (newSort) => {
      localSortBy.value = newSort
    })

    return {
      localSearchQuery,
      localSortBy,
      showSearchSuggestions,
      showRecommendations,
      searchSuggestions,
      intelligentRecommendations,
      viewModes,
      teamCategories,
      getTeamsByCategory,
      handleSearchInput,
      applySuggestion,
      clearSearch,
      hideSearchSuggestions,
      handleSortChange,
      handleTeamAction,
      handleRecommendationClick
    }
  }
}
</script>

<style scoped>
/* 🔍 智能发现组件样式 */

.smart-discovery {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* ===== 搜索头部 ===== */
.search-header {
  padding: 24px 24px 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
  margin-bottom: 24px;
}

.search-container {
  flex: 1;
  position: relative;
  max-width: 400px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 16px;
  color: #9ca3af;
  font-size: 16px;
  z-index: 2;
}

.search-input {
  width: 100%;
  padding: 14px 16px 14px 48px;
  border: 2px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px;
  font-size: 15px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  outline: none;
}

.search-input:focus {
  border-color: #6366f1;
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
  background: white;
}

.clear-search {
  position: absolute;
  right: 12px;
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(107, 114, 128, 0.1);
  border-radius: 50%;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.clear-search:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* ===== 搜索建议 ===== */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  z-index: 100;
  margin-top: 4px;
  overflow: hidden;
  backdrop-filter: blur(20px);
}

.suggestion-item {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover {
  background: rgba(99, 102, 241, 0.05);
}

.suggestion-item i {
  color: #6366f1;
  font-size: 14px;
  width: 16px;
}

.suggestion-type {
  margin-left: auto;
  font-size: 11px;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== 视图控制 ===== */
.view-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sort-dropdown {
  position: relative;
}

.sort-select {
  padding: 10px 16px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.9);
  color: #4b5563;
  font-size: 14px;
  cursor: pointer;
  outline: none;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.sort-select:focus {
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.view-mode-toggle {
  display: flex;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 4px;
  border: 1px solid rgba(226, 232, 240, 0.5);
  backdrop-filter: blur(10px);
}

.view-mode-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: transparent;
  border-radius: 8px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.view-mode-btn:hover {
  color: #6366f1;
  background: rgba(99, 102, 241, 0.1);
}

.view-mode-btn.active {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

/* ===== 发现内容区域 ===== */
.discovery-content {
  flex: 1;
  padding: 0 24px 24px;
  overflow-y: auto;
}

/* ===== 加载状态 ===== */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.loading-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  width: 100%;
  max-width: 1200px;
}

.loading-card {
  height: 380px;
  background: linear-gradient(90deg,
    rgba(226, 232, 240, 0.3) 0%,
    rgba(226, 232, 240, 0.5) 50%,
    rgba(226, 232, 240, 0.3) 100%);
  border-radius: 20px;
  animation: loading-shimmer 1.5s ease-in-out infinite;
}

@keyframes loading-shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

/* ===== 团队容器 ===== */
.teams-container {
  width: 100%;
}

.smart-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
}

.timeline-view {
  max-width: 800px;
  margin: 0 auto;
}

.kanban-view {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

/* ===== 空状态 ===== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  color: #6b7280;
}

.empty-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: #9ca3af;
  margin-bottom: 20px;
}

.empty-title {
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 16px;
  color: #6b7280;
  margin: 0 0 24px 0;
  line-height: 1.5;
  max-width: 400px;
}

.empty-action {
  padding: 12px 24px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.empty-action:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.3);
}

/* ===== 智能推荐浮层 ===== */
.recommendations-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200;
  padding: 20px;
}

.recommendations-panel {
  background: white;
  border-radius: 20px;
  max-width: 500px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.panel-header {
  padding: 20px 20px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
  margin-bottom: 20px;
}

.panel-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.recommendations-content {
  padding: 0 20px 20px;
}

.recommendation-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 12px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.recommendation-card:hover {
  background: rgba(99, 102, 241, 0.05);
  border-color: #6366f1;
  transform: translateY(-1px);
}

.recommendation-avatar {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  flex-shrink: 0;
}

.recommendation-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.recommendation-info {
  flex: 1;
}

.recommendation-info h5 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.recommendation-info p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}

.recommendation-score {
  font-size: 12px;
  color: #10b981;
  font-weight: 600;
}

/* ===== 响应式设计 ===== */
@media (max-width: 1200px) {
  .search-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .search-container {
    max-width: none;
  }

  .view-controls {
    justify-content: space-between;
  }

  .smart-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .discovery-content {
    padding: 0 16px 16px;
  }

  .search-header {
    padding: 16px 16px 0;
  }

  .smart-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .kanban-view {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .view-controls {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
