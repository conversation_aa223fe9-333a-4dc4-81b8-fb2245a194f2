<template>
  <div 
    class="team-card" 
    :class="{ 
      'list-view': viewMode === 'list',
      'is-member': team.isMember,
      'is-starred': team.isStarred,
      'is-public': team.isPublic
    }"
    @click="handleCardClick"
  >
    <!-- 卡片头部 -->
    <div class="card-header">
      <div class="team-avatar">
        <img v-if="team.avatar" :src="team.avatar" :alt="team.name" />
        <div v-else class="avatar-placeholder">
          <i class="fas fa-users"></i>
        </div>
        
        <!-- 状态指示器 -->
        <div v-if="team.isMember" class="status-badge member">
          <i class="fas fa-check"></i>
        </div>
        <div v-else-if="team.isPublic" class="status-badge public">
          <i class="fas fa-globe"></i>
        </div>
        <div v-else class="status-badge private">
          <i class="fas fa-lock"></i>
        </div>
      </div>

      <div class="team-info">
        <h3 class="team-name">{{ team.name }}</h3>
        <p class="team-description">{{ team.description || '暂无描述' }}</p>
      </div>

      <div class="card-actions">
        <button
          class="action-btn star-btn"
          :class="{ active: team.isStarred }"
          @click.stop="$emit('star', team)"
          :title="team.isStarred ? '取消收藏' : '收藏团队'"
        >
          <i class="fas fa-star"></i>
        </button>
        
        <button
          class="action-btn preview-btn"
          @click.stop="$emit('preview', team)"
          title="快速预览"
        >
          <i class="fas fa-eye"></i>
        </button>
      </div>
    </div>

    <!-- 卡片主体 -->
    <div class="card-body">
      <!-- 团队标签 -->
      <div v-if="team.tags && team.tags.length" class="team-tags">
        <span
          v-for="tag in team.tags.slice(0, 3)"
          :key="tag"
          class="tag"
        >
          {{ tag }}
        </span>
        <span v-if="team.tags.length > 3" class="tag more">
          +{{ team.tags.length - 3 }}
        </span>
      </div>

      <!-- 团队统计 -->
      <div class="team-stats">
        <div class="stat-item">
          <i class="fas fa-users"></i>
          <span class="stat-value">{{ team.membersCount || 0 }}</span>
          <span class="stat-label">成员</span>
        </div>
        <div class="stat-item">
          <i class="fas fa-file-alt"></i>
          <span class="stat-value">{{ team.articlesCount || 0 }}</span>
          <span class="stat-label">文章</span>
        </div>
        <div class="stat-item">
          <i class="fas fa-heart"></i>
          <span class="stat-value">{{ formatNumber(team.likesCount || 0) }}</span>
          <span class="stat-label">点赞</span>
        </div>
        <div class="stat-item">
          <i class="fas fa-eye"></i>
          <span class="stat-value">{{ formatNumber(team.viewsCount || 0) }}</span>
          <span class="stat-label">浏览</span>
        </div>
      </div>
    </div>

    <!-- 卡片底部 -->
    <div class="card-footer">
      <div class="team-meta">
        <span class="created-time">
          <i class="fas fa-calendar"></i>
          {{ formatDate(team.createdAt) }}
        </span>
        <span v-if="team.lastActivityAt" class="last-activity">
          <i class="fas fa-clock"></i>
          {{ formatLastActivity(team.lastActivityAt) }}
        </span>
      </div>

      <div class="primary-actions">
        <button
          v-if="team.isMember"
          class="primary-btn enter"
          @click.stop="$emit('enter', team)"
        >
          <i class="fas fa-arrow-right"></i>
          进入团队
        </button>
        <button
          v-else
          class="primary-btn join"
          @click.stop="$emit('join', team)"
        >
          <i class="fas fa-plus"></i>
          {{ team.isPublic ? '立即加入' : '申请加入' }}
        </button>
      </div>
    </div>

    <!-- 悬停效果 -->
    <div class="hover-overlay">
      <div class="hover-actions">
        <button class="hover-btn" @click.stop="$emit('preview', team)">
          <i class="fas fa-eye"></i>
          快速预览
        </button>
        <button 
          v-if="!team.isMember" 
          class="hover-btn primary" 
          @click.stop="$emit('join', team)"
        >
          <i class="fas fa-plus"></i>
          {{ team.isPublic ? '立即加入' : '申请加入' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TeamCardRedesigned',
  props: {
    team: {
      type: Object,
      required: true
    },
    viewMode: {
      type: String,
      default: 'grid'
    }
  },
  emits: ['join', 'star', 'enter', 'preview'],
  setup(props, { emit }) {
    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }

    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      
      const date = new Date(dateString)
      const now = new Date()
      const diffTime = now - date
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
      
      if (diffDays === 0) {
        return '今天创建'
      } else if (diffDays === 1) {
        return '昨天创建'
      } else if (diffDays < 30) {
        return `${diffDays}天前创建`
      } else {
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        })
      }
    }

    const formatLastActivity = (dateString) => {
      if (!dateString) return '暂无活动'
      
      const date = new Date(dateString)
      const now = new Date()
      const diffTime = now - date
      const diffHours = Math.floor(diffTime / (1000 * 60 * 60))
      const diffDays = Math.floor(diffHours / 24)
      
      if (diffHours < 1) {
        return '刚刚活跃'
      } else if (diffHours < 24) {
        return `${diffHours}小时前活跃`
      } else if (diffDays < 7) {
        return `${diffDays}天前活跃`
      } else {
        return date.toLocaleDateString('zh-CN', {
          month: 'short',
          day: 'numeric'
        }) + '活跃'
      }
    }

    const handleCardClick = () => {
      // 网格视图点击预览，列表视图点击进入
      if (props.viewMode === 'list') {
        if (props.team.isMember) {
          emit('enter', props.team)
        } else {
          emit('preview', props.team)
        }
      } else {
        emit('preview', props.team)
      }
    }

    return {
      formatNumber,
      formatDate,
      formatLastActivity,
      handleCardClick
    }
  }
}
</script>

<style scoped>
/* 🎨 团队卡片重新设计 - 参考AI工具箱卡片风格 */

.team-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.team-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  border-color: rgba(99, 102, 241, 0.3);
}

/* 特殊状态样式 */
.team-card.is-member {
  border-color: rgba(16, 185, 129, 0.3);
  background: linear-gradient(145deg,
    rgba(236, 253, 245, 0.5) 0%,
    rgba(255, 255, 255, 1) 100%);
}

.team-card.is-starred {
  border-color: rgba(245, 158, 11, 0.3);
  background: linear-gradient(145deg,
    rgba(255, 251, 235, 0.5) 0%,
    rgba(255, 255, 255, 1) 100%);
}

/* 列表视图样式 */
.team-card.list-view {
  flex-direction: row;
  height: auto;
  min-height: 120px;
  border-radius: 16px;
}

.team-card.list-view .card-header {
  flex-direction: row;
  align-items: center;
  flex: 1;
  padding: 20px;
}

.team-card.list-view .team-avatar {
  width: 60px;
  height: 60px;
  margin-right: 16px;
  flex-shrink: 0;
}

.team-card.list-view .team-info {
  flex: 1;
  margin-right: 16px;
}

.team-card.list-view .card-body {
  padding: 20px;
  border-left: 1px solid #f1f5f9;
  min-width: 200px;
}

.team-card.list-view .card-footer {
  padding: 20px;
  border-left: 1px solid #f1f5f9;
  min-width: 180px;
}

/* ===== 卡片头部 ===== */
.card-header {
  padding: 24px 24px 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: relative;
}

.team-avatar {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: 3px solid white;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
  align-self: center;
}

.team-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28px;
}

.status-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: white;
}

.status-badge.member {
  background: linear-gradient(135deg, #10b981, #059669);
}

.status-badge.public {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.status-badge.private {
  background: linear-gradient(135deg, #6b7280, #4b5563);
}

.team-info {
  text-align: center;
  flex: 1;
}

.team-name {
  font-size: 18px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.team-description {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 42px;
}

.card-actions {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  backdrop-filter: blur(10px);
}

.action-btn:hover {
  background: white;
  color: #6366f1;
  transform: scale(1.1);
}

.star-btn.active {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

/* ===== 卡片主体 ===== */
.card-body {
  padding: 0 24px 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.team-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  justify-content: center;
  min-height: 24px;
}

.tag {
  padding: 4px 8px;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  color: #4b5563;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.tag.more {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.team-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-top: auto;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 8px;
  background: #f8fafc;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.stat-item:hover {
  background: #f1f5f9;
  transform: translateY(-1px);
}

.stat-item i {
  color: #6366f1;
  font-size: 16px;
}

.stat-value {
  font-size: 16px;
  font-weight: 700;
  color: #1a202c;
}

.stat-label {
  font-size: 10px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== 卡片底部 ===== */
.card-footer {
  padding: 16px 24px 24px;
  border-top: 1px solid #f1f5f9;
  background: #fafbfc;
}

.team-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-size: 12px;
  color: #64748b;
}

.created-time,
.last-activity {
  display: flex;
  align-items: center;
  gap: 4px;
}

.primary-actions {
  display: flex;
  justify-content: center;
}

.primary-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  min-width: 120px;
  justify-content: center;
}

.primary-btn.enter {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.primary-btn.join {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
}

.primary-btn.enter:hover {
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.4);
}

/* ===== 悬停效果 ===== */
.hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(99, 102, 241, 0.95) 0%,
    rgba(139, 92, 246, 0.95) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  backdrop-filter: blur(10px);
  border-radius: 20px;
}

.team-card:hover .hover-overlay {
  opacity: 1;
}

.team-card.list-view .hover-overlay {
  border-radius: 16px;
}

.hover-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.hover-btn {
  padding: 12px 24px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  color: white;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  backdrop-filter: blur(10px);
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.hover-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.hover-btn.primary {
  background: rgba(255, 255, 255, 0.9);
  color: #6366f1;
  border-color: transparent;
}

.hover-btn.primary:hover {
  background: white;
  transform: scale(1.05);
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  .team-card {
    border-radius: 16px;
  }

  .card-header {
    padding: 20px 20px 12px;
  }

  .team-avatar {
    width: 60px;
    height: 60px;
    border-radius: 12px;
  }

  .avatar-placeholder {
    font-size: 20px;
  }

  .status-badge {
    width: 20px;
    height: 20px;
    font-size: 8px;
  }

  .team-name {
    font-size: 16px;
  }

  .team-description {
    font-size: 13px;
  }

  .card-body {
    padding: 0 20px 12px;
  }

  .team-stats {
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
  }

  .stat-item {
    padding: 8px 4px;
  }

  .stat-item i {
    font-size: 14px;
  }

  .stat-value {
    font-size: 14px;
  }

  .stat-label {
    font-size: 9px;
  }

  .card-footer {
    padding: 12px 20px 20px;
  }

  .team-meta {
    flex-direction: column;
    gap: 4px;
    align-items: flex-start;
    margin-bottom: 12px;
  }

  .primary-btn {
    padding: 8px 16px;
    font-size: 13px;
    min-width: 100px;
  }

  /* 列表视图移动端优化 */
  .team-card.list-view {
    flex-direction: column;
    height: auto;
  }

  .team-card.list-view .card-header {
    flex-direction: column;
    text-align: center;
  }

  .team-card.list-view .team-avatar {
    margin-right: 0;
    margin-bottom: 12px;
    align-self: center;
  }

  .team-card.list-view .team-info {
    margin-right: 0;
    text-align: center;
  }

  .team-card.list-view .card-body,
  .team-card.list-view .card-footer {
    border-left: none;
    border-top: 1px solid #f1f5f9;
    min-width: auto;
  }

  .hover-actions {
    flex-direction: row;
    gap: 8px;
  }

  .hover-btn {
    padding: 8px 16px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .card-header {
    padding: 16px 16px 8px;
  }

  .card-actions {
    top: 12px;
    right: 12px;
  }

  .action-btn {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .team-avatar {
    width: 50px;
    height: 50px;
    border-radius: 10px;
  }

  .avatar-placeholder {
    font-size: 18px;
  }

  .team-name {
    font-size: 15px;
  }

  .team-description {
    font-size: 12px;
  }

  .card-body {
    padding: 0 16px 8px;
  }

  .team-stats {
    gap: 6px;
  }

  .stat-item {
    padding: 6px 2px;
  }

  .stat-item i {
    font-size: 12px;
  }

  .stat-value {
    font-size: 12px;
  }

  .stat-label {
    font-size: 8px;
  }

  .card-footer {
    padding: 8px 16px 16px;
  }

  .team-meta {
    font-size: 11px;
    margin-bottom: 8px;
  }

  .primary-btn {
    padding: 6px 12px;
    font-size: 12px;
    min-width: 80px;
  }

  .hover-btn {
    padding: 6px 12px;
    font-size: 11px;
  }
}
</style>
