<template>
  <div class="immersive-preview-overlay" @click.self="$emit('close')">
    <div class="preview-modal">
      <div class="modal-header">
        <h3>{{ team.name }}</h3>
        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="modal-content">
        <div class="team-overview">
          <div class="team-avatar-large">
            <img v-if="team.avatar" :src="team.avatar" :alt="team.name" />
            <div v-else class="avatar-placeholder">
              <i class="fas fa-users"></i>
            </div>
          </div>
          <div class="team-details">
            <p>{{ team.description || '暂无描述' }}</p>
            <div class="team-stats">
              <span>{{ team.membersCount || 0 }} 成员</span>
              <span>{{ team.contentCount || 0 }} 内容</span>
              <span>{{ team.viewsCount || 0 }} 浏览</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="modal-actions">
        <button class="btn secondary" @click="$emit('close')">
          取消
        </button>
        <button class="btn primary" @click="$emit('join', team)">
          {{ team.isPublic ? '立即加入' : '申请加入' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ImmersivePreview',
  props: {
    team: {
      type: Object,
      required: true
    },
    user: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['close', 'join', 'follow', 'share']
}
</script>

<style scoped>
.immersive-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.preview-modal {
  background: white;
  border-radius: 20px;
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.modal-header {
  padding: 24px 24px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(107, 114, 128, 0.1);
  border-radius: 50%;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.modal-content {
  padding: 24px;
}

.team-overview {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.team-avatar-large {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  overflow: hidden;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  flex-shrink: 0;
}

.team-avatar-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
}

.team-details {
  flex: 1;
}

.team-details p {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #4b5563;
  line-height: 1.6;
}

.team-stats {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #6b7280;
}

.modal-actions {
  padding: 0 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn.secondary {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

.btn.secondary:hover {
  background: rgba(107, 114, 128, 0.2);
}

.btn.primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}
</style>
