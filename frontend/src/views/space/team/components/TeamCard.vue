<template>
  <div 
    class="team-card" 
    :class="[
      `view-${viewMode}`,
      {
        'is-member': team.isMember,
        'is-admin': team.isAdmin,
        'is-public': team.isPublic,
        'has-match-score': showMatchScore && team.matchScore > 0
      }
    ]"
    @click="handleCardClick"
  >
    <!-- 匹配度指示器 -->
    <div v-if="showMatchScore && team.matchScore > 0" class="match-indicator">
      <div class="match-score">{{ team.matchScore }}% 匹配</div>
    </div>

    <!-- 卡片头部 -->
    <div class="card-header">
      <!-- 团队头像 -->
      <div class="team-avatar" :class="{ 'has-activity': team.isActive }">
        <img v-if="team.avatar" :src="team.avatar" :alt="team.name" />
        <div v-else class="avatar-placeholder">
          <i class="fas fa-users"></i>
        </div>
        <div v-if="team.isActive" class="activity-indicator">
          <div class="pulse-ring"></div>
          <div class="pulse-dot"></div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="card-actions">
        <button
          class="action-btn star-btn"
          :class="{ active: team.isStarred }"
          @click.stop="$emit('star', team)"
          :title="team.isStarred ? '取消收藏' : '收藏团队'"
        >
          <i class="fas fa-star"></i>
        </button>
        
        <button
          class="action-btn preview-btn"
          @click.stop="$emit('preview', team)"
          title="快速预览"
        >
          <i class="fas fa-eye"></i>
        </button>
      </div>
    </div>

    <!-- 卡片主体 -->
    <div class="card-body">
      <!-- 团队信息 -->
      <div class="team-info">
        <h3 class="team-name">{{ team.name }}</h3>
        <p v-if="team.description" class="team-description">{{ team.description }}</p>
        
        <!-- 团队标签 -->
        <div v-if="team.tags && team.tags.length" class="team-tags">
          <span
            v-for="tag in displayTags"
            :key="tag"
            class="tag"
          >
            {{ tag }}
          </span>
          <span v-if="team.tags.length > maxTags" class="tag more">
            +{{ team.tags.length - maxTags }}
          </span>
        </div>
      </div>

      <!-- 团队状态徽章 -->
      <div class="team-badges">
        <span v-if="team.isAdmin" class="badge admin">
          <i class="fas fa-crown"></i>
          管理员
        </span>
        <span v-else-if="team.isMember" class="badge member">
          <i class="fas fa-check-circle"></i>
          已加入
        </span>
        <span class="badge visibility" :class="team.isPublic ? 'public' : 'private'">
          <i :class="team.isPublic ? 'fas fa-globe' : 'fas fa-lock'"></i>
          {{ team.isPublic ? '公开' : '私有' }}
        </span>
      </div>

      <!-- 团队统计 -->
      <div class="team-stats">
        <div class="stat-item">
          <i class="fas fa-users"></i>
          <span class="stat-value">{{ formatNumber(team.membersCount || 0) }}</span>
          <span class="stat-label">成员</span>
        </div>
        <div class="stat-item">
          <i class="fas fa-file-alt"></i>
          <span class="stat-value">{{ formatNumber(team.contentCount || 0) }}</span>
          <span class="stat-label">内容</span>
        </div>
        <div class="stat-item">
          <i class="fas fa-eye"></i>
          <span class="stat-value">{{ formatNumber(team.viewsCount || 0) }}</span>
          <span class="stat-label">浏览</span>
        </div>
      </div>
    </div>

    <!-- 卡片底部 -->
    <div class="card-footer">
      <!-- 成员预览 -->
      <div class="members-preview">
        <div class="member-avatars">
          <img
            v-for="member in displayMembers"
            :key="member.id"
            :src="member.avatar"
            :alt="member.name"
            class="member-avatar"
            :title="member.name"
          />
          <div v-if="team.membersCount > displayMembers.length" class="more-members">
            +{{ team.membersCount - displayMembers.length }}
          </div>
        </div>
        <div class="last-activity">
          <i class="fas fa-clock"></i>
          {{ formatLastActivity(team.lastActivityAt) }}
        </div>
      </div>

      <!-- 主要操作按钮 -->
      <div class="primary-actions">
        <button
          v-if="team.isMember"
          class="primary-btn enter"
          @click.stop="$emit('enter', team)"
        >
          <i class="fas fa-arrow-right"></i>
          进入团队
        </button>
        <button
          v-else
          class="primary-btn join"
          @click.stop="$emit('join', team)"
        >
          <i class="fas fa-plus"></i>
          {{ team.isPublic ? '立即加入' : '申请加入' }}
        </button>
      </div>
    </div>

    <!-- 悬停效果遮罩 -->
    <div class="hover-overlay">
      <div class="hover-content">
        <button class="hover-btn" @click.stop="$emit('preview', team)">
          <i class="fas fa-search-plus"></i>
          快速预览
        </button>
        <button class="hover-btn" @click.stop="handleCardClick">
          <i class="fas fa-arrow-right"></i>
          查看详情
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'TeamCard',
  props: {
    team: {
      type: Object,
      required: true
    },
    viewMode: {
      type: String,
      default: 'card',
      validator: value => ['card', 'list', 'compact'].includes(value)
    },
    showMatchScore: {
      type: Boolean,
      default: false
    }
  },
  emits: ['join', 'star', 'enter', 'preview'],
  setup(props) {
    const maxTags = computed(() => {
      switch (props.viewMode) {
        case 'compact': return 2
        case 'list': return 5
        default: return 3
      }
    })

    const displayTags = computed(() => {
      return props.team.tags?.slice(0, maxTags.value) || []
    })

    const displayMembers = computed(() => {
      return props.team.members?.slice(0, 4) || []
    })

    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }

    const formatLastActivity = (timestamp) => {
      if (!timestamp) return '暂无活动'
      
      const now = new Date()
      const activity = new Date(timestamp)
      const diffMs = now - activity
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
      const diffMinutes = Math.floor(diffMs / (1000 * 60))

      if (diffDays > 7) {
        return activity.toLocaleDateString()
      } else if (diffDays > 0) {
        return `${diffDays}天前`
      } else if (diffHours > 0) {
        return `${diffHours}小时前`
      } else if (diffMinutes > 0) {
        return `${diffMinutes}分钟前`
      } else {
        return '刚刚'
      }
    }

    const handleCardClick = () => {
      if (props.team.isMember) {
        // 如果是成员，直接进入团队
        props.$emit('enter', props.team)
      } else {
        // 否则显示预览
        props.$emit('preview', props.team)
      }
    }

    return {
      maxTags,
      displayTags,
      displayMembers,
      formatNumber,
      formatLastActivity,
      handleCardClick
    }
  }
}
</script>

<style scoped>
/* 🎨 现代化团队卡片样式 */

/* ===== 基础卡片样式 ===== */
.team-card {
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 100%);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.2s ease;
  position: relative;
  cursor: pointer;
  backdrop-filter: blur(10px);
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.05),
    0 1px 2px rgba(0, 0, 0, 0.1);

  /* 固定高度确保一致性 */
  height: 420px;
  display: flex;
  flex-direction: column;
}

.team-card:hover {
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.3);
}

.team-card.is-member {
  border-color: rgba(16, 185, 129, 0.3);
  background: linear-gradient(145deg,
    rgba(236, 253, 245, 0.95) 0%,
    rgba(209, 250, 229, 0.9) 100%);
}

.team-card.is-admin {
  border-color: rgba(245, 158, 11, 0.3);
  background: linear-gradient(145deg,
    rgba(255, 251, 235, 0.95) 0%,
    rgba(254, 243, 199, 0.9) 100%);
}

/* ===== 匹配度指示器 ===== */
.match-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 10;
}

.match-score {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
}

/* ===== 卡片头部 ===== */
.card-header {
  padding: 20px 20px 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.team-avatar {
  width: 64px;
  height: 64px;
  border-radius: 18px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg,
    #667eea 0%,
    #764ba2 50%,
    #f093fb 100%);
  border: 3px solid white;
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.1),
    0 4px 12px rgba(102, 126, 234, 0.15);
  transition: all 0.3s ease;
}

.team-avatar:hover {
  box-shadow:
    0 6px 16px rgba(0, 0, 0, 0.1),
    0 3px 8px rgba(102, 126, 234, 0.15);
}

.team-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  font-weight: 600;
}

.activity-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 18px;
  height: 18px;
}

.pulse-dot {
  width: 18px;
  height: 18px;
  background: linear-gradient(135deg, #10b981, #059669);
  border: 3px solid white;
  border-radius: 50%;
  position: absolute;
}

.pulse-ring {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(16, 185, 129, 0.3);
  border-radius: 50%;
  position: absolute;
  animation: pulse-ring 2s ease-out infinite;
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.card-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.9);
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
  background: rgba(255, 255, 255, 1);
}

.star-btn.active {
  background: linear-gradient(135deg, #f59e0b, #f97316);
  color: white;
  animation: star-glow 2s ease-in-out infinite alternate;
}

@keyframes star-glow {
  0% { box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3); }
  100% { box-shadow: 0 4px 16px rgba(245, 158, 11, 0.5); }
}

.preview-btn:hover {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

/* ===== 卡片主体 ===== */
.card-body {
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  min-height: 0; /* 允许flex子元素收缩 */
}

.team-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
  min-height: 0;
}

.team-name {
  font-size: 18px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  background: linear-gradient(135deg, #1a202c, #2d3748);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* 固定高度确保一致性 */
  height: 48px; /* 2行文字的固定高度 */
}

.team-description {
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  /* 固定高度确保一致性 */
  height: 42px; /* 2行文字的固定高度 */
}

.team-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  /* 固定高度确保一致性 */
  min-height: 28px;
  max-height: 56px; /* 最多2行标签 */
  overflow: hidden;
}

.tag {
  padding: 4px 8px;
  background: linear-gradient(135deg, #e2e8f0, #cbd5e0);
  color: #475569;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  transition: all 0.2s ease;
}

.tag:hover {
  background: linear-gradient(135deg, #cbd5e0, #94a3b8);
  color: #334155;
}

.tag.more {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.team-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  /* 固定高度确保一致性 */
  min-height: 32px;
  align-items: flex-start;
}

.badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  transition: all 0.3s ease;
}

.badge.admin {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  color: #92400e;
  border: 1px solid rgba(245, 158, 11, 0.3);
  animation: admin-glow 3s ease-in-out infinite alternate;
}

.badge.member {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  color: #1e40af;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.badge.visibility.public {
  background: linear-gradient(135deg, #d1fae5, #a7f3d0);
  color: #065f46;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.badge.visibility.private {
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  color: #374151;
  border: 1px solid rgba(107, 114, 128, 0.3);
}

@keyframes admin-glow {
  0% { box-shadow: 0 2px 8px rgba(245, 158, 11, 0.2); }
  100% { box-shadow: 0 4px 16px rgba(245, 158, 11, 0.4); }
}

.team-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  padding: 12px;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 1);
}

.stat-item i {
  color: #667eea;
  font-size: 14px;
}

.stat-value {
  font-size: 16px;
  font-weight: 700;
  color: #1a202c;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 10px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  font-weight: 500;
}

/* ===== 卡片底部 ===== */
.card-footer {
  padding: 16px 20px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  border-top: 1px solid rgba(226, 232, 240, 0.5);
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.5) 0%,
    rgba(255, 255, 255, 0.8) 100%);
  /* 固定在底部 */
  margin-top: auto;
  flex-shrink: 0;
}

.members-preview {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1;
}

.member-avatars {
  display: flex;
  align-items: center;
  gap: -8px;
}

.member-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: 2px solid white;
  object-fit: cover;
  transition: all 0.2s ease;
  margin-left: -8px;
}

.member-avatar:first-child {
  margin-left: 0;
}

.member-avatar:hover {
  z-index: 10;
  position: relative;
}

.more-members {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: linear-gradient(135deg, #e2e8f0, #cbd5e0);
  color: #475569;
  border: 2px solid white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  margin-left: -8px;
}

.last-activity {
  font-size: 11px;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 4px;
}

.last-activity i {
  font-size: 10px;
}

.primary-actions {
  display: flex;
  gap: 8px;
}

.primary-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 12px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  white-space: nowrap;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.primary-btn.enter {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.primary-btn.enter:hover {
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.primary-btn.join {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.primary-btn.join:hover {
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* ===== 悬停遮罩 ===== */
.hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(102, 126, 234, 0.9) 0%,
    rgba(118, 75, 162, 0.9) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.team-card:hover .hover-overlay {
  opacity: 1;
}

.hover-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.hover-btn {
  padding: 12px 24px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  color: white;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  backdrop-filter: blur(10px);
}

.hover-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* ===== 视图模式适配 ===== */
.team-card.view-list {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 16px;
  border-radius: 16px;
  height: 120px; /* 固定列表视图高度 */
}

.team-card.view-list .card-header {
  padding: 0;
  margin-right: 16px;
}

.team-card.view-list .team-avatar {
  width: 48px;
  height: 48px;
}

.team-card.view-list .card-body {
  flex: 1;
  padding: 0;
  flex-direction: row;
  align-items: center;
  gap: 24px;
}

.team-card.view-list .team-stats {
  grid-template-columns: repeat(3, auto);
  gap: 16px;
  background: transparent;
  border: none;
  padding: 0;
}

.team-card.view-list .stat-item {
  flex-direction: row;
  background: transparent;
  padding: 0;
}

.team-card.view-list .card-footer {
  padding: 0;
  border: none;
  background: transparent;
  margin-left: 16px;
}

.team-card.view-compact {
  padding: 12px;
  border-radius: 12px;
  height: 280px; /* 固定紧凑视图高度 */
}

.team-card.view-compact .card-header {
  padding: 0 0 8px 0;
}

.team-card.view-compact .team-avatar {
  width: 40px;
  height: 40px;
}

.team-card.view-compact .card-body {
  padding: 0;
  gap: 8px;
}

.team-card.view-compact .team-name {
  font-size: 14px;
}

.team-card.view-compact .team-stats {
  grid-template-columns: repeat(3, 1fr);
  gap: 6px;
  padding: 8px;
}

.team-card.view-compact .card-footer {
  padding: 8px 0 0 0;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  .team-card {
    border-radius: 16px;
  }

  .card-header {
    padding: 16px 16px 0;
  }

  .team-avatar {
    width: 56px;
    height: 56px;
  }

  .card-body {
    padding: 12px 16px;
  }

  .team-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    padding: 8px;
  }

  .card-footer {
    padding: 12px 16px 16px;
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .primary-actions {
    justify-content: center;
  }

  .primary-btn {
    flex: 1;
    justify-content: center;
  }
}
</style>
