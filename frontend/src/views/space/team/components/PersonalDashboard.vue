<template>
  <div class="personal-dashboard">
    <!-- 用户信息卡片 -->
    <div class="user-card">
      <div class="user-avatar">
        <img v-if="user?.avatar" :src="user.avatar" :alt="user.name" />
        <div v-else class="avatar-placeholder">
          <i class="fas fa-user"></i>
        </div>
        <div class="status-indicator online"></div>
      </div>
      <div class="user-info">
        <h3 class="user-name">{{ user?.name || '用户' }}</h3>
        <p class="user-role">{{ user?.role || '团队成员' }}</p>
      </div>
      <button class="personalize-btn" @click="$emit('quick-action', { type: 'personalize' })">
        <i class="fas fa-cog"></i>
      </button>
    </div>

    <!-- 快速统计 -->
    <div class="quick-stats">
      <div class="stat-card">
        <div class="stat-icon teams">
          <i class="fas fa-users"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ myTeams.length }}</div>
          <div class="stat-label">我的团队</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon activities">
          <i class="fas fa-chart-line"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ recentActivities.length }}</div>
          <div class="stat-label">最近活动</div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <h4 class="section-title">快速操作</h4>
      <div class="action-buttons">
        <button 
          class="action-btn primary"
          @click="$emit('quick-action', { type: 'create-team' })"
        >
          <i class="fas fa-plus"></i>
          <span>创建团队</span>
        </button>
        
        <button 
          class="action-btn secondary"
          @click="$emit('quick-action', { type: 'join-random' })"
        >
          <i class="fas fa-random"></i>
          <span>随机加入</span>
        </button>
        
        <button 
          class="action-btn secondary"
          @click="$emit('quick-action', { type: 'view-my-teams' })"
        >
          <i class="fas fa-eye"></i>
          <span>查看我的</span>
        </button>
      </div>
    </div>

    <!-- 智能筛选器 -->
    <div class="smart-filters">
      <h4 class="section-title">智能筛选</h4>
      <div class="filter-chips">
        <button
          v-for="filter in availableFilters"
          :key="filter.key"
          class="filter-chip"
          :class="{ active: activeFilters.has(filter.key) }"
          @click="toggleFilter(filter.key)"
        >
          <i :class="filter.icon"></i>
          <span>{{ filter.label }}</span>
          <span v-if="filter.count" class="filter-count">{{ filter.count }}</span>
        </button>
      </div>
    </div>

    <!-- 个人推荐 -->
    <div class="personal-recommendations">
      <h4 class="section-title">为你推荐</h4>
      <div class="recommendation-list">
        <div
          v-for="team in recommendations.slice(0, 3)"
          :key="team.id"
          class="recommendation-item"
          @click="$emit('quick-action', { type: 'preview-team', team })"
        >
          <div class="team-avatar-small">
            <img v-if="team.avatar" :src="team.avatar" :alt="team.name" />
            <div v-else class="avatar-placeholder-small">
              <i class="fas fa-users"></i>
            </div>
          </div>
          <div class="team-info-small">
            <h5 class="team-name-small">{{ team.name }}</h5>
            <p class="team-match">{{ team.matchScore }}% 匹配</p>
          </div>
          <div class="recommendation-score">
            <div class="score-bar">
              <div 
                class="score-fill" 
                :style="{ width: `${team.matchScore}%` }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activities">
      <h4 class="section-title">最近活动</h4>
      <div class="activity-list">
        <div
          v-for="activity in recentActivities.slice(0, 4)"
          :key="activity.id"
          class="activity-item"
        >
          <div class="activity-icon">
            <i :class="getActivityIcon(activity.type)"></i>
          </div>
          <div class="activity-content">
            <p class="activity-text">{{ activity.description }}</p>
            <span class="activity-time">{{ formatTime(activity.createdAt) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, ref } from 'vue'

export default {
  name: 'PersonalDashboard',
  props: {
    user: {
      type: Object,
      default: () => ({})
    },
    myTeams: {
      type: Array,
      default: () => []
    },
    recentActivities: {
      type: Array,
      default: () => []
    },
    recommendations: {
      type: Array,
      default: () => []
    }
  },
  emits: ['quick-action', 'filter-change'],
  setup(props, { emit }) {
    const activeFilters = ref(new Set())

    const availableFilters = computed(() => [
      { key: 'my-teams', label: '我的团队', icon: 'fas fa-user-friends', count: props.myTeams.length },
      { key: 'starred', label: '已收藏', icon: 'fas fa-star', count: 0 },
      { key: 'public', label: '公开团队', icon: 'fas fa-globe', count: 0 },
      { key: 'active', label: '活跃团队', icon: 'fas fa-fire', count: 0 },
      { key: 'recommended', label: '推荐团队', icon: 'fas fa-magic', count: props.recommendations.length }
    ])

    const toggleFilter = (filterKey) => {
      if (activeFilters.value.has(filterKey)) {
        activeFilters.value.delete(filterKey)
      } else {
        activeFilters.value.add(filterKey)
      }
      
      emit('filter-change', Array.from(activeFilters.value))
    }

    const getActivityIcon = (type) => {
      const icons = {
        'join': 'fas fa-user-plus',
        'create': 'fas fa-plus',
        'comment': 'fas fa-comment',
        'like': 'fas fa-heart',
        'share': 'fas fa-share',
        'update': 'fas fa-edit'
      }
      return icons[type] || 'fas fa-circle'
    }

    const formatTime = (timestamp) => {
      if (!timestamp) return ''
      
      const now = new Date()
      const time = new Date(timestamp)
      const diffMs = now - time
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
      const diffDays = Math.floor(diffHours / 24)

      if (diffDays > 0) {
        return `${diffDays}天前`
      } else if (diffHours > 0) {
        return `${diffHours}小时前`
      } else {
        return '刚刚'
      }
    }

    return {
      activeFilters,
      availableFilters,
      toggleFilter,
      getActivityIcon,
      formatTime
    }
  }
}
</script>

<style scoped>
/* 🎨 个人化控制面板样式 */

.personal-dashboard {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* ===== 用户信息卡片 ===== */
.user-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.5);
  position: relative;
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: 2px solid white;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.status-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: 2px solid white;
}

.status-indicator.online {
  background: #10b981;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-role {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.personalize-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.personalize-btn:hover {
  background: white;
  color: #6366f1;
  transform: scale(1.05);
}

/* ===== 快速统计 ===== */
.quick-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.stat-card {
  padding: 16px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.5);
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s ease;
}

.stat-card:hover {
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.stat-icon.teams {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
}

.stat-icon.activities {
  background: linear-gradient(135deg, #10b981, #059669);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 11px;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== 快速操作 ===== */
.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  padding: 12px 16px;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  text-align: left;
}

.action-btn.primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.action-btn.primary:hover {
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
  transform: translateY(-1px);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.8);
  color: #4b5563;
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.action-btn.secondary:hover {
  background: white;
  color: #1f2937;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* ===== 智能筛选器 ===== */
.filter-chips {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.filter-chip {
  padding: 8px 12px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  background: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
  font-size: 12px;
  color: #4b5563;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  text-align: left;
}

.filter-chip:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: #6366f1;
}

.filter-chip.active {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border-color: transparent;
}

.filter-count {
  margin-left: auto;
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 600;
}

.filter-chip.active .filter-count {
  background: rgba(255, 255, 255, 0.3);
}

/* ===== 个人推荐 ===== */
.recommendation-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.recommendation-item {
  padding: 12px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 10px;
  border: 1px solid rgba(226, 232, 240, 0.5);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 10px;
}

.recommendation-item:hover {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.team-avatar-small {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  overflow: hidden;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  flex-shrink: 0;
}

.team-avatar-small img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder-small {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.team-info-small {
  flex: 1;
  min-width: 0;
}

.team-name-small {
  font-size: 13px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 2px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.team-match {
  font-size: 11px;
  color: #10b981;
  margin: 0;
  font-weight: 500;
}

.recommendation-score {
  width: 40px;
  flex-shrink: 0;
}

.score-bar {
  width: 100%;
  height: 4px;
  background: rgba(226, 232, 240, 0.5);
  border-radius: 2px;
  overflow: hidden;
}

.score-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #059669);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* ===== 最近活动 ===== */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.activity-item {
  padding: 10px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(226, 232, 240, 0.5);
  display: flex;
  align-items: flex-start;
  gap: 10px;
  transition: all 0.2s ease;
}

.activity-item:hover {
  background: white;
}

.activity-icon {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-text {
  font-size: 12px;
  color: #4b5563;
  margin: 0 0 4px 0;
  line-height: 1.4;
}

.activity-time {
  font-size: 10px;
  color: #9ca3af;
}

/* ===== 响应式设计 ===== */
@media (max-width: 1200px) {
  .personal-dashboard {
    padding: 20px;
    gap: 20px;
  }

  .quick-stats {
    grid-template-columns: 1fr;
  }
}
</style>
