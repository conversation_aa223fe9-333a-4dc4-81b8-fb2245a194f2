<template>
  <div class="personalization-overlay" @click.self="$emit('close')">
    <div class="personalization-modal">
      <div class="modal-header">
        <h3>个性化设置</h3>
        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="modal-content">
        <div class="settings-section">
          <h4>兴趣标签</h4>
          <p>选择您感兴趣的领域，我们将为您推荐相关团队</p>
          <div class="tags-grid">
            <button
              v-for="tag in availableTags"
              :key="tag"
              class="tag-btn"
              :class="{ active: localPreferences.interests.includes(tag) }"
              @click="toggleInterest(tag)"
            >
              {{ tag }}
            </button>
          </div>
        </div>
        
        <div class="settings-section">
          <h4>技能标签</h4>
          <p>添加您的技能，帮助团队找到您</p>
          <div class="skills-input">
            <input
              v-model="newSkill"
              type="text"
              placeholder="输入技能并按回车添加"
              @keyup.enter="addSkill"
            />
            <div class="skills-list">
              <span
                v-for="skill in localPreferences.skills"
                :key="skill"
                class="skill-tag"
              >
                {{ skill }}
                <button @click="removeSkill(skill)">
                  <i class="fas fa-times"></i>
                </button>
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="modal-actions">
        <button class="btn secondary" @click="$emit('close')">
          取消
        </button>
        <button class="btn primary" @click="savePreferences">
          保存设置
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'

export default {
  name: 'PersonalizationPanel',
  props: {
    userPreferences: {
      type: Object,
      default: () => ({})
    },
    availableTags: {
      type: Array,
      default: () => []
    }
  },
  emits: ['close', 'save'],
  setup(props, { emit }) {
    const newSkill = ref('')
    const localPreferences = reactive({
      interests: [...(props.userPreferences.interests || [])],
      skills: [...(props.userPreferences.skills || [])]
    })

    const toggleInterest = (tag) => {
      const index = localPreferences.interests.indexOf(tag)
      if (index > -1) {
        localPreferences.interests.splice(index, 1)
      } else {
        localPreferences.interests.push(tag)
      }
    }

    const addSkill = () => {
      const skill = newSkill.value.trim()
      if (skill && !localPreferences.skills.includes(skill)) {
        localPreferences.skills.push(skill)
        newSkill.value = ''
      }
    }

    const removeSkill = (skill) => {
      const index = localPreferences.skills.indexOf(skill)
      if (index > -1) {
        localPreferences.skills.splice(index, 1)
      }
    }

    const savePreferences = () => {
      emit('save', { ...localPreferences })
    }

    return {
      newSkill,
      localPreferences,
      toggleInterest,
      addSkill,
      removeSkill,
      savePreferences
    }
  }
}
</script>

<style scoped>
.personalization-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.personalization-modal {
  background: white;
  border-radius: 20px;
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.modal-header {
  padding: 24px 24px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(107, 114, 128, 0.1);
  border-radius: 50%;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.modal-content {
  padding: 24px;
}

.settings-section {
  margin-bottom: 32px;
}

.settings-section h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.settings-section p {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
}

.tags-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
}

.tag-btn {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 8px;
  font-size: 13px;
  color: #4b5563;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag-btn:hover {
  border-color: #6366f1;
  color: #6366f1;
}

.tag-btn.active {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border-color: transparent;
}

.skills-input input {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  margin-bottom: 12px;
}

.skills-input input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.skill-tag {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.skill-tag button {
  border: none;
  background: none;
  color: inherit;
  cursor: pointer;
  padding: 0;
  font-size: 10px;
}

.modal-actions {
  padding: 0 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn.secondary {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

.btn.secondary:hover {
  background: rgba(107, 114, 128, 0.2);
}

.btn.primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}
</style>
