<template>
  <div class="context-panel">
    <div v-if="selectedTeam" class="panel-content">
      <div class="panel-header">
        <h4>{{ selectedTeam.name }}</h4>
        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="team-insights">
        <h5>团队洞察</h5>
        <div class="insight-item">
          <span>活跃度评分</span>
          <span>{{ selectedTeam.activityScore || 0 }}</span>
        </div>
        <div class="insight-item">
          <span>匹配度</span>
          <span>{{ selectedTeam.matchScore || 0 }}%</span>
        </div>
      </div>

      <div class="related-teams" v-if="relatedTeams.length">
        <h5>相关团队</h5>
        <div
          v-for="team in relatedTeams.slice(0, 3)"
          :key="team.id"
          class="related-team-item"
        >
          <div class="team-avatar-small">
            <img v-if="team.avatar" :src="team.avatar" :alt="team.name" />
            <div v-else class="avatar-placeholder">
              <i class="fas fa-users"></i>
            </div>
          </div>
          <div class="team-info">
            <h6>{{ team.name }}</h6>
            <p>{{ team.membersCount || 0 }} 成员</p>
          </div>
        </div>
      </div>

      <div class="trending-topics" v-if="trendingTopics.length">
        <h5>热门话题</h5>
        <div class="topic-list">
          <span
            v-for="topic in trendingTopics.slice(0, 5)"
            :key="topic.name"
            class="topic-tag"
          >
            {{ topic.name }}
          </span>
        </div>
      </div>
    </div>

    <div v-else class="empty-panel">
      <div class="empty-icon">
        <i class="fas fa-mouse-pointer"></i>
      </div>
      <p>选择一个团队查看详细信息</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ContextPanel',
  props: {
    selectedTeam: {
      type: Object,
      default: null
    },
    teamInsights: {
      type: Object,
      default: null
    },
    relatedTeams: {
      type: Array,
      default: () => []
    },
    trendingTopics: {
      type: Array,
      default: () => []
    }
  },
  emits: ['close', 'action']
}
</script>

<style scoped>
.context-panel {
  padding: 24px;
  height: 100%;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.panel-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: rgba(107, 114, 128, 0.1);
  border-radius: 6px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.team-insights,
.related-teams,
.trending-topics {
  margin-bottom: 24px;
}

.team-insights h5,
.related-teams h5,
.trending-topics h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.insight-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
  color: #4b5563;
}

.related-team-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 8px;
}

.related-team-item:hover {
  background: rgba(99, 102, 241, 0.05);
}

.team-avatar-small {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  overflow: hidden;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  flex-shrink: 0;
}

.team-avatar-small img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
}

.team-info h6 {
  margin: 0 0 2px 0;
  font-size: 13px;
  font-weight: 600;
  color: #1f2937;
}

.team-info p {
  margin: 0;
  font-size: 11px;
  color: #6b7280;
}

.topic-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.topic-tag {
  padding: 4px 8px;
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.empty-panel {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #9ca3af;
  text-align: center;
}

.empty-icon {
  font-size: 32px;
  margin-bottom: 12px;
  opacity: 0.5;
}

.empty-panel p {
  margin: 0;
  font-size: 14px;
}
</style>
