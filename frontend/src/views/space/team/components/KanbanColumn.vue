<template>
  <div class="kanban-column">
    <div class="column-header">
      <h4>{{ title }}</h4>
      <span class="team-count">{{ teams.length }}</span>
    </div>
    <div class="column-content">
      <div
        v-for="team in teams"
        :key="team.id"
        class="kanban-team-card"
        @click="$emit('team-action', { type: 'preview', team })"
      >
        <div class="team-avatar">
          <img v-if="team.avatar" :src="team.avatar" :alt="team.name" />
          <div v-else class="avatar-placeholder">
            <i class="fas fa-users"></i>
          </div>
        </div>
        <div class="team-info">
          <h5>{{ team.name }}</h5>
          <p>{{ team.membersCount || 0 }} 成员</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'KanbanColumn',
  props: {
    title: {
      type: String,
      required: true
    },
    teams: {
      type: Array,
      default: () => []
    }
  },
  emits: ['team-action']
}
</script>

<style scoped>
.kanban-column {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 16px;
  min-height: 400px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.column-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.column-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.team-count {
  background: #6366f1;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.column-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.kanban-team-card {
  background: white;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(226, 232, 240, 0.5);
  display: flex;
  align-items: center;
  gap: 10px;
}

.kanban-team-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #6366f1;
}

.team-avatar {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  overflow: hidden;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  flex-shrink: 0;
}

.team-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.team-info {
  flex: 1;
  min-width: 0;
}

.team-info h5 {
  margin: 0 0 2px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.team-info p {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
}
</style>
