<template>
  <div v-if="visible" class="modal-overlay" @click.self="$emit('close')">
    <div class="create-team-modal">
      <div class="modal-header">
        <h2 class="modal-title">
          <i class="fas fa-plus-circle"></i>
          创建团队空间
        </h2>
        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <form @submit.prevent="handleSubmit" class="modal-form">
        <div class="form-content">
          <!-- 基本信息 -->
          <div class="form-section">
            <h3 class="section-title">基本信息</h3>
            
            <div class="form-group">
              <label class="form-label">团队名称 *</label>
              <input
                v-model="formData.name"
                type="text"
                class="form-input"
                placeholder="输入团队名称..."
                maxlength="50"
                required
              >
              <div class="input-hint">{{ formData.name.length }}/50</div>
            </div>

            <div class="form-group">
              <label class="form-label">团队描述 *</label>
              <textarea
                v-model="formData.description"
                class="form-textarea"
                placeholder="描述团队的目标、方向或特色..."
                rows="4"
                maxlength="200"
                required
              ></textarea>
              <div class="input-hint">{{ formData.description.length }}/200</div>
            </div>

            <div class="form-group">
              <label class="form-label">团队头像</label>
              <div class="avatar-upload">
                <div class="avatar-preview">
                  <img v-if="formData.avatarUrl" :src="formData.avatarUrl" alt="团队头像" />
                  <div v-else class="avatar-placeholder">
                    <i class="fas fa-users"></i>
                  </div>
                </div>
                <div class="upload-actions">
                  <input
                    ref="fileInput"
                    type="file"
                    accept="image/*"
                    @change="handleAvatarUpload"
                    style="display: none"
                  >
                  <button type="button" class="upload-btn" @click="$refs.fileInput.click()">
                    <i class="fas fa-upload"></i>
                    上传头像
                  </button>
                  <button
                    v-if="formData.avatarUrl"
                    type="button"
                    class="remove-btn"
                    @click="formData.avatarUrl = ''"
                  >
                    <i class="fas fa-trash"></i>
                    移除
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 隐私设置 -->
          <div class="form-section">
            <h3 class="section-title">隐私设置</h3>
            
            <div class="privacy-options">
              <label class="privacy-option">
                <input
                  v-model="formData.privacy"
                  type="radio"
                  :value="1"
                >
                <div class="option-content">
                  <div class="option-header">
                    <i class="fas fa-globe"></i>
                    <span class="option-title">公开团队</span>
                  </div>
                  <p class="option-description">任何人都可以查看和加入这个团队</p>
                </div>
              </label>

              <label class="privacy-option">
                <input
                  v-model="formData.privacy"
                  type="radio"
                  :value="0"
                >
                <div class="option-content">
                  <div class="option-header">
                    <i class="fas fa-lock"></i>
                    <span class="option-title">私有团队</span>
                  </div>
                  <p class="option-description">只有受邀请的成员才能查看和加入</p>
                </div>
              </label>
            </div>
          </div>

          <!-- 标签设置 -->
          <div class="form-section">
            <h3 class="section-title">团队标签</h3>
            
            <div class="tag-input-section">
              <div class="tag-input-wrapper">
                <input
                  v-model="newTag"
                  type="text"
                  class="tag-input"
                  placeholder="输入标签并按回车添加..."
                  @keyup.enter="addTag"
                  @keyup.comma="addTag"
                  maxlength="20"
                >
                <button type="button" class="add-tag-btn" @click="addTag">
                  <i class="fas fa-plus"></i>
                </button>
              </div>
              
              <div class="tag-list">
                <span
                  v-for="(tag, index) in formData.tags"
                  :key="index"
                  class="tag-item"
                >
                  {{ tag }}
                  <button type="button" @click="removeTag(index)">
                    <i class="fas fa-times"></i>
                  </button>
                </span>
              </div>
              
              <div class="tag-suggestions">
                <span class="suggestions-label">推荐标签:</span>
                <button
                  v-for="suggestion in tagSuggestions"
                  :key="suggestion"
                  type="button"
                  class="suggestion-tag"
                  @click="addSuggestionTag(suggestion)"
                  :disabled="formData.tags.includes(suggestion)"
                >
                  {{ suggestion }}
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn-secondary" @click="$emit('close')">
            取消
          </button>
          <button type="submit" class="btn-primary" :disabled="!isFormValid || isSubmitting">
            <i v-if="isSubmitting" class="fas fa-spinner fa-spin"></i>
            <i v-else class="fas fa-check"></i>
            {{ isSubmitting ? '创建中...' : '创建团队' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, computed, reactive } from 'vue'
import { useToastStore } from '@/stores/toast'
import { useUserStore } from '@/stores/user'
import teamService from '@/services/teamService'

export default {
  name: 'CreateTeamModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close', 'created'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    const userStore = useUserStore()
    
    const isSubmitting = ref(false)
    const newTag = ref('')
    
    const formData = reactive({
      name: '',
      description: '',
      avatarUrl: '',
      privacy: 1, // 1=公开, 0=私有
      tags: []
    })

    const tagSuggestions = ref([
      'AIGC', 'AI', '前端', '后端', '设计', '产品', '运营', '数据分析',
      '机器学习', '深度学习', '自然语言处理', '计算机视觉', '区块链',
      '移动开发', 'Web开发', 'UI设计', 'UX设计', '项目管理'
    ])

    const isFormValid = computed(() => {
      return formData.name.trim().length > 0 && 
             formData.description.trim().length > 0
    })

    const handleAvatarUpload = (event) => {
      const file = event.target.files[0]
      if (!file) return

      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        toastStore.error('请选择图片文件')
        return
      }

      // 验证文件大小 (2MB)
      if (file.size > 2 * 1024 * 1024) {
        toastStore.error('图片大小不能超过2MB')
        return
      }

      // 创建预览URL
      const reader = new FileReader()
      reader.onload = (e) => {
        formData.avatarUrl = e.target.result
      }
      reader.readAsDataURL(file)
    }

    const addTag = () => {
      const tag = newTag.value.trim().replace(',', '')
      if (!tag) return
      
      if (formData.tags.length >= 10) {
        toastStore.error('最多只能添加10个标签')
        return
      }
      
      if (formData.tags.includes(tag)) {
        toastStore.error('标签已存在')
        return
      }
      
      formData.tags.push(tag)
      newTag.value = ''
    }

    const removeTag = (index) => {
      formData.tags.splice(index, 1)
    }

    const addSuggestionTag = (tag) => {
      if (formData.tags.length >= 10) {
        toastStore.error('最多只能添加10个标签')
        return
      }
      
      if (!formData.tags.includes(tag)) {
        formData.tags.push(tag)
      }
    }

    const handleSubmit = async () => {
      if (!isFormValid.value || isSubmitting.value) return

      isSubmitting.value = true

      try {
        const teamData = {
          name: formData.name.trim(),
          description: formData.description.trim(),
          avatarUrl: formData.avatarUrl || '',
          privacy: formData.privacy,
          tags: formData.tags
        }

        const result = await teamService.createTeam(teamData)
        
        // 构造团队对象用于更新UI
        const newTeam = {
          id: result.teamId || Date.now(),
          name: teamData.name,
          description: teamData.description,
          avatar: teamData.avatarUrl,
          isPublic: teamData.privacy === 1,
          isMember: true,
          isCreatedByMe: true,
          isStarred: false,
          membersCount: 1,
          articlesCount: 0,
          likesCount: 0,
          viewsCount: 0,
          tags: teamData.tags,
          createdAt: new Date().toISOString(),
          lastActivityAt: new Date().toISOString(),
          members: [{
            userId: userStore.user?.id,
            name: userStore.user?.name,
            avatar: userStore.user?.avatar,
            role: 'owner'
          }],
          creatorId: userStore.user?.id
        }

        emit('created', newTeam)
        
        // 重置表单
        Object.assign(formData, {
          name: '',
          description: '',
          avatarUrl: '',
          privacy: 1,
          tags: []
        })
        
      } catch (error) {
        console.error('创建团队失败:', error)
        toastStore.error('创建失败: ' + (error.message || '未知错误'))
      } finally {
        isSubmitting.value = false
      }
    }

    return {
      formData,
      newTag,
      tagSuggestions,
      isFormValid,
      isSubmitting,
      handleAvatarUpload,
      addTag,
      removeTag,
      addSuggestionTag,
      handleSubmit
    }
  }
}
</script>

<style scoped>
/* 🎨 创建团队模态框样式 */

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.create-team-modal {
  background: white;
  border-radius: 24px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* ===== 模态框头部 ===== */
.modal-header {
  padding: 32px 32px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f1f5f9;
  margin-bottom: 32px;
}

.modal-title {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.modal-title i {
  color: #6366f1;
  font-size: 20px;
}

.close-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(107, 114, 128, 0.1);
  border-radius: 50%;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* ===== 表单内容 ===== */
.modal-form {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.form-content {
  flex: 1;
  padding: 0 32px;
}

.form-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 2px;
}

/* ===== 表单组件 ===== */
.form-group {
  margin-bottom: 24px;
  position: relative;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: white;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
  line-height: 1.5;
}

.input-hint {
  position: absolute;
  bottom: -20px;
  right: 0;
  font-size: 12px;
  color: #9ca3af;
}

/* ===== 头像上传 ===== */
.avatar-upload {
  display: flex;
  align-items: center;
  gap: 20px;
}

.avatar-preview {
  width: 80px;
  height: 80px;
  border-radius: 16px;
  overflow: hidden;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: 3px solid white;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

.avatar-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.upload-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.upload-btn,
.remove-btn {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.upload-btn:hover {
  border-color: #6366f1;
  color: #6366f1;
}

.remove-btn {
  color: #ef4444;
  border-color: #fecaca;
}

.remove-btn:hover {
  background: #fef2f2;
  border-color: #ef4444;
}

/* ===== 隐私选项 ===== */
.privacy-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.privacy-option {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.privacy-option:hover {
  border-color: #6366f1;
  background: #f8faff;
}

.privacy-option input[type="radio"] {
  margin-top: 2px;
  accent-color: #6366f1;
}

.option-content {
  flex: 1;
}

.option-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.option-header i {
  color: #6366f1;
  font-size: 16px;
}

.option-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
}

.option-description {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  line-height: 1.4;
}

/* ===== 标签输入 ===== */
.tag-input-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.tag-input-wrapper {
  display: flex;
  gap: 8px;
}

.tag-input {
  flex: 1;
  padding: 10px 14px;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.tag-input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.add-tag-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-tag-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 32px;
}

.tag-item {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.tag-item button {
  border: none;
  background: none;
  color: inherit;
  cursor: pointer;
  padding: 0;
  font-size: 10px;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.tag-item button:hover {
  opacity: 1;
}

.tag-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.suggestions-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.suggestion-tag {
  padding: 4px 10px;
  border: 1px solid #d1d5db;
  background: white;
  color: #4b5563;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggestion-tag:hover:not(:disabled) {
  border-color: #6366f1;
  color: #6366f1;
}

.suggestion-tag:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* ===== 模态框底部 ===== */
.modal-footer {
  padding: 24px 32px 32px;
  border-top: 1px solid #f1f5f9;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: #fafbfc;
  border-radius: 0 0 24px 24px;
}

.btn-secondary,
.btn-primary {
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
}

.btn-secondary {
  background: white;
  color: #64748b;
  border: 2px solid #e2e8f0;
}

.btn-secondary:hover {
  border-color: #6366f1;
  color: #6366f1;
  transform: translateY(-1px);
}

.btn-primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border: none;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 16px;
  }

  .create-team-modal {
    border-radius: 20px;
    max-height: 95vh;
  }

  .modal-header {
    padding: 24px 24px 0;
    margin-bottom: 24px;
  }

  .modal-title {
    font-size: 20px;
  }

  .form-content {
    padding: 0 24px;
  }

  .form-section {
    margin-bottom: 24px;
  }

  .section-title {
    font-size: 16px;
  }

  .avatar-upload {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .upload-actions {
    flex-direction: row;
    gap: 12px;
  }

  .privacy-options {
    gap: 12px;
  }

  .privacy-option {
    padding: 12px;
  }

  .modal-footer {
    padding: 20px 24px 24px;
    flex-direction: column;
  }

  .btn-secondary,
  .btn-primary {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .modal-overlay {
    padding: 12px;
  }

  .create-team-modal {
    border-radius: 16px;
  }

  .modal-header {
    padding: 20px 20px 0;
    margin-bottom: 20px;
  }

  .modal-title {
    font-size: 18px;
  }

  .close-btn {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  .form-content {
    padding: 0 20px;
  }

  .form-section {
    margin-bottom: 20px;
  }

  .section-title {
    font-size: 15px;
  }

  .form-group {
    margin-bottom: 20px;
  }

  .form-input,
  .form-textarea {
    padding: 10px 12px;
    font-size: 13px;
  }

  .avatar-preview {
    width: 60px;
    height: 60px;
    border-radius: 12px;
  }

  .avatar-placeholder {
    font-size: 20px;
  }

  .upload-btn,
  .remove-btn {
    padding: 6px 12px;
    font-size: 12px;
  }

  .privacy-option {
    padding: 10px;
  }

  .option-title {
    font-size: 14px;
  }

  .option-description {
    font-size: 13px;
  }

  .tag-input {
    padding: 8px 12px;
    font-size: 13px;
  }

  .add-tag-btn {
    width: 36px;
    height: 36px;
  }

  .modal-footer {
    padding: 16px 20px 20px;
  }

  .btn-secondary,
  .btn-primary {
    padding: 10px 20px;
    font-size: 13px;
  }
}
</style>
