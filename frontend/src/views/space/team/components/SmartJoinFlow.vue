<template>
  <div class="smart-join-overlay" @click.self="$emit('close')">
    <div class="join-modal">
      <div class="modal-header">
        <h3>加入 {{ team.name }}</h3>
        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="modal-content">
        <div v-if="team.isPublic" class="public-join">
          <div class="join-icon">
            <i class="fas fa-rocket"></i>
          </div>
          <h4>欢迎加入！</h4>
          <p>这是一个公开团队，您可以立即加入并开始协作。</p>
        </div>
        
        <div v-else class="private-join">
          <div class="join-icon">
            <i class="fas fa-shield-alt"></i>
          </div>
          <h4>申请加入</h4>
          <p>这是一个私有团队，需要管理员审核您的申请。</p>
          
          <form @submit.prevent="handleSubmit" class="application-form">
            <div class="form-group">
              <label>申请理由</label>
              <textarea
                v-model="applicationData.reason"
                placeholder="请说明您加入团队的原因..."
                rows="4"
                required
              ></textarea>
            </div>
            
            <div class="form-group">
              <label>相关技能</label>
              <input
                v-model="applicationData.skills"
                type="text"
                placeholder="如：JavaScript, Vue.js, 项目管理..."
              />
            </div>
          </form>
        </div>
      </div>
      
      <div class="modal-actions">
        <button class="btn secondary" @click="$emit('close')">
          取消
        </button>
        <button class="btn primary" @click="handleSubmit">
          {{ team.isPublic ? '立即加入' : '提交申请' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { reactive } from 'vue'

export default {
  name: 'SmartJoinFlow',
  props: {
    team: {
      type: Object,
      required: true
    },
    user: {
      type: Object,
      default: () => ({})
    },
    suggestions: {
      type: Array,
      default: () => []
    }
  },
  emits: ['close', 'submit'],
  setup(props, { emit }) {
    const applicationData = reactive({
      reason: '',
      skills: ''
    })

    const handleSubmit = () => {
      if (props.team.isPublic) {
        emit('submit', { type: 'direct_join' })
      } else {
        emit('submit', {
          type: 'application',
          reason: applicationData.reason,
          skills: applicationData.skills
        })
      }
    }

    return {
      applicationData,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.smart-join-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.join-modal {
  background: white;
  border-radius: 20px;
  max-width: 500px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.modal-header {
  padding: 24px 24px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(107, 114, 128, 0.1);
  border-radius: 50%;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.modal-content {
  padding: 24px;
  text-align: center;
}

.join-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin: 0 auto 16px;
}

.public-join h4,
.private-join h4 {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.public-join p,
.private-join p {
  margin: 0 0 20px 0;
  color: #6b7280;
  line-height: 1.5;
}

.application-form {
  text-align: left;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.form-group textarea,
.form-group input {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-group textarea:focus,
.form-group input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-actions {
  padding: 0 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn.secondary {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

.btn.secondary:hover {
  background: rgba(107, 114, 128, 0.2);
}

.btn.primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}
</style>
