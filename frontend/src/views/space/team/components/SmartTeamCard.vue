<template>
  <div 
    class="smart-team-card" 
    :class="{ 
      'is-member': team.isMember,
      'is-recommended': team.isRecommended,
      'is-featured': team.isFeatured
    }"
    @click="handleCardClick"
  >
    <!-- 推荐标识 -->
    <div v-if="team.isRecommended" class="recommendation-badge">
      <i class="fas fa-magic"></i>
      <span>推荐</span>
    </div>

    <!-- 匹配度指示器 -->
    <div v-if="team.matchScore > 0" class="match-indicator">
      <div class="match-score">{{ team.matchScore }}%</div>
      <div class="match-label">匹配</div>
    </div>

    <!-- 卡片头部 -->
    <div class="card-header">
      <div class="team-avatar">
        <img v-if="team.avatar" :src="team.avatar" :alt="team.name" />
        <div v-else class="avatar-placeholder">
          <i class="fas fa-users"></i>
        </div>
        <div v-if="team.isActive" class="activity-pulse"></div>
      </div>

      <div class="team-basic-info">
        <h3 class="team-name">{{ team.name }}</h3>
        <p class="team-category">{{ team.category || '综合团队' }}</p>
      </div>

      <div class="card-actions">
        <button
          class="action-btn star-btn"
          :class="{ active: team.isStarred }"
          @click.stop="$emit('action', { type: 'star', team })"
        >
          <i class="fas fa-star"></i>
        </button>
      </div>
    </div>

    <!-- 卡片主体 -->
    <div class="card-body">
      <p class="team-description">{{ team.description || '暂无描述' }}</p>
      
      <!-- 团队标签 -->
      <div v-if="team.tags && team.tags.length" class="team-tags">
        <span
          v-for="tag in team.tags.slice(0, 3)"
          :key="tag"
          class="tag"
        >
          {{ tag }}
        </span>
        <span v-if="team.tags.length > 3" class="tag more">
          +{{ team.tags.length - 3 }}
        </span>
      </div>

      <!-- 团队统计 -->
      <div class="team-stats">
        <div class="stat-item">
          <i class="fas fa-users"></i>
          <span>{{ team.membersCount || 0 }}</span>
        </div>
        <div class="stat-item">
          <i class="fas fa-file-alt"></i>
          <span>{{ team.contentCount || 0 }}</span>
        </div>
        <div class="stat-item">
          <i class="fas fa-eye"></i>
          <span>{{ formatNumber(team.viewsCount || 0) }}</span>
        </div>
      </div>
    </div>

    <!-- 卡片底部 -->
    <div class="card-footer">
      <div class="team-status">
        <span class="status-badge" :class="team.isPublic ? 'public' : 'private'">
          <i :class="team.isPublic ? 'fas fa-globe' : 'fas fa-lock'"></i>
          {{ team.isPublic ? '公开' : '私有' }}
        </span>
        <span class="last-activity">
          {{ formatLastActivity(team.lastActivityAt) }}
        </span>
      </div>

      <div class="primary-actions">
        <button
          v-if="team.isMember"
          class="primary-btn enter"
          @click.stop="$emit('action', { type: 'enter', team })"
        >
          <i class="fas fa-arrow-right"></i>
          进入
        </button>
        <button
          v-else
          class="primary-btn join"
          @click.stop="$emit('action', { type: 'join', team })"
        >
          <i class="fas fa-plus"></i>
          {{ team.isPublic ? '加入' : '申请' }}
        </button>
      </div>
    </div>

    <!-- 悬停预览 -->
    <div class="hover-preview">
      <button class="preview-btn" @click.stop="$emit('action', { type: 'preview', team })">
        <i class="fas fa-eye"></i>
        快速预览
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SmartTeamCard',
  props: {
    team: {
      type: Object,
      required: true
    },
    viewMode: {
      type: String,
      default: 'smart-grid'
    }
  },
  emits: ['action'],
  setup(props, { emit }) {
    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }

    const formatLastActivity = (timestamp) => {
      if (!timestamp) return '暂无活动'
      
      const now = new Date()
      const activity = new Date(timestamp)
      const diffMs = now - activity
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60))

      if (diffDays > 7) {
        return activity.toLocaleDateString()
      } else if (diffDays > 0) {
        return `${diffDays}天前`
      } else if (diffHours > 0) {
        return `${diffHours}小时前`
      } else {
        return '刚刚活跃'
      }
    }

    const handleCardClick = () => {
      emit('action', { type: 'select', team: props.team })
    }

    return {
      formatNumber,
      formatLastActivity,
      handleCardClick
    }
  }
}
</script>

<style scoped>
/* 智能团队卡片样式 */
.smart-team-card {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
  backdrop-filter: blur(10px);
  height: 380px;
  display: flex;
  flex-direction: column;
}

.smart-team-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
  border-color: rgba(99, 102, 241, 0.3);
}

.smart-team-card.is-member {
  border-color: rgba(16, 185, 129, 0.4);
  background: linear-gradient(145deg, 
    rgba(236, 253, 245, 0.9) 0%, 
    rgba(255, 255, 255, 0.9) 100%);
}

.smart-team-card.is-recommended {
  border-color: rgba(245, 158, 11, 0.4);
  background: linear-gradient(145deg, 
    rgba(255, 251, 235, 0.9) 0%, 
    rgba(255, 255, 255, 0.9) 100%);
}

/* 推荐标识 */
.recommendation-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  z-index: 2;
}

/* 匹配度指示器 */
.match-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  text-align: center;
  z-index: 2;
}

.match-score {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 700;
}

.match-label {
  font-size: 10px;
  color: #6b7280;
  margin-top: 2px;
}

/* 卡片头部 */
.card-header {
  padding: 20px 20px 0;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.team-avatar {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: 3px solid white;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
  flex-shrink: 0;
}

.team-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.activity-pulse {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  background: #10b981;
  border: 2px solid white;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.team-basic-info {
  flex: 1;
  min-width: 0;
}

.team-name {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 4px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.team-category {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: white;
  color: #6366f1;
}

.star-btn.active {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

/* 卡片主体 */
.card-body {
  padding: 16px 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.team-description {
  font-size: 14px;
  color: #4b5563;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 42px;
}

.team-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  min-height: 24px;
}

.tag {
  padding: 4px 8px;
  background: linear-gradient(135deg, #e5e7eb, #d1d5db);
  color: #4b5563;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.tag.more {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.team-stats {
  display: flex;
  justify-content: space-around;
  padding: 12px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  margin-top: auto;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6b7280;
}

.stat-item i {
  color: #6366f1;
  font-size: 14px;
}

.stat-item span {
  font-weight: 600;
  color: #1f2937;
}

/* 卡片底部 */
.card-footer {
  padding: 16px 20px 20px;
  border-top: 1px solid rgba(226, 232, 240, 0.5);
  background: rgba(248, 250, 252, 0.5);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.team-status {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 6px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.public {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
}

.status-badge.private {
  background: rgba(107, 114, 128, 0.1);
  color: #4b5563;
}

.last-activity {
  font-size: 10px;
  color: #9ca3af;
}

.primary-actions {
  display: flex;
  gap: 8px;
}

.primary-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.primary-btn.enter {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.primary-btn.join {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.primary-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 悬停预览 */
.hover-preview {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(99, 102, 241, 0.9) 0%, 
    rgba(139, 92, 246, 0.9) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  backdrop-filter: blur(10px);
}

.smart-team-card:hover .hover-preview {
  opacity: 1;
}

.preview-btn {
  padding: 12px 24px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  color: white;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  backdrop-filter: blur(10px);
}

.preview-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}
</style>
