<template>
  <div class="timeline-team-card">
    <div class="timeline-marker"></div>
    <div class="card-content">
      <div class="team-header">
        <div class="team-avatar">
          <img v-if="team.avatar" :src="team.avatar" :alt="team.name" />
          <div v-else class="avatar-placeholder">
            <i class="fas fa-users"></i>
          </div>
        </div>
        <div class="team-info">
          <h4>{{ team.name }}</h4>
          <p>{{ team.description }}</p>
        </div>
        <div class="team-actions">
          <button @click="$emit('action', { type: 'preview', team })">
            <i class="fas fa-eye"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TimelineTeamCard',
  props: {
    team: {
      type: Object,
      required: true
    }
  },
  emits: ['action']
}
</script>

<style scoped>
.timeline-team-card {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 24px;
  position: relative;
}

.timeline-marker {
  width: 12px;
  height: 12px;
  background: #6366f1;
  border-radius: 50%;
  margin-top: 8px;
  flex-shrink: 0;
}

.timeline-marker::before {
  content: '';
  position: absolute;
  left: 5px;
  top: 20px;
  width: 2px;
  height: calc(100% + 24px);
  background: #e5e7eb;
  z-index: -1;
}

.card-content {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.team-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.team-avatar {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  overflow: hidden;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
}

.team-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.team-info {
  flex: 1;
}

.team-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.team-info p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}

.team-actions button {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  border-radius: 8px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.team-actions button:hover {
  background: #6366f1;
  color: white;
}
</style>
