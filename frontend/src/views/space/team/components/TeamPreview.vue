<template>
  <div class="team-preview-modal" @click.self="$emit('close')">
    <div class="preview-container">
      <!-- 关闭按钮 -->
      <button class="close-btn" @click="$emit('close')">
        <i class="fas fa-times"></i>
      </button>

      <!-- 团队头部信息 -->
      <div class="preview-header">
        <div class="team-cover" :style="{ background: team.themeColor || defaultGradient }">
          <div class="cover-pattern"></div>
          <div class="team-avatar-large">
            <img v-if="team.avatar" :src="team.avatar" :alt="team.name" />
            <div v-else class="avatar-placeholder">
              <i class="fas fa-users"></i>
            </div>
            <div v-if="team.isActive" class="activity-indicator">
              <div class="pulse-ring"></div>
              <div class="pulse-dot"></div>
            </div>
          </div>
        </div>

        <div class="team-basic-info">
          <div class="team-title-section">
            <h2 class="team-name">{{ team.name }}</h2>
            <div class="team-badges">
              <span v-if="team.isAdmin" class="badge admin">
                <i class="fas fa-crown"></i>
                管理员
              </span>
              <span v-else-if="team.isMember" class="badge member">
                <i class="fas fa-check-circle"></i>
                已加入
              </span>
              <span class="badge visibility" :class="team.isPublic ? 'public' : 'private'">
                <i :class="team.isPublic ? 'fas fa-globe' : 'fas fa-lock'"></i>
                {{ team.isPublic ? '公开团队' : '私有团队' }}
              </span>
            </div>
          </div>

          <p v-if="team.description" class="team-description">{{ team.description }}</p>
          <p v-else class="team-description placeholder">这个团队还没有添加描述...</p>

          <!-- 团队标签 -->
          <div v-if="team.tags && team.tags.length" class="team-tags">
            <span v-for="tag in team.tags" :key="tag" class="tag">{{ tag }}</span>
          </div>
        </div>
      </div>

      <!-- 团队统计 -->
      <div class="preview-stats">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-users"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ formatNumber(team.membersCount || 0) }}</div>
            <div class="stat-label">团队成员</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-file-alt"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ formatNumber(team.contentCount || 0) }}</div>
            <div class="stat-label">团队内容</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-eye"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ formatNumber(team.viewsCount || 0) }}</div>
            <div class="stat-label">总浏览量</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-heart"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ formatNumber(team.likesCount || 0) }}</div>
            <div class="stat-label">获得点赞</div>
          </div>
        </div>
      </div>

      <!-- 团队成员预览 -->
      <div v-if="team.members && team.members.length" class="preview-members">
        <h3 class="section-title">
          <i class="fas fa-users"></i>
          团队成员 ({{ team.membersCount || 0 }})
        </h3>
        <div class="members-grid">
          <div
            v-for="member in displayMembers"
            :key="member.id"
            class="member-card"
          >
            <img :src="member.avatar" :alt="member.name" class="member-avatar" />
            <div class="member-info">
              <div class="member-name">{{ member.name }}</div>
              <div class="member-role">{{ member.role || '成员' }}</div>
            </div>
            <div v-if="member.isOnline" class="online-indicator"></div>
          </div>
          <div v-if="team.membersCount > displayMembers.length" class="more-members-card">
            <div class="more-icon">
              <i class="fas fa-plus"></i>
            </div>
            <div class="more-text">
              还有 {{ team.membersCount - displayMembers.length }} 位成员
            </div>
          </div>
        </div>
      </div>

      <!-- 最近活动 -->
      <div v-if="team.recentActivities && team.recentActivities.length" class="preview-activities">
        <h3 class="section-title">
          <i class="fas fa-clock"></i>
          最近活动
        </h3>
        <div class="activities-list">
          <div
            v-for="activity in team.recentActivities.slice(0, 3)"
            :key="activity.id"
            class="activity-item"
          >
            <div class="activity-avatar">
              <img :src="activity.user.avatar" :alt="activity.user.name" />
            </div>
            <div class="activity-content">
              <div class="activity-text">
                <strong>{{ activity.user.name }}</strong>
                {{ activity.description }}
              </div>
              <div class="activity-time">{{ formatTime(activity.createdAt) }}</div>
            </div>
            <div class="activity-icon">
              <i :class="getActivityIcon(activity.type)"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="preview-actions">
        <button
          v-if="team.isMember"
          class="action-btn primary large"
          @click="$emit('enter', team)"
        >
          <i class="fas fa-arrow-right"></i>
          进入团队空间
        </button>
        <template v-else>
          <button
            class="action-btn primary large"
            @click="$emit('join', team)"
          >
            <i class="fas fa-plus"></i>
            {{ team.isPublic ? '立即加入团队' : '申请加入团队' }}
          </button>
          <button
            class="action-btn secondary"
            @click="$emit('enter', team)"
          >
            <i class="fas fa-eye"></i>
            查看详情
          </button>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'TeamPreview',
  props: {
    team: {
      type: Object,
      required: true
    }
  },
  emits: ['close', 'join', 'enter'],
  setup(props) {
    const defaultGradient = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'

    const displayMembers = computed(() => {
      return props.team.members?.slice(0, 6) || []
    })

    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }

    const formatTime = (timestamp) => {
      if (!timestamp) return ''
      
      const now = new Date()
      const time = new Date(timestamp)
      const diffMs = now - time
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
      const diffMinutes = Math.floor(diffMs / (1000 * 60))

      if (diffDays > 0) {
        return `${diffDays}天前`
      } else if (diffHours > 0) {
        return `${diffHours}小时前`
      } else if (diffMinutes > 0) {
        return `${diffMinutes}分钟前`
      } else {
        return '刚刚'
      }
    }

    const getActivityIcon = (type) => {
      const icons = {
        'content': 'fas fa-file-alt',
        'comment': 'fas fa-comment',
        'like': 'fas fa-heart',
        'join': 'fas fa-user-plus',
        'create': 'fas fa-plus',
        'update': 'fas fa-edit',
        'share': 'fas fa-share'
      }
      return icons[type] || 'fas fa-circle'
    }

    return {
      defaultGradient,
      displayMembers,
      formatNumber,
      formatTime,
      getActivityIcon
    }
  }
}
</script>

<style scoped>
/* 🔍 团队预览模态框样式 */

/* ===== 模态框基础样式 ===== */
.team-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: modal-fade-in 0.3s ease-out;
}

@keyframes modal-fade-in {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px);
  }
}

.preview-container {
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 100%);
  border-radius: 24px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.06);
  animation: modal-slide-up 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modal-slide-up {
  from {
    transform: translateY(40px) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

.close-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  z-index: 10;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.close-btn:hover {
  background: rgba(255, 255, 255, 1);
  color: #ef4444;
  transform: scale(1.1);
}

/* ===== 团队头部信息 ===== */
.preview-header {
  position: relative;
  margin-bottom: 24px;
}

.team-cover {
  height: 160px;
  position: relative;
  border-radius: 24px 24px 0 0;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cover-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  background-size: 60px 60px;
}

.team-avatar-large {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.1) 100%);
  border: 4px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.team-avatar-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  font-weight: 600;
}

.activity-indicator {
  position: absolute;
  bottom: -4px;
  right: -4px;
  width: 24px;
  height: 24px;
}

.pulse-dot {
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #10b981, #059669);
  border: 4px solid white;
  border-radius: 50%;
  position: absolute;
}

.pulse-ring {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(16, 185, 129, 0.4);
  border-radius: 50%;
  position: absolute;
  animation: pulse-ring 2s ease-out infinite;
}

.team-basic-info {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.team-title-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.team-name {
  font-size: 28px;
  font-weight: 800;
  color: #1a202c;
  margin: 0;
  line-height: 1.2;
  background: linear-gradient(135deg, #1a202c, #2d3748);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.team-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.badge.admin {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  color: #92400e;
  border: 1px solid rgba(245, 158, 11, 0.3);
  animation: admin-glow 3s ease-in-out infinite alternate;
}

.badge.member {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  color: #1e40af;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.badge.visibility.public {
  background: linear-gradient(135deg, #d1fae5, #a7f3d0);
  color: #065f46;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.badge.visibility.private {
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  color: #374151;
  border: 1px solid rgba(107, 114, 128, 0.3);
}

@keyframes admin-glow {
  0% { box-shadow: 0 2px 8px rgba(245, 158, 11, 0.2); }
  100% { box-shadow: 0 4px 16px rgba(245, 158, 11, 0.4); }
}

.team-description {
  font-size: 16px;
  line-height: 1.6;
  color: #4a5568;
  margin: 0;
}

.team-description.placeholder {
  color: #a0aec0;
  font-style: italic;
}

.team-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  padding: 6px 12px;
  background: linear-gradient(135deg, #e2e8f0, #cbd5e0);
  color: #475569;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  transition: all 0.2s ease;
}

.tag:hover {
  background: linear-gradient(135deg, #cbd5e0, #94a3b8);
  color: #334155;
  transform: scale(1.05);
}

/* ===== 统计数据 ===== */
.preview-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 0 24px 24px;
}

.stat-card {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(248, 250, 252, 0.6) 100%);
  border: 1px solid rgba(226, 232, 240, 0.5);
  border-radius: 16px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  border-color: rgba(102, 126, 234, 0.3);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 800;
  color: #1a202c;
  margin-bottom: 4px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

/* ===== 团队成员 ===== */
.preview-members {
  padding: 0 24px 24px;
}

.section-title {
  font-size: 18px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title i {
  color: #667eea;
  font-size: 16px;
}

.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 12px;
}

.member-card {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(226, 232, 240, 0.5);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s ease;
  position: relative;
  backdrop-filter: blur(10px);
}

.member-card:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.member-info {
  flex: 1;
  min-width: 0;
}

.member-name {
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.member-role {
  font-size: 12px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.online-indicator {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  position: absolute;
  top: 12px;
  right: 12px;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.more-members-card {
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  border: 2px dashed #cbd5e0;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-align: center;
  color: #64748b;
  transition: all 0.2s ease;
}

.more-members-card:hover {
  background: linear-gradient(135deg, #e2e8f0, #cbd5e0);
  border-color: #94a3b8;
}

.more-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #cbd5e0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #64748b;
}

.more-text {
  font-size: 12px;
  font-weight: 500;
}

/* ===== 最近活动 ===== */
.preview-activities {
  padding: 0 24px 24px;
}

.activities-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activity-item {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(226, 232, 240, 0.5);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.activity-item:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateX(4px);
}

.activity-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-text {
  font-size: 14px;
  color: #1a202c;
  margin-bottom: 4px;
  line-height: 1.4;
}

.activity-time {
  font-size: 12px;
  color: #64748b;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

/* ===== 操作按钮 ===== */
.preview-actions {
  padding: 24px;
  border-top: 1px solid rgba(226, 232, 240, 0.5);
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.5) 0%,
    rgba(255, 255, 255, 0.8) 100%);
  display: flex;
  gap: 12px;
  border-radius: 0 0 24px 24px;
}

.action-btn {
  padding: 14px 24px;
  border: none;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.action-btn.large {
  flex: 1;
  padding: 16px 24px;
  font-size: 15px;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.8);
  color: #64748b;
  border: 1px solid rgba(226, 232, 240, 0.8);
  backdrop-filter: blur(10px);
}

.action-btn.secondary:hover {
  background: rgba(255, 255, 255, 1);
  color: #475569;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  .team-preview-modal {
    padding: 12px;
  }

  .preview-container {
    border-radius: 20px;
    max-height: 95vh;
  }

  .team-cover {
    height: 120px;
  }

  .team-avatar-large {
    width: 64px;
    height: 64px;
  }

  .team-basic-info {
    padding: 20px;
  }

  .team-name {
    font-size: 24px;
  }

  .preview-stats {
    grid-template-columns: 1fr;
    padding: 0 20px 20px;
  }

  .members-grid {
    grid-template-columns: 1fr;
  }

  .preview-actions {
    flex-direction: column;
    padding: 20px;
  }

  .action-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .preview-container {
    margin: 0;
    border-radius: 16px;
    max-height: 100vh;
  }

  .team-basic-info {
    padding: 16px;
  }

  .preview-stats,
  .preview-members,
  .preview-activities {
    padding-left: 16px;
    padding-right: 16px;
  }

  .preview-actions {
    padding: 16px;
  }
}
</style>
