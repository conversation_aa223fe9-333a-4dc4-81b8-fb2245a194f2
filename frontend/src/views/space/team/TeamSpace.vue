<template>
  <Layout>
    <div class="modern-team-spaces-page">
      <!-- 英雄区域 -->
      <div class="hero-section">
        <div class="hero-background">
          <div class="gradient-bg"></div>
          <div class="pattern-overlay"></div>
        </div>

        <div class="container">
          <div class="hero-content">
            <div class="hero-text">
              <h1 class="hero-title">团队空间</h1>
              <p class="hero-subtitle">连接团队，激发创新，共创未来</p>
              <div class="hero-stats">
                <div class="stat-item">
                  <div class="stat-number">{{ totalTeams }}</div>
                  <div class="stat-label">活跃团队</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ totalMembers }}</div>
                  <div class="stat-label">团队成员</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ totalProjects }}</div>
                  <div class="stat-label">进行项目</div>
                </div>
              </div>
            </div>

            <div class="hero-actions">
              <button class="btn btn-primary btn-large" @click="goToCreateTeam">
                <i class="fas fa-plus"></i>
                <span>创建团队空间</span>
              </button>
              <button class="btn btn-outline btn-large" @click="exploreTeams">
                <i class="fas fa-compass"></i>
                <span>探索团队</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <div class="container">
          <!-- 快速导航 -->
          <div class="quick-nav">
            <div class="nav-cards">
              <div class="nav-card" @click="setFilterType('my-teams')">
                <div class="nav-icon">
                  <i class="fas fa-user-friends"></i>
                </div>
                <div class="nav-content">
                  <h3>我的团队</h3>
                  <p>{{ myTeamsCount }} 个团队</p>
                </div>
              </div>

              <div class="nav-card" @click="setFilterType('recent')">
                <div class="nav-icon">
                  <i class="fas fa-clock"></i>
                </div>
                <div class="nav-content">
                  <h3>最近访问</h3>
                  <p>{{ recentTeamsCount }} 个团队</p>
                </div>
              </div>

              <div class="nav-card" @click="setFilterType('starred')">
                <div class="nav-icon">
                  <i class="fas fa-star"></i>
                </div>
                <div class="nav-content">
                  <h3>收藏团队</h3>
                  <p>{{ starredTeamsCount }} 个团队</p>
                </div>
              </div>

              <div class="nav-card" @click="setFilterType('public')">
                <div class="nav-icon">
                  <i class="fas fa-globe"></i>
                </div>
                <div class="nav-content">
                  <h3>公开团队</h3>
                  <p>{{ publicTeamsCount }} 个团队</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 搜索和过滤 -->
          <div class="search-filter-section">
            <div class="search-container">
              <div class="search-box">
                <i class="fas fa-search"></i>
                <input
                  type="text"
                  placeholder="搜索团队空间、成员或项目..."
                  v-model="searchQuery"
                  @input="filterTeams"
                >
                <button v-if="searchQuery" class="clear-search" @click="clearSearch">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>

            <div class="filter-controls">
              <div class="filter-tabs">
                <button
                  v-for="filter in filterOptions"
                  :key="filter.key"
                  class="filter-tab"
                  :class="{ active: filterType === filter.key }"
                  @click="setFilterType(filter.key)"
                >
                  <i :class="filter.icon"></i>
                  <span>{{ filter.label }}</span>
                  <div class="tab-indicator"></div>
                </button>
              </div>

              <div class="view-controls">
                <div class="view-toggle">
                  <button
                    class="view-btn"
                    :class="{ active: viewMode === 'grid' }"
                    @click="setViewMode('grid')"
                    title="网格视图"
                  >
                    <i class="fas fa-th"></i>
                  </button>
                  <button
                    class="view-btn"
                    :class="{ active: viewMode === 'list' }"
                    @click="setViewMode('list')"
                    title="列表视图"
                  >
                    <i class="fas fa-list"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 结果统计 -->
          <div class="results-header">
            <div class="results-info">
              <h2>{{ getFilterTitle() }}</h2>
              <p>
                显示 {{ teams.length }} / {{ totalCount }} 个团队空间
                <span v-if="filteredTeams.length !== teams.length">
                  （筛选后 {{ filteredTeams.length }} 个）
                </span>
              </p>
            </div>

            <div class="results-actions">
              <button class="btn btn-outline btn-sm" @click="refreshTeams">
                <i class="fas fa-sync-alt"></i>
                刷新
              </button>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="loading-state">
            <div class="loading-spinner">
              <div class="spinner"></div>
            </div>
            <p>正在加载团队数据...</p>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="error" class="error-state">
            <div class="error-icon">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3>加载失败</h3>
            <p>{{ error }}</p>
            <div class="error-actions">
              <button class="btn btn-primary" @click="loadTeamsData">
                <i class="fas fa-sync-alt"></i>
                重试
              </button>
            </div>
          </div>



          <!-- 团队列表 -->
          <div v-if="!loading && !error && filteredTeams.length > 0" class="teams-container">
            <!-- 网格视图 -->
            <div v-if="viewMode === 'grid'" class="teams-grid">
              <div
                v-for="team in filteredTeams"
                :key="team.id"
                class="team-card"
                :class="{
                  'is-member': team.isMember,
                  'is-admin': team.isAdmin,
                  'is-public': team.isPublic
                }"
              >
                <!-- 卡片头部 -->
                <div class="card-header" @click="enterTeam(team)">
                  <!-- 团队头像 -->
                  <div class="team-avatar">
                    <img v-if="team.avatar" :src="team.avatar" :alt="team.name">
                    <div v-else class="avatar-placeholder">
                      <i class="fas fa-users"></i>
                    </div>
                    <div v-if="team.isActive" class="online-dot"></div>
                  </div>

                  <!-- 收藏按钮 -->
                  <button
                    class="favorite-btn"
                    :class="{ active: team.isStarred }"
                    @click.stop="toggleStar(team)"
                    :title="team.isStarred ? '取消收藏' : '收藏团队'"
                  >
                    <i class="fas fa-star"></i>
                  </button>
                </div>

                <!-- 团队信息 -->
                <div class="card-body" @click="enterTeam(team)">
                  <!-- 团队名称和状态 -->
                  <div class="team-title">
                    <h3 class="team-name">{{ team.name }}</h3>
                    <div class="team-badges">
                      <span v-if="team.isAdmin" class="badge admin">
                        <i class="fas fa-crown"></i>
                        管理员
                      </span>
                      <span v-else-if="team.isMember" class="badge member">
                        <i class="fas fa-check"></i>
                        已加入
                      </span>
                      <span class="badge visibility" :class="team.isPublic ? 'public' : 'private'">
                        <i :class="team.isPublic ? 'fas fa-globe' : 'fas fa-lock'"></i>
                        {{ team.isPublic ? '公开' : '私有' }}
                      </span>
                    </div>
                  </div>

                  <!-- 团队描述 -->
                  <p class="team-description">{{ team.description || '这个团队还没有添加描述...' }}</p>

                  <!-- 团队标签 -->
                  <div class="team-tags" v-if="team.tags && team.tags.length > 0">
                    <span
                      v-for="tag in team.tags.slice(0, 3)"
                      :key="tag"
                      class="tag"
                    >
                      {{ tag }}
                    </span>
                    <span v-if="team.tags.length > 3" class="tag-more">
                      +{{ team.tags.length - 3 }}
                    </span>
                  </div>

                  <!-- 团队统计 -->
                  <div class="team-stats">
                    <div class="stat">
                      <div class="stat-number">{{ formatNumber(team.articlesCount || 0) }}</div>
                      <div class="stat-label">内容</div>
                    </div>
                    <div class="stat">
                      <div class="stat-number">{{ formatNumber(team.viewsCount || 0) }}</div>
                      <div class="stat-label">浏览</div>
                    </div>
                    <div class="stat">
                      <div class="stat-number">{{ formatNumber(team.totalLikes || 0) }}</div>
                      <div class="stat-label">点赞</div>
                    </div>
                    <div class="stat">
                      <div class="stat-number">{{ team.activityScore || 0 }}</div>
                      <div class="stat-label">活跃度</div>
                    </div>
                  </div>
                </div>

                <!-- 卡片底部 -->
                <div class="card-footer">
                  <!-- 成员头像 -->
                  <div class="members-preview">
                    <div class="member-avatars">
                      <div
                        v-for="member in team.members?.slice(0, 4)"
                        :key="member.userId"
                        class="member-avatar"
                        :title="`${member.displayName} - ${member.role === 'admin' ? '管理员' : '成员'}`"
                      >
                        <img
                          :src="member.avatarUrl || `https://api.dicebear.com/7.x/avataaars/svg?seed=${member.displayName}`"
                          :alt="member.displayName"
                        >
                        <div v-if="member.role === 'admin'" class="admin-badge">
                          <i class="fas fa-crown"></i>
                        </div>
                      </div>
                      <div v-if="team.membersCount > 4" class="more-count">
                        +{{ team.membersCount - 4 }}
                      </div>
                    </div>
                    <div class="member-info">
                      <span class="member-count">{{ team.membersCount }} 成员</span>
                      <span class="create-time">{{ formatCreateTime(team.createdAt) }}</span>
                    </div>
                  </div>

                  <!-- 操作按钮 -->
                  <button
                    v-if="team.isMember"
                    class="action-btn entered"
                    @click="enterTeam(team)"
                  >
                    <i class="fas fa-sign-in-alt"></i>
                    进入团队
                  </button>
                  <button
                    v-else
                    class="action-btn join"
                    @click.stop="showJoinDialog(team)"
                  >
                    <i class="fas fa-plus"></i>
                    申请加入
                  </button>
                </div>
              </div>
            </div>

            <!-- 加载更多按钮 -->
            <div v-if="hasMore && !loadingMore" class="load-more-container">
              <button class="btn btn-outline load-more-btn" @click="loadMoreTeams">
                <i class="fas fa-chevron-down"></i>
                加载更多团队
              </button>
            </div>

            <!-- 加载更多状态 -->
            <div v-if="loadingMore" class="loading-more">
              <div class="loading-spinner"></div>
              <span>正在加载更多团队...</span>
            </div>

            <!-- 没有更多数据提示 -->
            <div v-if="!hasMore && teams.length > 0" class="no-more-data">
              <i class="fas fa-check-circle"></i>
              <span>已显示全部 {{ totalCount }} 个团队</span>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else-if="!loading && !error && filteredTeams.length === 0" class="empty-state">
            <div class="empty-icon">
              <i class="fas fa-users"></i>
            </div>
            <h3>{{ getEmptyStateTitle() }}</h3>
            <p>{{ getEmptyStateDescription() }}</p>
            <div class="empty-actions">
              <button class="btn btn-primary" @click="goToCreateTeam" v-if="filterType === 'my-teams'">
                <i class="fas fa-plus"></i>
                创建团队空间
              </button>
              <button class="btn btn-outline" @click="clearFilters" v-else>
                <i class="fas fa-filter"></i>
                清除筛选
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加入团队对话框 -->
    <Teleport to="body">
      <div v-if="showJoinModal" class="join-modal-overlay" @click="closeJoinDialog">
        <div class="join-modal" @click.stop>
          <!-- 模态框头部 -->
          <div class="join-modal-header">
            <div class="header-content">
              <div class="modal-icon">
                <i class="fas fa-user-plus"></i>
              </div>
              <div class="header-text">
                <h2>申请加入团队</h2>
                <p>向团队管理员发送加入申请</p>
              </div>
            </div>
            <button class="close-btn" @click="closeJoinDialog" :disabled="isSubmittingJoin">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <!-- 团队信息卡片 -->
          <div class="team-preview-card">
            <div class="team-cover-mini">
              <div class="cover-gradient" :style="{ background: selectedTeam?.themeColor }"></div>
              <div class="team-avatar-mini">
                <img v-if="selectedTeam?.avatar" :src="selectedTeam.avatar" :alt="selectedTeam?.name">
                <div v-else class="avatar-placeholder">
                  <i class="fas fa-users"></i>
                </div>
              </div>
            </div>
            <div class="team-info-mini">
              <h3>{{ selectedTeam?.name }}</h3>
              <p>{{ selectedTeam?.description || '这个团队还没有添加描述...' }}</p>
              <div class="team-stats-mini">
                <span class="stat">
                  <i class="fas fa-users"></i>
                  {{ selectedTeam?.membersCount || 0 }} 成员
                </span>
                <span class="stat">
                  <i class="fas fa-eye"></i>
                  {{ formatNumber(selectedTeam?.viewsCount || 0) }} 浏览
                </span>
                <span class="badge" :class="selectedTeam?.isPublic ? 'public' : 'private'">
                  <i :class="selectedTeam?.isPublic ? 'fas fa-globe' : 'fas fa-lock'"></i>
                  {{ selectedTeam?.isPublic ? '公开团队' : '私有团队' }}
                </span>
              </div>
            </div>
          </div>

          <!-- 申请表单 -->
          <div class="join-form">
            <div class="form-section">
              <label class="form-label">
                <i class="fas fa-edit"></i>
                申请理由
                <span class="required">*</span>
              </label>
              <div class="textarea-wrapper">
                <textarea
                  v-model="joinReason"
                  class="join-textarea"
                  placeholder="请介绍一下您的背景和加入团队的原因，这将帮助管理员更好地了解您..."
                  rows="4"
                  maxlength="300"
                  :disabled="isSubmittingJoin"
                  @input="validateForm"
                ></textarea>
                <div class="textarea-footer">
                  <div class="char-count" :class="{ warning: joinReason.length > 250 }">
                    {{ joinReason.length }}/300
                  </div>
                  <div class="form-tips">
                    <i class="fas fa-lightbulb"></i>
                    建议包含：技能背景、兴趣方向、期望贡献
                  </div>
                </div>
              </div>
            </div>

            <!-- 快速模板 -->
            <div class="quick-templates" v-if="!joinReason.trim()">
              <div class="templates-label">
                <i class="fas fa-magic"></i>
                快速模板
              </div>
              <div class="template-buttons">
                <button
                  v-for="template in joinTemplates"
                  :key="template.id"
                  class="template-btn"
                  @click="useTemplate(template.content)"
                >
                  {{ template.label }}
                </button>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="join-modal-footer">
            <button
              class="btn-secondary"
              @click="closeJoinDialog"
              :disabled="isSubmittingJoin"
            >
              <i class="fas fa-times"></i>
              取消
            </button>
            <button
              class="btn-primary"
              @click="submitJoinApplication"
              :disabled="!isFormValid || isSubmittingJoin"
              :class="{ loading: isSubmittingJoin }"
            >
              <div class="btn-content">
                <i v-if="isSubmittingJoin" class="fas fa-spinner fa-spin"></i>
                <i v-else class="fas fa-paper-plane"></i>
                <span>{{ isSubmittingJoin ? '提交中...' : '发送申请' }}</span>
              </div>
            </button>
          </div>
        </div>
      </div>
    </Teleport>
  </Layout>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import Layout from '../../../components/Layout.vue'
import { useToastStore } from '../../../stores/toast'
import { useUserStore } from '../../../stores/user'
import teamService from '../../../services/teamService'
import userService from '../../../services/userService'

export default {
  name: 'ModernTeamSpaces',
  components: {
    Layout
  },
  setup() {
    const router = useRouter()
    const toastStore = useToastStore()
    const userStore = useUserStore()

    // 响应式数据
    const loading = ref(false)
    const error = ref(null)
    const searchQuery = ref('')
    const filterType = ref('all')
    const viewMode = ref('grid')

    // 分页数据
    const currentPage = ref(1)
    const pageSize = ref(12)
    const totalCount = ref(0)
    const hasMore = ref(true)
    const loadingMore = ref(false)

    // 统计数据
    const totalTeams = ref(0)
    const totalMembers = ref(0)
    const totalProjects = ref(0)

    // 团队数据
    const teams = ref([])
    const myTeams = ref([])

    // 加入团队相关
    const showJoinModal = ref(false)
    const selectedTeam = ref(null)
    const joinReason = ref('')
    const isSubmittingJoin = ref(false)
    const isFormValid = computed(() => joinReason.value.trim().length >= 10)

    // 申请理由快速模板
    const joinTemplates = ref([
      {
        id: 1,
        label: '技术学习',
        content: '我对团队的技术方向很感兴趣，希望能够学习相关知识并参与技术讨论，同时也愿意分享自己的经验和见解。'
      },
      {
        id: 2,
        label: '项目合作',
        content: '我有相关的项目经验，希望能够参与团队的项目开发，贡献自己的专业技能，与团队成员共同完成有价值的工作。'
      },
      {
        id: 3,
        label: '知识分享',
        content: '我在相关领域有一定的积累，希望能够与团队成员分享知识和经验，同时也期待从其他成员那里学到新的内容。'
      }
    ])

    // 加载团队数据
    const loadTeamsData = async (page = 1, append = false) => {
      try {
        if (!append) {
          loading.value = true
          error.value = null
        } else {
          loadingMore.value = true
        }

        console.log('开始加载团队数据...', { page, append })

        // 获取当前用户ID
        const currentUserId = userStore.user?.id || 1

        // 并行获取所有团队和用户团队
        const [allTeamsResponse, userTeamsResponse] = await Promise.all([
          teamService.getAllTeams({ page, pageSize: pageSize.value }),
          userService.getUserTeams(currentUserId)
        ])

        // 处理所有团队数据
        if (allTeamsResponse) {
          // API服务已经处理了响应格式，直接使用data
          // 原始后台格式: { code: 200, data: { page, pageSize, total, list: [...] } }
          // API服务返回: { page, pageSize, total, list: [...] }
          const teamsArray = allTeamsResponse?.list || []

          // 更新分页信息
          currentPage.value = allTeamsResponse.page || page
          totalCount.value = allTeamsResponse.total || 0
          hasMore.value = (currentPage.value * pageSize.value) < totalCount.value

          if (Array.isArray(teamsArray) && teamsArray.length > 0) {
            const currentUserId = userStore.user?.id // 获取当前用户ID

            const newTeams = teamsArray.map(team => {
              // 从后端数据中提取更丰富的信息
              const achievements = team.achievements || {}
              const members = team.members || []

              // 判断用户与团队的关系
              const userMember = members.find(m => m.userId === currentUserId)
              const isMember = !!userMember
              const isAdmin = userMember?.role === 'admin'
              const isMyTeam = team.creatorId === currentUserId || isMember

              return {
                id: team.teamId || team.id,
                teamId: team.teamId || team.id,
                name: team.name || '未命名团队',
                description: team.description || '',
                avatar: team.avatarUrl,
                avatarUrl: team.avatarUrl,

                // 主题色彩 - 使用项目主色调
                themeColor: generateTeamTheme(team.tags, team.privacy),

                // 团队属性 - 根据接口定义，privacy为"0"(私有)或"1"(公开)
                isPublic: team.privacy === '1' || team.privacy === 1,
                isPrivate: team.privacy === '0' || team.privacy === 0,
                privacy: team.privacy,
                inviteSetting: team.inviteSetting || 'admin_approval',

                // 用户关系
                isMember,
                isAdmin,
                isMyTeam,
                isStarred: false, // TODO: 从用户收藏数据中获取
                isActive: team.isActive !== false,

                // 推荐和特色标识
                isRecommended: achievements.articlesRecommended > 50 || achievements.totalViews > 10000,
                isFeatured: achievements.totalViews > 50000 || members.length > 15,

                // 统计数据
                membersCount: team.memberCount || members.length || 0,
                articlesCount: achievements.articlesRecommended || 0,
                viewsCount: achievements.totalViews || 0,
                likesCount: achievements.totalLikes || 0,
                favoritesCount: achievements.totalFavorites || 0,
                totalLikes: achievements.totalLikes || 0,

                // 团队标签和分类
                tags: team.tags || [],
                category: team.category || getCategoryFromTags(team.tags),

                // 成员信息
                members: members,
                recentMembers: members.slice(0, 5),
                adminMembers: members.filter(m => m.role === 'admin'),

                // 时间信息
                createdAt: team.createdAt,
                lastActivity: getLastActivity(team, members),

                // 活跃度评分
                activityScore: calculateActivityScore(achievements, members.length)
              }
            })

            // 根据是否追加来更新teams数组
            if (append) {
              teams.value = [...teams.value, ...newTeams]
            } else {
              teams.value = newTeams
            }

            // 更新统计数据（使用总数而不是当前页数据）
            totalTeams.value = totalCount.value
            totalMembers.value = teams.value.reduce((sum, team) => sum + (team.membersCount || 0), 0)
            totalProjects.value = teams.value.reduce((sum, team) => sum + (team.articlesCount || 0), 0)

          } else {
            if (!append) {
              teams.value = []
              totalTeams.value = 0
              totalMembers.value = 0
              totalProjects.value = 0
            }
          }
        } else {
          if (!append) {
            teams.value = []
            totalTeams.value = 0
            totalMembers.value = 0
            totalProjects.value = 0
          }
        }

        // 处理用户团队数据
        if (userTeamsResponse) {
          const userTeamsArray = Array.isArray(userTeamsResponse) ? userTeamsResponse : (userTeamsResponse?.data || [])

          if (Array.isArray(userTeamsArray)) {
            myTeams.value = userTeamsArray.map(team => ({
              teamId: team.teamId,
              name: team.name,
              description: team.description || '',
              avatarUrl: team.avatarUrl,
              memberCount: team.memberCount || 0,
              recommendationCount: team.recommendationCount || 0,
              joinedAt: team.joinedAt
            }))

            // 标记用户已加入的团队
            const userTeamIds = new Set(myTeams.value.map(team => team.teamId))
            teams.value.forEach(team => {
              if (userTeamIds.has(team.teamId)) {
                team.isMember = true
              }
            })
          }
        }



      } catch (err) {
        console.error('加载团队数据失败:', err)
        error.value = err.message || '加载团队数据失败'
        toastStore.error('加载团队数据失败: ' + (err.message || '未知错误'))

        // 使用默认数据作为降级方案
        teams.value = []
        myTeams.value = []
        totalTeams.value = 0
        totalMembers.value = 0
        totalProjects.value = 0
      } finally {
        loading.value = false
        loadingMore.value = false
      }
    }



    // 过滤选项
    const filterOptions = ref([
      { key: 'all', label: '全部团队', icon: 'fas fa-globe' },
      { key: 'my-teams', label: '我的团队', icon: 'fas fa-user-friends' },
      { key: 'admin', label: '我管理的', icon: 'fas fa-crown' },
      { key: 'member', label: '我参与的', icon: 'fas fa-users' },
      { key: 'starred', label: '收藏团队', icon: 'fas fa-star' },
      { key: 'public', label: '公开团队', icon: 'fas fa-globe' },
      { key: 'private', label: '私有团队', icon: 'fas fa-lock' },
      { key: 'recommended', label: '推荐团队', icon: 'fas fa-fire' },
      { key: 'recent', label: '最近访问', icon: 'fas fa-clock' }
    ])

    // 计算属性
    const filteredTeams = computed(() => {
      let result = teams.value

      // 首先应用可见性规则：只显示公开团队和我的团队，隐藏无关的私有团队
      result = result.filter(team => {
        if (team.isPublic) return true // 公开团队对所有人可见
        if (team.isMyTeam) return true // 我的团队（创建的或加入的）可见
        return false // 隐藏其他私有团队
      })

      // 按类型过滤
      if (filterType.value !== 'all') {
        switch (filterType.value) {
          case 'my-teams':
            // 我的团队：我创建的和我加入的团队
            result = result.filter(team => team.isMyTeam)
            break
          case 'recent':
            result = result.filter(team => team.isMember).slice(0, 5)
            break
          case 'starred':
            result = result.filter(team => team.isStarred)
            break
          case 'public':
            result = result.filter(team => team.isPublic)
            break
          case 'private':
            // 私有团队：只显示我加入的私有团队
            result = result.filter(team => team.isPrivate && team.isMyTeam)
            break
          case 'recommended':
            result = result.filter(team => team.isRecommended)
            break
          case 'admin':
            // 我管理的团队
            result = result.filter(team => team.isAdmin)
            break
          case 'member':
            // 我作为成员的团队（非管理员）
            result = result.filter(team => team.isMember && !team.isAdmin)
            break
        }
      }

      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        result = result.filter(team => {
          const nameMatch = team.name.toLowerCase().includes(query)
          const descMatch = team.description.toLowerCase().includes(query)
          const tagsMatch = Array.isArray(team.tags) && team.tags.some(tag => tag.toLowerCase().includes(query))
          return nameMatch || descMatch || tagsMatch
        })
      }

      // 按最近活跃排序
      result.sort((a, b) => new Date(b.lastActivity) - new Date(a.lastActivity))

      return result
    })

    const myTeamsCount = computed(() => myTeams.value.length)
    const recentTeamsCount = computed(() => myTeams.value.slice(0, 5).length)
    const starredTeamsCount = computed(() => teams.value.filter(team => team.isStarred).length)
    const publicTeamsCount = computed(() => teams.value.filter(team => team.isPublic).length)

    // 方法
    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }

    const formatTime = (dateString) => {
      const now = new Date()
      const time = new Date(dateString)
      const diff = now - time

      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)

      if (days > 0) return `${days}天前`
      if (hours > 0) return `${hours}小时前`
      if (minutes > 0) return `${minutes}分钟前`
      return '刚刚'
    }

    // 格式化创建时间
    const formatCreateTime = (dateString) => {
      if (!dateString) return '未知'
      const target = new Date(dateString)
      const now = new Date()
      const diff = now - target
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (days === 0) return '今天创建'
      if (days === 1) return '昨天创建'
      if (days < 7) return `${days}天前创建`
      if (days < 30) return `${Math.floor(days / 7)}周前创建`
      if (days < 365) return `${Math.floor(days / 30)}个月前创建`

      return `${Math.floor(days / 365)}年前创建`
    }

    const getFilterTitle = () => {
      const option = filterOptions.value.find(opt => opt.key === filterType.value)
      return option ? option.label : '团队空间'
    }

    const getEmptyStateTitle = () => {
      switch (filterType.value) {
        case 'my-teams':
          return '您还没有加入任何团队'
        case 'starred':
          return '您还没有收藏任何团队'
        case 'recent':
          return '暂无最近访问的团队'
        default:
          return '暂无团队空间'
      }
    }

    const getEmptyStateDescription = () => {
      switch (filterType.value) {
        case 'my-teams':
          return '创建或加入团队，开始协作之旅'
        case 'starred':
          return '收藏感兴趣的团队，方便快速访问'
        case 'recent':
          return '访问团队后会在这里显示'
        default:
          return '当前没有找到符合条件的团队空间'
      }
    }

    const setFilterType = (type) => {
      filterType.value = type
    }

    const setViewMode = (mode) => {
      viewMode.value = mode
    }

    const clearSearch = () => {
      searchQuery.value = ''
      filterTeams()
    }

    const clearFilters = () => {
      searchQuery.value = ''
      filterType.value = 'all'
    }

    const filterTeams = () => {
      // 过滤逻辑已在computed中实现
    }

    const refreshTeams = async () => {
      currentPage.value = 1
      await loadTeamsData(1, false)
      if (!error.value) {
        toastStore.success('团队数据已刷新')
      }
    }

    // 加载更多团队
    const loadMoreTeams = async () => {
      if (hasMore.value && !loadingMore.value) {
        await loadTeamsData(currentPage.value + 1, true)
      }
    }

    const toggleStar = async (team) => {
      try {
        const wasStarred = team.isStarred

        if (wasStarred) {
          await teamService.unstarTeam(team.id)
          team.isStarred = false
          toastStore.success('已取消收藏团队')
        } else {
          await teamService.starTeam(team.id)
          team.isStarred = true
          toastStore.success('已收藏团队')
        }
      } catch (error) {
        console.error('切换团队收藏状态失败:', error)
        toastStore.error('操作失败: ' + (error.message || '未知错误'))
      }
    }

    const showTeamMenu = (team) => {
      toastStore.info('团队菜单功能开发中...')
    }

    // 显示加入团队对话框
    const showJoinDialog = (team) => {
      selectedTeam.value = team
      joinReason.value = ''
      showJoinModal.value = true
    }

    // 关闭加入团队对话框
    const closeJoinDialog = () => {
      showJoinModal.value = false
      selectedTeam.value = null
      joinReason.value = ''
      isSubmittingJoin.value = false
    }

    // 使用快速模板
    const useTemplate = (content) => {
      joinReason.value = content
    }

    // 表单验证
    const validateForm = () => {
      // 实时验证逻辑可以在这里扩展
      return joinReason.value.trim().length >= 10
    }

    // 生成团队主题色彩 - 使用项目主色调
    const generateTeamTheme = (tags = [], privacy = 'private') => {
      const themes = {
        'AIGC': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'AI': 'linear-gradient(135deg, #667eea 0%, #8b5cf6 100%)',
        'Python': 'linear-gradient(135deg, #667eea 0%, #3b82f6 100%)',
        'JavaScript': 'linear-gradient(135deg, #667eea 0%, #10b981 100%)',
        'Vue': 'linear-gradient(135deg, #667eea 0%, #06b6d4 100%)',
        'React': 'linear-gradient(135deg, #667eea 0%, #8b5cf6 100%)',
        '前端': 'linear-gradient(135deg, #667eea 0%, #06b6d4 100%)',
        '后端': 'linear-gradient(135deg, #667eea 0%, #6366f1 100%)',
        '算法': 'linear-gradient(135deg, #667eea 0%, #8b5cf6 100%)',
        '设计': 'linear-gradient(135deg, #667eea 0%, #ec4899 100%)',
        '产品': 'linear-gradient(135deg, #667eea 0%, #f59e0b 100%)',
        '运营': 'linear-gradient(135deg, #667eea 0%, #10b981 100%)'
      }

      // 根据标签选择主题
      for (const tag of tags) {
        if (themes[tag]) return themes[tag]
      }

      // 默认主题 - 使用项目主色调
      return 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }

    // 从标签推断分类
    const getCategoryFromTags = (tags = []) => {
      const categoryMap = {
        'AIGC': 'ai',
        'AI': 'ai',
        '大语言模型': 'ai',
        'Python': 'programming',
        'JavaScript': 'programming',
        'Vue': 'frontend',
        'React': 'frontend',
        '前端开发': 'frontend',
        '后端开发': 'backend',
        '算法': 'algorithm',
        '设计': 'design'
      }

      for (const tag of tags) {
        if (categoryMap[tag]) return categoryMap[tag]
      }
      return 'general'
    }

    // 获取最后活跃时间
    const getLastActivity = (team, members = []) => {
      if (team.lastActivity) return team.lastActivity

      // 如果没有明确的最后活跃时间，使用成员加入时间
      const latestJoin = members.reduce((latest, member) => {
        const joinTime = new Date(member.joinedAt || 0)
        return joinTime > latest ? joinTime : latest
      }, new Date(team.createdAt || 0))

      return latestJoin.toISOString()
    }

    // 计算活跃度评分
    const calculateActivityScore = (achievements = {}, memberCount = 0) => {
      const views = achievements.totalViews || 0
      const likes = achievements.totalLikes || 0
      const articles = achievements.articlesRecommended || 0

      // 简单的活跃度评分算法
      return Math.min(100, Math.floor(
        (views / 1000) * 0.3 +
        (likes / 100) * 0.4 +
        articles * 0.2 +
        memberCount * 0.1
      ))
    }

    // 提交加入申请
    const submitJoinApplication = async () => {
      if (!joinReason.value.trim()) {
        toastStore.error('请填写申请理由')
        return
      }

      if (!selectedTeam.value) {
        toastStore.error('请选择要加入的团队')
        return
      }

      isSubmittingJoin.value = true

      try {
        const applicationData = {
          reason: joinReason.value.trim()
        }

        await teamService.applyToJoinTeam(selectedTeam.value.teamId, applicationData)

        toastStore.success('申请已提交，等待管理员审核')
        closeJoinDialog()

        // 可选：刷新团队数据以更新状态
        // await refreshTeams()

      } catch (error) {
        console.error('申请加入团队失败:', error)

        if (error.response?.status === 409) {
          toastStore.error('您已是该团队成员或已有待审核的申请')
        } else {
          toastStore.error('申请失败: ' + (error.message || '未知错误'))
        }
      } finally {
        isSubmittingJoin.value = false
      }
    }

    const enterTeam = (team) => {
      router.push(`/team-space/${team.id}`)
    }

    const goToCreateTeam = () => {
      router.push('/team-space/create')
    }

    const exploreTeams = () => {
      setFilterType('public')
    }

    // 调试方法
    const debugTeamsData = () => {
      console.log('=== 团队数据调试信息 ===')
      console.log('teams.value:', teams.value)
      console.log('filteredTeams.value:', filteredTeams.value)
      console.log('loading.value:', loading.value)
      console.log('error.value:', error.value)
      console.log('filterType.value:', filterType.value)
      console.log('searchQuery.value:', searchQuery.value)
    }

    // 生命周期
    onMounted(() => {
      loadTeamsData()
      // 延迟调试，确保数据加载完成
      setTimeout(debugTeamsData, 2000)
    })

    return {
      // 响应式数据
      loading,
      error,
      searchQuery,
      filterType,
      viewMode,
      currentPage,
      pageSize,
      totalCount,
      hasMore,
      loadingMore,
      totalTeams,
      totalMembers,
      totalProjects,
      teams,
      myTeams,
      filterOptions,

      // 加入团队相关
      showJoinModal,
      selectedTeam,
      joinReason,
      isSubmittingJoin,
      isFormValid,
      joinTemplates,

      // 计算属性
      filteredTeams,
      myTeamsCount,
      recentTeamsCount,
      starredTeamsCount,
      publicTeamsCount,

      // 方法
      formatNumber,
      formatTime,
      formatCreateTime,
      getFilterTitle,
      getEmptyStateTitle,
      getEmptyStateDescription,
      setFilterType,
      setViewMode,
      clearSearch,
      clearFilters,
      filterTeams,
      refreshTeams,
      loadMoreTeams,
      toggleStar,
      showTeamMenu,
      showJoinDialog,
      closeJoinDialog,
      submitJoinApplication,
      useTemplate,
      validateForm,
      generateTeamTheme,
      getCategoryFromTags,
      getLastActivity,
      calculateActivityScore,
      enterTeam,
      goToCreateTeam,
      exploreTeams,
      loadTeamsData,
      debugTeamsData
    }
  }
}
</script>

<style scoped>
/* 现代化团队空间样式 */
.modern-team-spaces-page {
  background: #f8f9fa;
  min-height: 100vh;
}

/* 英雄区域 */
.hero-section {
  position: relative;
  padding: 80px 0 60px;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.gradient-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.pattern-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  background-size: 100px 100px;
}

.hero-content {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 60px;
  align-items: center;
}

.hero-text {
  color: white;
}

.hero-title {
  font-size: 48px;
  font-weight: 800;
  margin: 0 0 16px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 20px;
  margin: 0 0 32px 0;
  opacity: 0.9;
  font-weight: 400;
}

.hero-stats {
  display: flex;
  gap: 40px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
  font-weight: 500;
}

.hero-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.btn {
  padding: 12px 24px;
  border-radius: 12px;
  border: none;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-decoration: none;
  min-width: 200px;
}

.btn-large {
  padding: 16px 32px;
  font-size: 18px;
}

.btn-primary {
  background: white;
  color: #667eea;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.btn-outline {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-outline:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.btn-sm {
  padding: 8px 16px;
  font-size: 14px;
}

/* 主要内容区域 */
.main-content {
  padding: 40px 0;
}

/* 快速导航 */
.quick-nav {
  margin-bottom: 40px;
}

.nav-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.nav-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.nav-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.nav-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.nav-content h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.nav-content p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

/* 搜索和过滤区域 */
.search-filter-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
}

.search-container {
  margin-bottom: 24px;
}

.search-box {
  position: relative;
  max-width: 600px;
}

.search-box i {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 16px;
}

.search-box input {
  width: 100%;
  padding: 16px 16px 16px 48px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 16px;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.search-box input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.clear-search {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.clear-search:hover {
  background: #e9ecef;
  color: #495057;
}

.filter-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
}

.filter-tabs {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-tab {
  position: relative;
  padding: 12px 20px;
  border: none;
  background: transparent;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  border-bottom: 3px solid transparent;
}

.filter-tab:hover {
  color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.filter-tab.active {
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  border-bottom-color: #667eea;
}

.filter-tab i {
  font-size: 16px;
}

.tab-indicator {
  position: absolute;
  bottom: -3px;
  left: 0;
  right: 0;
  height: 3px;
  background: #667eea;
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.filter-tab.active .tab-indicator {
  transform: scaleX(1);
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.view-toggle {
  display: flex;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 4px;
  border: 1px solid #e9ecef;
}

.view-btn {
  padding: 8px 12px;
  border: none;
  background: transparent;
  color: #6c757d;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-btn:hover {
  color: #667eea;
}

.view-btn.active {
  background: white;
  color: #667eea;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 结果头部 */
.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.results-info h2 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
}

.results-info p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

.results-actions {
  display: flex;
  gap: 12px;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.loading-spinner {
  margin-bottom: 16px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.error-state {
  text-align: center;
  padding: 60px 20px;
  color: #dc3545;
}

.error-icon {
  font-size: 64px;
  margin-bottom: 24px;
  opacity: 0.7;
}

.error-state h3 {
  font-size: 24px;
  margin: 0 0 12px 0;
  color: #495057;
  font-weight: 600;
}

.error-state p {
  margin: 0 0 24px 0;
  font-size: 16px;
  color: #6c757d;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 团队网格 */
.teams-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
}

/* 🎨 极致美观的现代团队卡片 - 告别白色单调！ */
.team-card {
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.95) 25%,
    rgba(241, 245, 249, 0.9) 50%,
    rgba(248, 250, 252, 0.95) 75%,
    rgba(255, 255, 255, 0.9) 100%);
  border-radius: 24px;
  overflow: hidden;
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.08),
    0 8px 32px rgba(0, 0, 0, 0.06),
    0 4px 16px rgba(102, 126, 234, 0.04),
    0 2px 8px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8),
    inset 0 -1px 0 rgba(0, 0, 0, 0.02);
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.team-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(16, 185, 129, 0.02) 0%, transparent 50%);
  opacity: 0.6;
  transition: opacity 0.6s ease;
  pointer-events: none;
  z-index: 1;
}

.team-card:hover {
  transform: translateY(-16px) scale(1.04);
  box-shadow:
    0 40px 80px rgba(0, 0, 0, 0.15),
    0 20px 40px rgba(102, 126, 234, 0.12),
    0 8px 24px rgba(102, 126, 234, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 1),
    inset 0 -1px 0 rgba(102, 126, 234, 0.03);
  border-color: rgba(102, 126, 234, 0.4);
}

.team-card:hover::before {
  opacity: 1;
}

.team-card.is-member {
  border-color: rgba(102, 126, 234, 0.5);
  background: linear-gradient(145deg,
    rgba(102, 126, 234, 0.06) 0%,
    rgba(255, 255, 255, 0.95) 30%,
    rgba(102, 126, 234, 0.03) 70%,
    rgba(255, 255, 255, 0.95) 100%);
  box-shadow:
    0 20px 60px rgba(102, 126, 234, 0.12),
    0 8px 32px rgba(102, 126, 234, 0.08),
    0 4px 16px rgba(102, 126, 234, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.team-card.is-admin {
  border-color: rgba(245, 158, 11, 0.5);
  background: linear-gradient(145deg,
    rgba(245, 158, 11, 0.06) 0%,
    rgba(255, 255, 255, 0.95) 30%,
    rgba(245, 158, 11, 0.03) 70%,
    rgba(255, 255, 255, 0.95) 100%);
  box-shadow:
    0 20px 60px rgba(245, 158, 11, 0.12),
    0 8px 32px rgba(245, 158, 11, 0.08),
    0 4px 16px rgba(245, 158, 11, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.team-card.is-public {
  position: relative;
}

.team-card.is-public::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-top: 20px solid rgba(16, 185, 129, 0.1);
  border-top-right-radius: 20px;
}

/* 🎨 紧凑卡片头部 */
.card-header {
  position: relative;
  padding: 16px 20px 0;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  cursor: pointer;
}

/* 🎭 精美头像设计 */
.team-avatar {
  width: 72px;
  height: 72px;
  border-radius: 20px;
  overflow: hidden;
  background: linear-gradient(145deg,
    rgba(248, 249, 255, 0.9) 0%,
    rgba(232, 236, 255, 0.8) 100%);
  border: 4px solid rgba(255, 255, 255, 0.9);
  box-shadow:
    0 12px 32px rgba(0, 0, 0, 0.12),
    0 6px 16px rgba(102, 126, 234, 0.08),
    0 3px 8px rgba(102, 126, 234, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.8),
    inset 0 -1px 0 rgba(0, 0, 0, 0.02);
  position: relative;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.team-avatar::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg,
    rgba(102, 126, 234, 0.3) 0%,
    rgba(118, 75, 162, 0.3) 50%,
    rgba(16, 185, 129, 0.3) 100%);
  border-radius: 22px;
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: -1;
}

.team-avatar:hover {
  transform: scale(1.12) rotate(3deg);
  box-shadow:
    0 20px 48px rgba(0, 0, 0, 0.18),
    0 10px 24px rgba(102, 126, 234, 0.15),
    0 5px 12px rgba(102, 126, 234, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 1);
  border-color: rgba(102, 126, 234, 0.4);
}

.team-avatar:hover::before {
  opacity: 1;
}

.team-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.team-avatar:hover img {
  transform: scale(1.05);
}

/* 🎭 彩色头像占位符 */
.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg,
    #4f46e5 0%,
    #7c3aed 30%,
    #ec4899 60%,
    #f59e0b 100%);
  color: white;
  font-size: 24px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: all 0.5s ease;
}

.team-avatar:hover .avatar-placeholder {
  background: linear-gradient(135deg,
    #3730a3 0%,
    #581c87 30%,
    #be185d 60%,
    #d97706 100%);
  transform: scale(1.1);
}

/* 🟢 精美在线状态指示器 */
.online-dot {
  position: absolute;
  bottom: -3px;
  right: -3px;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg,
    #10b981 0%,
    #059669 50%,
    #047857 100%);
  border: 3px solid white;
  border-radius: 50%;
  box-shadow:
    0 4px 12px rgba(16, 185, 129, 0.4),
    0 2px 6px rgba(16, 185, 129, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  animation: online-magical-pulse 2.5s ease-in-out infinite;
}

.online-dot::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border: 2px solid rgba(16, 185, 129, 0.3);
  border-radius: 50%;
  animation: online-ring-expand 2.5s ease-in-out infinite;
}

@keyframes online-magical-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow:
      0 4px 12px rgba(16, 185, 129, 0.4),
      0 2px 6px rgba(16, 185, 129, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
  50% {
    transform: scale(1.15);
    box-shadow:
      0 6px 20px rgba(16, 185, 129, 0.6),
      0 3px 10px rgba(16, 185, 129, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }
}

@keyframes online-ring-expand {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.8);
    opacity: 0;
  }
}

/* ⭐ 精美收藏按钮 */
.favorite-btn {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  border: none;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 100%);
  backdrop-filter: blur(10px);
  color: #64748b;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    0 2px 6px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.favorite-btn:hover {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(254, 243, 199, 0.9) 100%);
  color: #f59e0b;
  transform: scale(1.15) rotate(8deg);
  box-shadow:
    0 8px 24px rgba(245, 158, 11, 0.3),
    0 4px 12px rgba(245, 158, 11, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 1);
}

.favorite-btn.active {
  background: linear-gradient(135deg,
    #f59e0b 0%,
    #f97316 50%,
    #ea580c 100%);
  color: white;
  animation: favorite-magical-glow 2.5s ease-in-out infinite alternate;
  box-shadow:
    0 6px 20px rgba(245, 158, 11, 0.4),
    0 3px 10px rgba(245, 158, 11, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

@keyframes favorite-magical-glow {
  0% {
    box-shadow:
      0 6px 20px rgba(245, 158, 11, 0.4),
      0 3px 10px rgba(245, 158, 11, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
  100% {
    box-shadow:
      0 8px 28px rgba(245, 158, 11, 0.5),
      0 4px 14px rgba(245, 158, 11, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }
}

/* 🎨 紧凑卡片主体 */
.card-body {
  padding: 18px 20px;
  flex: 1;
  cursor: pointer;
  transition: all 0.3s ease;
}

.card-body:hover {
  background: linear-gradient(135deg,
    rgba(102, 126, 234, 0.01) 0%,
    rgba(255, 255, 255, 1) 100%);
}

.team-title {
  margin-bottom: 12px;
}

.team-name {
  font-size: 18px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
  line-height: 1.2;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  background: linear-gradient(135deg, #1a202c, #2d3748);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 🏆 紧凑徽章系统 */
.team-badges {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
}

.badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  transition: all 0.3s ease;
}

/* 🏆 精美徽章系统 */
.badge.admin {
  background: linear-gradient(135deg,
    #fef3c7 0%,
    #fde68a 50%,
    #f59e0b 100%);
  color: #92400e;
  border: 1px solid rgba(245, 158, 11, 0.4);
  box-shadow:
    0 4px 12px rgba(245, 158, 11, 0.2),
    0 2px 6px rgba(245, 158, 11, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  animation: admin-badge-glow 3s ease-in-out infinite alternate;
}

.badge.member {
  background: linear-gradient(135deg,
    #dbeafe 0%,
    #bfdbfe 50%,
    #3b82f6 100%);
  color: #1e40af;
  border: 1px solid rgba(59, 130, 246, 0.4);
  box-shadow:
    0 4px 12px rgba(59, 130, 246, 0.2),
    0 2px 6px rgba(59, 130, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  animation: member-badge-pulse 2s ease-in-out infinite alternate;
}

.badge.visibility.public {
  background: linear-gradient(135deg,
    #d1fae5 0%,
    #a7f3d0 50%,
    #10b981 100%);
  color: #065f46;
  border: 1px solid rgba(16, 185, 129, 0.4);
  box-shadow:
    0 4px 12px rgba(16, 185, 129, 0.2),
    0 2px 6px rgba(16, 185, 129, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.badge.visibility.private {
  background: linear-gradient(135deg,
    #f3f4f6 0%,
    #e5e7eb 50%,
    #9ca3af 100%);
  color: #374151;
  border: 1px solid rgba(107, 114, 128, 0.4);
  box-shadow:
    0 4px 12px rgba(107, 114, 128, 0.15),
    0 2px 6px rgba(107, 114, 128, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

@keyframes admin-badge-glow {
  0% {
    box-shadow:
      0 4px 12px rgba(245, 158, 11, 0.2),
      0 2px 6px rgba(245, 158, 11, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
  100% {
    box-shadow:
      0 6px 18px rgba(245, 158, 11, 0.3),
      0 3px 9px rgba(245, 158, 11, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }
}

@keyframes member-badge-pulse {
  0% {
    box-shadow:
      0 4px 12px rgba(59, 130, 246, 0.2),
      0 2px 6px rgba(59, 130, 246, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
  100% {
    box-shadow:
      0 6px 18px rgba(59, 130, 246, 0.3),
      0 3px 9px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }
}

.team-description {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.6;
  margin: 0 0 16px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.team-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 20px;
}

.tag {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background: linear-gradient(135deg, #f7fafc, #edf2f7);
  color: #4a5568;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s ease;
}

.tag:hover {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.tag-more {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
  color: #64748b;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid rgba(203, 213, 225, 0.8);
}

/* 团队描述 */
.team-description {
  padding: 0 16px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.team-description:hover {
  background: #f8f9fa;
}

.team-description p {
  font-size: 13px;
  color: #6c757d;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 团队标签 */
.team-tags {
  padding: 0 16px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.team-tags:hover {
  background: #f8f9fa;
}

.tag {
  display: inline-flex;
  align-items: center;
  padding: 3px 8px;
  background: #f1f5f9;
  color: #495057;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
  margin: 0 4px 4px 0;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.tag:hover {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.tag-more {
  display: inline-flex;
  align-items: center;
  padding: 3px 8px;
  background: #e9ecef;
  color: #6c757d;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
  margin: 0 4px 4px 0;
}

/* 📊 紧凑统计数据 */
.team-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1px;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  border-radius: 12px;
  overflow: hidden;
  padding: 2px;
  margin-bottom: 14px;
}

.stat {
  background: white;
  padding: 12px 8px;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat:hover::before {
  opacity: 1;
}

.stat:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(102, 126, 234, 0.12);
}

.stat-number {
  font-size: 16px;
  font-weight: 800;
  color: #1a202c;
  margin-bottom: 2px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  z-index: 1;
}

.stat-label {
  font-size: 10px;
  color: #64748b;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  position: relative;
  z-index: 1;
}

/* 🎨 紧凑卡片底部 */
.card-footer {
  padding: 16px 20px 18px;
  border-top: 1px solid rgba(241, 245, 249, 0.8);
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.5) 0%,
    rgba(255, 255, 255, 1) 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.members-preview {
  flex: 1;
  min-width: 0;
}

.member-avatars {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.member-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 3px solid white;
  overflow: hidden;
  position: relative;
  margin-left: -8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.member-avatar:first-child {
  margin-left: 0;
}

.member-avatar:hover {
  transform: scale(1.15) translateY(-2px);
  z-index: 5;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.admin-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #f59e0b, #f97316);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  color: white;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.4);
  animation: crown-shine 3s ease-in-out infinite;
}

@keyframes crown-shine {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.more-count {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
  border: 3px solid white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 700;
  color: #64748b;
  margin-left: -8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.more-count:hover {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  transform: scale(1.1);
}

.member-info {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #64748b;
}

.member-count {
  font-weight: 600;
}

.create-time {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 🚀 精美操作按钮 */
.action-btn {
  padding: 12px 20px;
  border-radius: 14px;
  border: none;
  font-size: 13px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
  min-width: 110px;
  white-space: nowrap;
  text-overflow: ellipsis;
  letter-spacing: 0.3px;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%);
  transition: left 0.6s ease;
}

.action-btn:hover::before {
  left: 100%;
}

.action-btn.join {
  background: linear-gradient(135deg,
    #667eea 0%,
    #764ba2 50%,
    #f093fb 100%);
  color: white;
  box-shadow:
    0 8px 24px rgba(102, 126, 234, 0.3),
    0 4px 12px rgba(102, 126, 234, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.action-btn.join:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow:
    0 16px 40px rgba(102, 126, 234, 0.4),
    0 8px 20px rgba(102, 126, 234, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.action-btn.entered {
  background: linear-gradient(135deg,
    #10b981 0%,
    #059669 50%,
    #047857 100%);
  color: white;
  box-shadow:
    0 8px 24px rgba(16, 185, 129, 0.3),
    0 4px 12px rgba(16, 185, 129, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.action-btn.entered:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow:
    0 16px 40px rgba(16, 185, 129, 0.4),
    0 8px 20px rgba(16, 185, 129, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.action-btn i {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.action-btn:hover i {
  transform: scale(1.1);
}

/* 卡片装饰元素 */
.card-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  pointer-events: none;
}

.featured-ribbon {
  position: absolute;
  top: 16px;
  right: -8px;
  background: linear-gradient(135deg, #f59e0b, #f97316);
  color: white;
  padding: 6px 16px 6px 12px;
  font-size: 11px;
  font-weight: 600;
  border-radius: 12px 0 0 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
  transform: translateX(8px);
}

.featured-ribbon::after {
  content: '';
  position: absolute;
  right: -8px;
  top: 0;
  bottom: 0;
  width: 8px;
  background: linear-gradient(135deg, #ea580c, #dc2626);
  clip-path: polygon(0 0, 100% 50%, 0 100%);
}

.hot-indicator {
  position: absolute;
  top: 16px;
  left: 16px;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  animation: pulse-fire 2s ease-in-out infinite;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

@keyframes pulse-fire {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* 团队头部区域 */
.team-header {
  position: relative;
  height: 140px;
  cursor: pointer;
  overflow: hidden;
  border-radius: 24px 24px 0 0;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.9;
}

.pattern-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  background-size: 100px 100px, 80px 80px, 120px 120px;
  animation: float-pattern 20s ease-in-out infinite;
}

@keyframes float-pattern {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(10px, -10px) rotate(1deg); }
  66% { transform: translate(-5px, 5px) rotate(-1deg); }
}

.header-content {
  position: relative;
  z-index: 2;
  height: 100%;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

/* 团队头像容器 */
.team-avatar-container {
  position: relative;
}

.team-avatar {
  width: 64px;
  height: 64px;
  border-radius: 20px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border: 3px solid rgba(255, 255, 255, 0.2);
  position: relative;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.team-avatar:hover {
  transform: scale(1.05);
  border-color: rgba(255, 255, 255, 0.4);
}

.team-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.9);
  font-size: 28px;
}

.avatar-ring {
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border: 2px solid transparent;
  border-radius: 23px;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
  background-clip: padding-box;
  animation: rotate-ring 10s linear infinite;
}

@keyframes rotate-ring {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.activity-pulse {
  position: absolute;
  bottom: -4px;
  right: -4px;
  width: 20px;
  height: 20px;
  background: #10b981;
  border: 3px solid white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4);
  animation: pulse-activity 2s ease-in-out infinite;
}

@keyframes pulse-activity {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.6);
  }
}

/* 团队状态区域 */
.team-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 16px;
}

.status-badges {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-end;
}

.badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.badge.privacy.public {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(37, 99, 235, 0.2));
  color: #1e40af;
  border-color: rgba(59, 130, 246, 0.4);
}

.badge.privacy.private {
  background: linear-gradient(135deg, rgba(107, 114, 128, 0.3), rgba(75, 85, 99, 0.2));
  color: #374151;
  border-color: rgba(107, 114, 128, 0.4);
}

.badge.member {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.3), rgba(5, 150, 105, 0.2));
  color: #065f46;
  border-color: rgba(16, 185, 129, 0.4);
  animation: member-glow 3s ease-in-out infinite alternate;
}

@keyframes member-glow {
  0% { box-shadow: 0 0 0 rgba(16, 185, 129, 0); }
  100% { box-shadow: 0 0 20px rgba(16, 185, 129, 0.3); }
}

/* 收藏按钮 */
.favorite-btn {
  width: 44px;
  height: 44px;
  border-radius: 16px;
  border: none;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  position: relative;
  overflow: hidden;
}

.favorite-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.favorite-btn:hover::before {
  opacity: 1;
}

.favorite-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  color: white;
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.favorite-btn.active {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.3), rgba(220, 38, 38, 0.2));
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.4);
  animation: heart-beat 1.5s ease-in-out infinite;
}

@keyframes heart-beat {
  0%, 100% { transform: scale(1); }
  25% { transform: scale(1.1); }
  50% { transform: scale(1.05); }
  75% { transform: scale(1.1); }
}

.favorite-btn.active:hover {
  transform: scale(1.15) rotate(-5deg);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

/* 团队主体信息区域 */
.team-body {
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
}

.team-body:hover {
  background: linear-gradient(145deg, rgba(248, 250, 252, 0.5), rgba(241, 245, 249, 0.3));
}

.team-title-section {
  margin-bottom: 16px;
}

.team-name {
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  background: linear-gradient(135deg, #1e293b, #475569);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.team-meta-info {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 13px;
  color: #64748b;
  font-weight: 500;
}

.team-meta-info span {
  display: flex;
  align-items: center;
  gap: 6px;
}

.team-meta-info i {
  font-size: 11px;
  opacity: 0.8;
}

.team-description {
  font-size: 14px;
  color: #64748b;
  line-height: 1.6;
  margin: 0 0 20px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-align: justify;
}

/* 团队标签云 */
.team-tags-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 4px;
}

.tag-bubble {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  color: #475569;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s ease;
  animation: tag-float-in 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(10px);
}

@keyframes tag-float-in {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tag-bubble:hover {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.tag-more-indicator {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
  color: #64748b;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid rgba(203, 213, 225, 0.8);
  animation: tag-float-in 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(10px);
}

.team-description {
  font-size: 14px;
  color: #64748b;
  line-height: 1.6;
  margin: 0 0 20px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-align: justify;
}

/* 团队标签云 */
.team-tags-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 4px;
}

.tag-bubble {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  color: #475569;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s ease;
  animation: tag-float-in 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(10px);
}

@keyframes tag-float-in {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tag-bubble:hover {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.tag-more-indicator {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
  color: #64748b;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid rgba(203, 213, 225, 0.8);
  animation: tag-float-in 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(10px);
}

/* 团队数据仪表板 */
.team-dashboard {
  padding: 0 24px 20px;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  background: linear-gradient(145deg, #f8fafc, #f1f5f9);
  border-radius: 16px;
  padding: 16px;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.metric-card {
  background: white;
  border-radius: 12px;
  padding: 16px 12px;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(226, 232, 240, 0.4);
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(16, 185, 129, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.metric-card:hover::before {
  opacity: 1;
}

.metric-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

.metric-icon {
  width: 32px;
  height: 32px;
  margin: 0 auto 8px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  position: relative;
  z-index: 1;
}

.metric-info {
  position: relative;
  z-index: 1;
}

.metric-value {
  font-size: 18px;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 4px;
  background: linear-gradient(135deg, #1e293b, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.metric-label {
  font-size: 11px;
  color: #64748b;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 团队成员展示区域 */
.team-members-showcase {
  padding: 20px 24px;
  border-top: 1px solid rgba(241, 245, 249, 0.8);
  background: linear-gradient(145deg, rgba(248, 250, 252, 0.3), rgba(255, 255, 255, 0.1));
}

.members-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.members-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  font-weight: 600;
  color: #475569;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.members-label i {
  font-size: 12px;
  color: #3b82f6;
}

.members-gallery {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.member-card {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: member-slide-in 0.6s ease-out forwards;
  opacity: 0;
  transform: translateX(-20px);
}

@keyframes member-slide-in {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.member-card:hover {
  transform: translateY(-4px) scale(1.1);
  z-index: 5;
}

.member-avatar-wrapper {
  position: relative;
  width: 40px;
  height: 40px;
}

.member-avatar {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  object-fit: cover;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.member-card:hover .member-avatar {
  border-color: #3b82f6;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.role-badge {
  position: absolute;
  bottom: -4px;
  right: -4px;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  color: white;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.role-badge.admin {
  background: linear-gradient(135deg, #f59e0b, #f97316);
  animation: crown-glow 2s ease-in-out infinite alternate;
}

@keyframes crown-glow {
  0% { box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); }
  100% { box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2), 0 0 12px rgba(245, 158, 11, 0.4); }
}

.role-badge.member {
  background: linear-gradient(135deg, #6b7280, #4b5563);
}

.more-members-card {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
  border: 2px solid white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #64748b;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.more-members-card:hover {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.more-count {
  font-size: 12px;
  line-height: 1;
}

.more-label {
  font-size: 8px;
  line-height: 1;
  opacity: 0.8;
}

/* 团队操作区域 */
.team-footer {
  padding: 24px;
  border-top: 1px solid rgba(241, 245, 249, 0.8);
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.4));
}

.action-section {
  display: flex;
  justify-content: center;
}

.primary-action {
  width: 100%;
  padding: 16px 20px;
  border-radius: 16px;
  border: none;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  overflow: hidden;
  font-family: inherit;
}

.primary-action::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.primary-action:hover::before {
  left: 100%;
}

.primary-action.join {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8, #1e40af);
  color: white;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.primary-action.join:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af, #1e3a8a);
  transform: translateY(-3px);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.4);
}

.primary-action.entered {
  background: linear-gradient(135deg, #10b981, #059669, #047857);
  color: white;
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
}

.primary-action.entered:hover {
  background: linear-gradient(135deg, #059669, #047857, #065f46);
  transform: translateY(-3px);
  box-shadow: 0 8px 32px rgba(16, 185, 129, 0.4);
}

.action-icon {
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.primary-action:hover .action-icon {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1) rotate(5deg);
}

.action-text {
  flex: 1;
  text-align: left;
}

.action-title {
  display: block;
  font-size: 16px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 2px;
}

.action-subtitle {
  display: block;
  font-size: 12px;
  opacity: 0.9;
  font-weight: 500;
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 120px;
  z-index: 1;
}

.gradient-overlay {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.team-header {
  position: relative;
  z-index: 2;
  padding: 20px 20px 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.team-avatar {
  position: relative;
  width: 64px;
  height: 64px;
  border-radius: 12px;
  overflow: hidden;
  border: 3px solid white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.team-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.activity-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  background: #4CAF50;
  border-radius: 50%;
  border: 2px solid white;
}

.team-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.action-btn.starred {
  background: #ffc107;
  border-color: #ffc107;
  color: white;
}

.team-content {
  position: relative;
  z-index: 2;
  padding: 20px;
  background: white;
  margin-top: 60px;
}

.team-badges {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge.public {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.2);
}

.badge.private {
  background: rgba(158, 158, 158, 0.1);
  color: #9E9E9E;
  border: 1px solid rgba(158, 158, 158, 0.2);
}

.badge.member {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.badge.recommended {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.2);
}

.team-name {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #2c3e50;
  line-height: 1.3;
}

.team-description {
  color: #6c757d;
  line-height: 1.5;
  margin: 0 0 16px 0;
  font-size: 14px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.team-tags {
  display: flex;
  gap: 6px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.tag {
  padding: 4px 8px;
  background: #f8f9fa;
  color: #495057;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid #e9ecef;
}

.tag.more {
  background: #e9ecef;
  color: #6c757d;
}

.team-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6c757d;
  font-size: 12px;
  font-weight: 500;
}

.stat i {
  font-size: 14px;
}

.team-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #f1f3f4;
}

.members-avatars {
  display: flex;
  align-items: center;
  gap: -8px;
}

.member-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: 2px solid white;
  object-fit: cover;
  margin-left: -8px;
}

.member-avatar:first-child {
  margin-left: 0;
}

.more-members {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #e9ecef;
  color: #6c757d;
  font-size: 10px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  margin-left: -8px;
}

.last-activity {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6c757d;
  font-size: 11px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 20px;
  color: #6c757d;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 24px;
  opacity: 0.5;
  color: #dee2e6;
}

.empty-state h3 {
  font-size: 24px;
  margin: 0 0 12px 0;
  color: #495057;
  font-weight: 600;
}

.empty-state p {
  margin: 0 0 24px 0;
  font-size: 16px;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.empty-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .hero-actions {
    align-self: center;
  }

  .teams-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 60px 0 40px;
  }

  .hero-title {
    font-size: 36px;
  }

  .hero-subtitle {
    font-size: 18px;
  }

  .hero-stats {
    gap: 20px;
    justify-content: center;
  }

  .stat-number {
    font-size: 24px;
  }

  .nav-cards {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .filter-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .filter-tabs {
    justify-content: center;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .filter-tabs::-webkit-scrollbar {
    display: none;
  }

  .teams-grid {
    grid-template-columns: 1fr;
  }

  .team-card {
    margin: 0 auto;
    max-width: 400px;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 40px 0 30px;
  }

  .hero-title {
    font-size: 28px;
  }

  .hero-subtitle {
    font-size: 16px;
  }

  .hero-stats {
    flex-direction: column;
    gap: 16px;
  }

  .nav-cards {
    grid-template-columns: 1fr;
  }

  .search-filter-section {
    padding: 16px;
  }

  .search-box input {
    padding: 12px 12px 12px 40px;
    font-size: 14px;
  }

  .filter-tabs {
    gap: 4px;
  }

  .filter-tab {
    padding: 8px 12px;
    font-size: 12px;
  }

  .team-content {
    padding: 16px;
  }

  .team-name {
    font-size: 18px;
  }
}

/* 加载更多样式 */
.load-more-container {
  display: flex;
  justify-content: center;
  margin: 32px 0;
}

.load-more-btn {
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.load-more-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin: 32px 0;
  color: #6b7280;
  font-size: 14px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.no-more-data {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 32px 0;
  color: #6b7280;
  font-size: 14px;
}

.no-more-data i {
  color: #10b981;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 加入团队按钮样式 */
.join-actions {
  display: flex;
  gap: 8px;
}

.btn-join {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-join:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.btn-joined {
  background: #10b981;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: not-allowed;
  display: flex;
  align-items: center;
  gap: 6px;
  opacity: 0.8;
}

/* 现代化加入团队对话框 */
.join-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeIn 0.3s ease;
}

.join-modal {
  background: white;
  border-radius: 24px;
  width: 100%;
  max-width: 560px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
  animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 对话框头部 */
.join-modal-header {
  padding: 32px 32px 24px;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 20px;
}

.header-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex: 1;
}

.modal-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.header-text h2 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
}

.header-text p {
  margin: 0;
  font-size: 14px;
  color: #64748b;
  line-height: 1.4;
}

.close-btn {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  border: none;
  background: #f8fafc;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.close-btn:hover:not(:disabled) {
  background: #e2e8f0;
  color: #475569;
  transform: scale(1.05);
}

.close-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 团队预览卡片 */
.team-preview-card {
  margin: 0 32px 24px;
  background: #f8fafc;
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.team-cover-mini {
  position: relative;
  height: 80px;
}

.team-cover-mini .cover-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.9;
}

.team-avatar-mini {
  position: absolute;
  bottom: -20px;
  left: 20px;
  width: 40px;
  height: 40px;
  border-radius: 12px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 3px solid white;
  z-index: 2;
}

.team-avatar-mini img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.team-avatar-mini .avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
}

.team-info-mini {
  padding: 28px 20px 20px;
}

.team-info-mini h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.team-info-mini p {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.team-stats-mini {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.team-stats-mini .stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #64748b;
}

.team-stats-mini .stat i {
  font-size: 10px;
}

.team-stats-mini .badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 500;
}

.team-stats-mini .badge.public {
  background: rgba(59, 130, 246, 0.1);
  color: #1e40af;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.team-stats-mini .badge.private {
  background: rgba(107, 114, 128, 0.1);
  color: #374151;
  border: 1px solid rgba(107, 114, 128, 0.2);
}

/* 申请表单 */
.join-form {
  padding: 0 32px 24px;
}

.form-section {
  margin-bottom: 24px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.form-label i {
  font-size: 12px;
  color: #3b82f6;
}

.required {
  color: #ef4444;
  font-weight: 700;
}

.textarea-wrapper {
  position: relative;
}

.join-textarea {
  width: 100%;
  min-height: 120px;
  padding: 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.6;
  resize: vertical;
  transition: all 0.2s ease;
  font-family: inherit;
}

.join-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.join-textarea:disabled {
  background: #f8fafc;
  color: #94a3b8;
  cursor: not-allowed;
}

.textarea-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  gap: 12px;
}

.char-count {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.char-count.warning {
  color: #f59e0b;
}

.form-tips {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #64748b;
}

.form-tips i {
  color: #f59e0b;
  font-size: 10px;
}

.team-info {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.team-info .team-avatar {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.team-info .team-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.team-info .avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 20px;
}

.team-details h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.team-details p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.required {
  color: #ef4444;
}

.form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.2s ease;
}

.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 快速模板 */
.quick-templates {
  margin-top: 16px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.templates-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 13px;
  font-weight: 600;
  color: #475569;
}

.templates-label i {
  color: #3b82f6;
  font-size: 11px;
}

.template-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.template-btn {
  padding: 8px 12px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.template-btn:hover {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
  transform: translateY(-1px);
}

/* 对话框底部操作按钮 */
.join-modal-footer {
  padding: 24px 32px 32px;
  border-top: 1px solid #f1f5f9;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn-secondary {
  padding: 12px 20px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #475569;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-secondary:hover:not(:disabled) {
  background: #e2e8f0;
  border-color: #cbd5e1;
  transform: translateY(-1px);
}

.btn-secondary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  padding: 12px 24px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-primary.loading {
  pointer-events: none;
}

.btn-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-primary .fa-spinner {
  animation: spin 1s linear infinite;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .team-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .stat {
    padding: 12px 8px;
  }

  .stat-number {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .team-card {
    border-radius: 16px;
  }

  .card-header {
    padding: 20px 20px 0;
  }

  .team-avatar {
    width: 56px;
    height: 56px;
    border-radius: 16px;
  }

  .card-body {
    padding: 20px;
  }

  .team-name {
    font-size: 18px;
  }

  .team-stats {
    grid-template-columns: repeat(4, 1fr);
    margin-bottom: 16px;
  }

  .stat {
    padding: 12px 8px;
  }

  .stat-number {
    font-size: 16px;
  }

  .card-footer {
    padding: 16px 20px 20px;
  }

  .member-avatar {
    width: 28px;
    height: 28px;
  }

  .more-count {
    width: 28px;
    height: 28px;
    font-size: 10px;
  }

  .action-btn {
    padding: 10px 16px;
    min-width: 100px;
  }
}

@media (max-width: 480px) {
  .team-card {
    border-radius: 12px;
  }

  .card-header {
    padding: 16px 16px 0;
  }

  .team-avatar {
    width: 48px;
    height: 48px;
    border-radius: 14px;
  }

  .favorite-btn {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  .card-body {
    padding: 16px;
  }

  .team-name {
    font-size: 16px;
  }

  .team-description {
    font-size: 13px;
  }

  .team-stats {
    grid-template-columns: repeat(2, 1fr);
    margin-bottom: 16px;
  }

  .stat {
    padding: 10px 6px;
  }

  .stat-number {
    font-size: 14px;
  }

  .stat-label {
    font-size: 10px;
  }

  .card-footer {
    padding: 14px 16px 16px;
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .member-avatar {
    width: 24px;
    height: 24px;
  }

  .more-count {
    width: 24px;
    height: 24px;
    font-size: 9px;
  }

  .member-info {
    font-size: 11px;
  }

  .action-btn {
    padding: 12px 16px;
    width: 100%;
    min-width: auto;
  }
}

/* 加入团队对话框响应式设计 */
@media (max-width: 640px) {
  .join-modal {
    margin: 20px;
    max-width: calc(100vw - 40px);
  }

  .join-modal-header {
    padding: 24px 20px 20px;
  }

  .header-content {
    gap: 12px;
  }

  .modal-icon {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .header-text h2 {
    font-size: 20px;
  }

  .team-preview-card {
    margin: 0 20px 20px;
  }

  .join-form {
    padding: 0 20px 20px;
  }

  .join-modal-footer {
    padding: 20px;
    flex-direction: column;
  }

  .btn-secondary,
  .btn-primary {
    width: 100%;
    justify-content: center;
  }
}

/* 性能优化 - 减少不必要的动画 */
@media (prefers-reduced-motion: reduce) {
  .team-card,
  .metric-card,
  .member-card,
  .primary-action,
  .tag-bubble {
    animation: none;
    transition: none;
  }

  .team-card:hover {
    transform: none;
  }

  .primary-action:hover {
    transform: translateY(-1px);
  }
}
</style>
