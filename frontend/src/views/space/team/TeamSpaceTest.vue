<template>
  <Layout>
    <div class="team-space-revolution">
      <!-- 🎯 Hero Section - 全新革命性设计 -->
      <div class="hero-section">
        <div class="hero-background">
          <div class="gradient-orb orb-1"></div>
          <div class="gradient-orb orb-2"></div>
          <div class="gradient-orb orb-3"></div>
          <div class="floating-particles">
            <div class="particle" v-for="i in 20" :key="i"></div>
          </div>
        </div>

        <div class="container">
          <div class="hero-content">
            <div class="hero-text">
              <h1 class="hero-title">
                <span class="title-gradient">团队空间</span>
                <div class="title-decoration">
                  <i class="fas fa-users"></i>
                  <div class="decoration-glow"></div>
                </div>
              </h1>
              <p class="hero-subtitle">
                发现志同道合的团队，开启协作新篇章
                <br>
                <span class="highlight">{{ allTeams.length }}+</span> 个活跃团队等你加入
              </p>
            </div>

            <div class="hero-actions">
              <button class="cta-button primary" @click="showCreateModal = true">
                <i class="fas fa-plus"></i>
                <span>创建团队</span>
                <div class="button-glow"></div>
              </button>
              <button class="cta-button secondary" @click="scrollToExplore">
                <i class="fas fa-compass"></i>
                <span>探索团队</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="container">
        <!-- 🔍 搜索和筛选区域 - 全新设计 -->
        <div class="search-filter-section" ref="exploreSection">

          <!-- 🔍 优化的智能搜索栏 -->
          <div class="search-container">
            <div class="search-box">
              <div class="search-input-wrapper">
                <div class="search-icon-wrapper">
                  <i class="fas fa-search search-icon"></i>
                </div>
                <input
                  type="text"
                  placeholder="搜索团队名称、标签或描述..."
                  v-model="searchQuery"
                  @input="handleSearch"
                  @focus="showSearchSuggestions = true"
                  @blur="hideSearchSuggestions"
                  class="search-input"
                />
                <div class="search-actions">
                  <button v-if="searchQuery" class="clear-search" @click="clearSearch" title="清除搜索">
                    <i class="fas fa-times"></i>
                  </button>
                  <div class="search-divider" v-if="searchQuery"></div>
                  <button class="search-filter-btn" title="高级筛选">
                    <i class="fas fa-sliders-h"></i>
                  </button>
                </div>
              </div>

              <!-- 搜索状态指示 -->
              <div v-if="searchQuery" class="search-status">
                <span class="search-results-count">{{ filteredTeams.length }} 个结果</span>
                <div class="search-loading" v-if="loading">
                  <i class="fas fa-spinner fa-spin"></i>
                </div>
              </div>

              <!-- 优化的搜索建议 -->
              <div v-if="showSearchSuggestions && searchSuggestions.length" class="search-suggestions-enhanced">
                <div class="suggestions-header">
                  <i class="fas fa-magic"></i>
                  <span>智能建议</span>
                  <span class="suggestions-count">{{ searchSuggestions.length }}</span>
                </div>
                <div class="suggestions-list">
                  <div
                    v-for="suggestion in searchSuggestions"
                    :key="suggestion.id"
                    class="suggestion-item-enhanced"
                    @mousedown="applySuggestion(suggestion)"
                  >
                    <div class="suggestion-icon">
                      <i :class="suggestion.icon"></i>
                    </div>
                    <div class="suggestion-content">
                      <span class="suggestion-text">{{ suggestion.text }}</span>
                      <span class="suggestion-type">{{ suggestion.type }}</span>
                    </div>
                    <div class="suggestion-arrow">
                      <i class="fas fa-arrow-right"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Tab Navigation -->
          <div class="tab-navigation">
            <div class="tab-list">
              <button
                v-for="tab in filterTabs"
                :key="tab.key"
                class="tab-item"
                :class="{ active: activeTab === tab.key }"
                @click="setActiveTab(tab.key)"
              >
                <i :class="tab.icon"></i>
                <span>{{ tab.label }}</span>
                <span v-if="tab.count !== undefined" class="tab-badge">{{ tab.count }}</span>
              </button>
            </div>
          </div>

          <!-- Advanced Filters -->
          <div class="advanced-filters">
            <div class="filter-group">
              <div class="filter-section">
                <h4 class="filter-title">
                  <i class="fas fa-tags"></i>
                  热门标签
                </h4>
                <div class="tag-cloud">
                  <button
                    v-for="tag in popularTags"
                    :key="tag.name"
                    class="tag-chip"
                    :class="{ active: selectedTags.includes(tag.name) }"
                    @click="toggleTag(tag.name)"
                  >
                    {{ tag.name }}
                    <span class="tag-count">{{ tag.count }}</span>
                  </button>
                </div>
              </div>

              <div class="filter-controls">
                <div class="sort-control">
                  <label class="control-label">排序方式</label>
                  <select v-model="sortBy" @change="handleSortChange" class="sort-select">
                    <option value="members">成员数量</option>
                    <option value="articles">文章数量</option>
                    <option value="likes">点赞收藏</option>
                    <option value="activity">最近活跃</option>
                    <option value="created">创建时间</option>
                  </select>
                </div>

                <div class="view-control">
                  <label class="control-label">视图模式</label>
                  <div class="view-toggle">
                    <button
                      class="view-btn"
                      :class="{ active: viewMode === 'grid' }"
                      @click="setViewMode('grid')"
                      title="网格视图"
                    >
                      <i class="fas fa-th"></i>
                    </button>
                    <button
                      class="view-btn"
                      :class="{ active: viewMode === 'list' }"
                      @click="setViewMode('list')"
                      title="列表视图"
                    >
                      <i class="fas fa-list"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Results Summary -->
          <div class="results-summary">
            <div class="summary-info">
              <span class="results-count">
                找到 <strong>{{ filteredTeams.length }}</strong> 个团队
              </span>
              <span v-if="hasActiveFilters" class="filter-indicator">
                <i class="fas fa-filter"></i>
                已应用筛选条件
              </span>
            </div>
            <div class="summary-actions">
              <button v-if="hasActiveFilters" class="clear-all-filters" @click="clearAllFilters">
                <i class="fas fa-times"></i>
                清除筛选
              </button>
            </div>
          </div>
        </div>

        <!-- 🎯 Teams Display Section -->
        <div class="teams-display-section">
          <!-- Loading State -->
          <div v-if="loading" class="loading-container">
            <div class="loading-grid" :class="viewMode">
              <div v-for="i in 12" :key="i" class="loading-card">
                <div class="loading-shimmer"></div>
              </div>
            </div>
          </div>

          <!-- Teams Grid/List -->
          <div v-else-if="filteredTeams.length" class="teams-container" :class="viewMode">
            <div
              v-for="team in paginatedTeams"
              :key="team.id"
              class="team-card"
              :class="{
                'list-view': viewMode === 'list',
                'is-member': team.isMember,
                'is-starred': team.isStarred,
                'is-public': team.isPublic
              }"
              @click="handleTeamClick(team)"
            >
              <!-- 🎯 右上角操作按钮 -->
              <div class="card-top-actions">
                <button
                  class="top-action-btn preview-btn"
                  @click.stop="handlePreviewTeam(team)"
                  title="快速预览"
                >
                  <i class="fas fa-eye"></i>
                </button>
                <button
                  class="top-action-btn star-btn"
                  :class="{ active: team.isStarred }"
                  @click.stop="handleStarTeam(team)"
                  :title="team.isStarred ? '取消收藏' : '收藏团队'"
                >
                  <i class="fas fa-star"></i>
                </button>
              </div>

              <!-- 卡片头部 -->
              <div class="card-header">
                <div class="team-avatar">
                  <img v-if="team.avatar" :src="team.avatar" :alt="team.name" />
                  <div v-else class="avatar-placeholder">
                    <i class="fas fa-users"></i>
                  </div>

                  <!-- 状态指示器 -->
                  <div v-if="team.isMember" class="status-badge member">
                    <i class="fas fa-check"></i>
                  </div>
                  <div v-else-if="team.isPublic" class="status-badge public">
                    <i class="fas fa-globe"></i>
                  </div>
                  <div v-else class="status-badge private">
                    <i class="fas fa-lock"></i>
                  </div>
                </div>

                <div class="team-info">
                  <h3 class="team-name">{{ team.name }}</h3>
                  <p class="team-description">{{ team.description || '暂无描述' }}</p>
                </div>
              </div>

              <!-- 卡片主体 -->
              <div class="card-body">
                <!-- 团队标签 -->
                <div v-if="team.tags && team.tags.length" class="team-tags">
                  <span
                    v-for="tag in team.tags.slice(0, 3)"
                    :key="tag"
                    class="tag"
                  >
                    {{ tag }}
                  </span>
                  <span v-if="team.tags.length > 3" class="tag more">
                    +{{ team.tags.length - 3 }}
                  </span>
                </div>

                <!-- 团队统计 -->
                <div class="team-stats">
                  <div class="stat-item">
                    <i class="fas fa-users"></i>
                    <span class="stat-value">{{ team.membersCount || 0 }}</span>
                    <span class="stat-label">成员</span>
                  </div>
                  <div class="stat-item">
                    <i class="fas fa-file-alt"></i>
                    <span class="stat-value">{{ team.articlesCount || 0 }}</span>
                    <span class="stat-label">文章</span>
                  </div>
                  <div class="stat-item">
                    <i class="fas fa-heart"></i>
                    <span class="stat-value">{{ formatNumber(team.likesCount || 0) }}</span>
                    <span class="stat-label">点赞</span>
                  </div>
                  <div class="stat-item">
                    <i class="fas fa-eye"></i>
                    <span class="stat-value">{{ formatNumber(team.viewsCount || 0) }}</span>
                    <span class="stat-label">浏览</span>
                  </div>
                </div>
              </div>

              <!-- 卡片底部 -->
              <div class="card-footer">
                <div class="team-meta">
                  <span class="created-time">
                    <i class="fas fa-calendar"></i>
                    {{ formatDate(team.createdAt) }}
                  </span>
                </div>

                <div class="primary-actions">
                  <button
                    v-if="team.isMember"
                    class="primary-btn enter"
                    @click.stop="handleEnterTeam(team)"
                  >
                    <i class="fas fa-arrow-right"></i>
                    进入团队
                  </button>
                  <button
                    v-else
                    class="primary-btn join"
                    @click.stop="handleJoinTeam(team)"
                  >
                    <i class="fas fa-plus"></i>
                    {{ team.isPublic ? '立即加入' : '申请加入' }}
                  </button>
                </div>
              </div>

              <!-- 优化的悬停效果 -->
              <div class="hover-overlay">
                <div class="hover-gradient"></div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else class="empty-state">
            <div class="empty-illustration">
              <div class="empty-icon">
                <i class="fas fa-search"></i>
              </div>
              <div class="empty-particles">
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
              </div>
            </div>
            <h3 class="empty-title">{{ getEmptyTitle() }}</h3>
            <p class="empty-description">{{ getEmptyDescription() }}</p>
            <div class="empty-actions">
              <button v-if="hasActiveFilters" class="btn-outline" @click="clearAllFilters">
                <i class="fas fa-refresh"></i>
                重置筛选
              </button>
              <button class="btn-primary" @click="showCreateModal = true">
                <i class="fas fa-plus"></i>
                创建团队
              </button>
            </div>
          </div>

          <!-- Pagination -->
          <div v-if="filteredTeams.length > pageSize" class="pagination-container">
            <div class="pagination">
              <button
                class="page-btn prev"
                :disabled="currentPage === 1"
                @click="setPage(currentPage - 1)"
              >
                <i class="fas fa-chevron-left"></i>
                上一页
              </button>

              <div class="page-numbers">
                <button
                  v-for="page in visiblePages"
                  :key="page"
                  class="page-number"
                  :class="{ active: page === currentPage, ellipsis: page === '...' }"
                  @click="page !== '...' && setPage(page)"
                  :disabled="page === '...'"
                >
                  {{ page }}
                </button>
              </div>

              <button
                class="page-btn next"
                :disabled="currentPage === totalPages"
                @click="setPage(currentPage + 1)"
              >
                下一页
                <i class="fas fa-chevron-right"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- 🎨 Modals -->
        <CreateTeamModal
          :visible="showCreateModal"
          @close="showCreateModal = false"
          @created="handleTeamCreated"
        />

        <TeamPreviewModal
          :visible="!!previewTeam"
          :team="previewTeam"
          @close="closePreview"
          @join="handleJoinTeam"
          @star="handleStarTeam"
        />

        <JoinTeamModal
          :visible="!!joinModalTeam"
          :team="joinModalTeam"
          @close="closeJoinModal"
          @joined="handleTeamJoined"
        />
      </div>
    </div>
  </Layout>
</template>

<script>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '@/stores/toast'
import { useUserStore } from '@/stores/user'
import teamService from '@/services/teamService'
import userService from '@/services/userService'

// 导入组件
import Layout from '@/components/Layout.vue'
import CreateTeamModal from './components/CreateTeamModal.vue'
import TeamPreviewModal from './components/TeamPreviewModal.vue'
import JoinTeamModal from './components/JoinTeamModal.vue'

export default {
  name: 'TeamSpaceRevolution',
  components: {
    Layout,
    CreateTeamModal,
    TeamPreviewModal,
    JoinTeamModal
  },
  setup() {
    const router = useRouter()
    const toastStore = useToastStore()
    const userStore = useUserStore()
    const exploreSection = ref(null)

    // ===== 响应式数据 =====
    const loading = ref(false)
    const error = ref(null)

    // 团队数据
    const allTeams = ref([])
    const myTeams = ref([])

    // 搜索和筛选
    const searchQuery = ref('')
    const showSearchSuggestions = ref(false)
    const searchSuggestions = ref([])
    const activeTab = ref('all')
    const selectedTags = ref([])
    const sortBy = ref('members')
    const viewMode = ref('grid')
    const showAllTags = ref(false)

    // 分页
    const currentPage = ref(1)
    const pageSize = ref(12)

    // 模态框状态
    const showCreateModal = ref(false)
    const previewTeam = ref(null)
    const joinModalTeam = ref(null)

    // ===== 计算属性 =====

    // Tab配置
    const filterTabs = computed(() => [
      { key: 'all', label: '全部团队', icon: 'fas fa-globe', count: allTeams.value.length },
      { key: 'my', label: '我的团队', icon: 'fas fa-user-friends', count: myTeams.value.length },
      { key: 'joined', label: '已加入', icon: 'fas fa-check-circle', count: getJoinedTeamsCount() },
      { key: 'created', label: '我创建的', icon: 'fas fa-crown', count: getCreatedTeamsCount() },
      { key: 'public', label: '公开团队', icon: 'fas fa-unlock', count: getPublicTeamsCount() },
      { key: 'starred', label: '收藏团队', icon: 'fas fa-star', count: getStarredTeamsCount() }
    ])

    // 热门标签
    const popularTags = computed(() => {
      const tagCounts = {}
      allTeams.value.forEach(team => {
        team.tags?.forEach(tag => {
          tagCounts[tag] = (tagCounts[tag] || 0) + 1
        })
      })

      return Object.entries(tagCounts)
        .map(([name, count]) => ({ name, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, showAllTags.value ? undefined : 8)
    })

    const allTags = computed(() => {
      const tagCounts = {}
      allTeams.value.forEach(team => {
        team.tags?.forEach(tag => {
          tagCounts[tag] = (tagCounts[tag] || 0) + 1
        })
      })

      return Object.entries(tagCounts)
        .map(([name, count]) => ({ name, count }))
        .sort((a, b) => b.count - a.count)
    })

    // 筛选后的团队
    const filteredTeams = computed(() => {
      let teams = [...allTeams.value]

      // Tab筛选
      switch (activeTab.value) {
        case 'my':
          teams = myTeams.value
          break
        case 'joined':
          teams = teams.filter(team => team.isMember)
          break
        case 'created':
          teams = teams.filter(team => team.isCreatedByMe)
          break
        case 'public':
          teams = teams.filter(team => team.isPublic)
          break
        case 'starred':
          teams = teams.filter(team => team.isStarred)
          break
      }

      // 搜索筛选
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        teams = teams.filter(team =>
          team.name.toLowerCase().includes(query) ||
          team.description?.toLowerCase().includes(query) ||
          team.tags?.some(tag => tag.toLowerCase().includes(query))
        )
      }

      // 标签筛选
      if (selectedTags.value.length > 0) {
        teams = teams.filter(team =>
          selectedTags.value.some(tag => team.tags?.includes(tag))
        )
      }

      // 排序
      return sortTeams(teams)
    })

    // 分页后的团队
    const paginatedTeams = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return filteredTeams.value.slice(start, end)
    })

    const totalPages = computed(() => {
      return Math.ceil(filteredTeams.value.length / pageSize.value)
    })

    const visiblePages = computed(() => {
      const pages = []
      const total = totalPages.value
      const current = currentPage.value

      if (total <= 7) {
        for (let i = 1; i <= total; i++) {
          pages.push(i)
        }
      } else {
        if (current <= 4) {
          for (let i = 1; i <= 5; i++) pages.push(i)
          pages.push('...')
          pages.push(total)
        } else if (current >= total - 3) {
          pages.push(1)
          pages.push('...')
          for (let i = total - 4; i <= total; i++) pages.push(i)
        } else {
          pages.push(1)
          pages.push('...')
          for (let i = current - 1; i <= current + 1; i++) pages.push(i)
          pages.push('...')
          pages.push(total)
        }
      }

      return pages
    })

    const hasActiveFilters = computed(() => {
      return searchQuery.value || selectedTags.value.length > 0 || activeTab.value !== 'all'
    })

    // ===== 辅助方法 =====
    const getJoinedTeamsCount = () => {
      return allTeams.value.filter(team => team.isMember).length
    }

    const getCreatedTeamsCount = () => {
      return allTeams.value.filter(team => team.isCreatedByMe).length
    }

    const getPublicTeamsCount = () => {
      return allTeams.value.filter(team => team.isPublic).length
    }

    const getStarredTeamsCount = () => {
      return allTeams.value.filter(team => team.isStarred).length
    }

    const sortTeams = (teams) => {
      return [...teams].sort((a, b) => {
        switch (sortBy.value) {
          case 'members':
            return (b.membersCount || 0) - (a.membersCount || 0)
          case 'articles':
            return (b.articlesCount || 0) - (a.articlesCount || 0)
          case 'likes':
            return (b.likesCount || 0) - (a.likesCount || 0)
          case 'activity':
            return new Date(b.lastActivityAt || b.createdAt) - new Date(a.lastActivityAt || a.createdAt)
          case 'created':
            return new Date(b.createdAt) - new Date(a.createdAt)
          default:
            return 0
        }
      })
    }

    const formatNumber = (num) => {
      if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M'
      if (num >= 1000) return (num / 1000).toFixed(1) + 'K'
      return num.toString()
    }

    const formatDate = (dateStr) => {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      const now = new Date()
      const diff = now - date
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (days === 0) return '今天'
      if (days === 1) return '昨天'
      if (days < 7) return `${days}天前`
      if (days < 30) return `${Math.floor(days / 7)}周前`
      if (days < 365) return `${Math.floor(days / 30)}个月前`
      return `${Math.floor(days / 365)}年前`
    }

    // ===== 防抖函数 =====
    function debounce(func, wait) {
      let timeout
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout)
          func(...args)
        }
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
      }
    }

    // ===== 事件处理方法 =====

    // 搜索相关
    const handleSearch = debounce(() => {
      generateSearchSuggestions()
      currentPage.value = 1
    }, 300)

    const clearSearch = () => {
      searchQuery.value = ''
      searchSuggestions.value = []
      showSearchSuggestions.value = false
      currentPage.value = 1
    }

    const hideSearchSuggestions = () => {
      setTimeout(() => {
        showSearchSuggestions.value = false
      }, 200)
    }

    const applySuggestion = (suggestion) => {
      searchQuery.value = suggestion.text
      showSearchSuggestions.value = false
      currentPage.value = 1
    }

    const generateSearchSuggestions = () => {
      if (!searchQuery.value || searchQuery.value.length < 2) {
        searchSuggestions.value = []
        return
      }

      const query = searchQuery.value.toLowerCase()
      const suggestions = []

      // 团队名称建议
      allTeams.value.forEach(team => {
        if (team.name.toLowerCase().includes(query)) {
          suggestions.push({
            id: `team-${team.id}`,
            text: team.name,
            type: '团队',
            icon: 'fas fa-users'
          })
        }
      })

      // 标签建议
      allTags.value.forEach(tag => {
        if (tag.name.toLowerCase().includes(query)) {
          suggestions.push({
            id: `tag-${tag.name}`,
            text: tag.name,
            type: '标签',
            icon: 'fas fa-tag'
          })
        }
      })

      searchSuggestions.value = suggestions.slice(0, 6)
    }

    // Tab和筛选
    const setActiveTab = (tab) => {
      activeTab.value = tab
      currentPage.value = 1
    }

    const toggleTag = (tag) => {
      const index = selectedTags.value.indexOf(tag)
      if (index > -1) {
        selectedTags.value.splice(index, 1)
      } else {
        selectedTags.value.push(tag)
      }
      currentPage.value = 1
    }

    const handleSortChange = () => {
      currentPage.value = 1
    }

    const setViewMode = (mode) => {
      viewMode.value = mode
    }

    const clearAllFilters = () => {
      searchQuery.value = ''
      selectedTags.value = []
      activeTab.value = 'all'
      currentPage.value = 1
    }

    // 分页
    const setPage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page
        // 滚动到顶部
        nextTick(() => {
          exploreSection.value?.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          })
        })
      }
    }

    const handleTeamClick = (team) => {
      router.push(`/space/team/${team.id}`)
    }

    // 团队操作
    const handleJoinTeam = (team) => {
      joinModalTeam.value = team
    }

    const handleStarTeam = async (team) => {
      try {
        if (team.isStarred) {
          await teamService.unstarTeam(team.id)
          team.isStarred = false
          toastStore.success('已取消收藏')
        } else {
          await teamService.starTeam(team.id)
          team.isStarred = true
          toastStore.success('已收藏团队')
        }
      } catch (error) {
        toastStore.error('操作失败: ' + (error.message || '未知错误'))
      }
    }

    const handleEnterTeam = (team) => {
      router.push(`/space/team/${team.id}`)
    }

    const handlePreviewTeam = (team) => {
      previewTeam.value = team
    }

    const closePreview = () => {
      previewTeam.value = null
    }

    const closeJoinModal = () => {
      joinModalTeam.value = null
    }

    const handleTeamCreated = (team) => {
      showCreateModal.value = false
      allTeams.value.unshift(team)
      myTeams.value.unshift(team)
      toastStore.success('团队创建成功！')
    }

    const handleTeamJoined = (team) => {
      closeJoinModal()
      // 更新团队状态
      const teamIndex = allTeams.value.findIndex(t => t.id === team.id)
      if (teamIndex > -1) {
        allTeams.value[teamIndex].isMember = true
        allTeams.value[teamIndex].membersCount += 1
      }
      toastStore.success('成功加入团队！')
    }

    // 空状态文案
    const getEmptyTitle = () => {
      if (searchQuery.value) return '未找到匹配的团队'
      if (selectedTags.value.length) return '该标签下暂无团队'
      if (activeTab.value === 'my') return '您还没有加入任何团队'
      if (activeTab.value === 'starred') return '您还没有收藏任何团队'
      return '暂无团队'
    }

    const getEmptyDescription = () => {
      if (searchQuery.value) return '尝试使用其他关键词搜索，或者创建一个新团队'
      if (selectedTags.value.length) return '尝试选择其他标签，或者创建一个新团队'
      if (activeTab.value === 'my') return '加入感兴趣的团队，开始您的协作之旅'
      if (activeTab.value === 'starred') return '收藏感兴趣的团队，方便快速访问'
      return '创建第一个团队，开启协作之旅'
    }

    const scrollToExplore = () => {
      exploreSection.value?.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
    }

    // ===== 数据加载 =====
    const loadTeamsData = async () => {
      try {
        loading.value = true
        error.value = null

        const currentUserId = userStore.user?.id || 1

        // 并行获取所有团队和用户团队
        const [allTeamsResponse, userTeamsResponse] = await Promise.all([
          teamService.getAllTeams({ pageSize: 100 }),
          userService.getUserTeams(currentUserId)
        ])

        // 处理团队数据
        allTeams.value = processTeamsData(allTeamsResponse?.list || [])
        myTeams.value = userTeamsResponse?.list || []

        console.log('团队数据加载完成:', {
          allTeams: allTeams.value.length,
          myTeams: myTeams.value.length
        })

      } catch (err) {
        console.error('加载团队数据失败:', err)
        error.value = err.message || '加载失败'
        toastStore.error('加载团队数据失败')
      } finally {
        loading.value = false
      }
    }

    const processTeamsData = (teams) => {
      const currentUserId = userStore.user?.id || 1

      return teams.map(team => {
        const achievements = team.achievements || {}
        const members = team.members || []

        // 判断用户关系
        const userMember = members.find(m => m.userId === currentUserId)
        const isMember = !!userMember
        const isCreatedByMe = team.creatorId === currentUserId || team.createdBy === currentUserId.toString()

        return {
          id: team.teamId || team.id,
          name: team.name || '未命名团队',
          description: team.description || '',
          avatar: team.avatarUrl,

          // 团队属性
          isPublic: team.privacy === '1' || team.privacy === 1,
          isActive: team.isActive !== false,
          isMember,
          isCreatedByMe,
          isStarred: false, // TODO: 从用户数据获取

          // 统计数据
          membersCount: team.memberCount || members.length || 0,
          articlesCount: achievements.articlesRecommended || 0,
          likesCount: achievements.totalLikes || 0,
          viewsCount: achievements.totalViews || 0,

          // 标签和时间
          tags: team.tags || [],
          createdAt: team.createdAt,
          lastActivityAt: team.updatedAt || team.createdAt,

          // 成员信息
          members: members,
          creatorId: team.creatorId || team.createdBy
        }
      })
    }

    // 生命周期
    onMounted(() => {
      loadTeamsData()
    })

    // 监听器
    watch(() => userStore.user, (newUser) => {
      if (newUser) {
        loadTeamsData()
      }
    })

    return {
      // 响应式数据
      loading,
      error,
      allTeams,
      myTeams,
      searchQuery,
      showSearchSuggestions,
      searchSuggestions,
      activeTab,
      selectedTags,
      sortBy,
      viewMode,
      showAllTags,
      currentPage,
      pageSize,
      showCreateModal,
      previewTeam,
      joinModalTeam,
      exploreSection,

      // 计算属性
      filterTabs,
      popularTags,
      allTags,
      filteredTeams,
      paginatedTeams,
      totalPages,
      visiblePages,
      hasActiveFilters,

      // 辅助方法
      getJoinedTeamsCount,
      getCreatedTeamsCount,
      getPublicTeamsCount,
      getStarredTeamsCount,
      sortTeams,
      formatNumber,
      formatDate,

      // 事件处理方法
      handleSearch,
      clearSearch,
      hideSearchSuggestions,
      applySuggestion,
      generateSearchSuggestions,
      setActiveTab,
      toggleTag,
      handleSortChange,
      setViewMode,
      clearAllFilters,
      setPage,
      handleTeamClick,
      handleJoinTeam,
      handleStarTeam,
      handleEnterTeam,
      handlePreviewTeam,
      closePreview,
      closeJoinModal,
      handleTeamCreated,
      handleTeamJoined,
      getEmptyTitle,
      getEmptyDescription,
      scrollToExplore,

      // 数据加载
      loadTeamsData,
      processTeamsData
    }
  }
}
</script>

<style scoped>
/* ===== 🎨 革命性团队空间样式设计 ===== */

.team-space-revolution {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow-x: hidden;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

/* ===== 🎯 Hero Section - 全新设计 ===== */
.hero-section {
  position: relative;
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: linear-gradient(135deg,
    rgba(99, 102, 241, 0.1) 0%,
    rgba(139, 92, 246, 0.1) 50%,
    rgba(236, 72, 153, 0.1) 100%
  );
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.6;
  animation: float 6s ease-in-out infinite;
}

.orb-1 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.orb-2 {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, #8b5cf6, #ec4899);
  top: 60%;
  right: 20%;
  animation-delay: 2s;
}

.orb-3 {
  width: 250px;
  height: 250px;
  background: linear-gradient(135deg, #ec4899, #f59e0b);
  bottom: 20%;
  left: 60%;
  animation-delay: 4s;
}

.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(99, 102, 241, 0.6);
  border-radius: 50%;
  animation: particle-float 8s linear infinite;
}

.particle:nth-child(odd) {
  background: rgba(139, 92, 246, 0.6);
  animation-duration: 10s;
}

.particle:nth-child(3n) {
  background: rgba(236, 72, 153, 0.6);
  animation-duration: 12s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes particle-float {
  0% {
    transform: translateY(100vh) translateX(0px) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(100px) rotate(360deg);
    opacity: 0;
  }
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: 60px 20px;
}

.hero-text {
  margin-bottom: 40px;
}

.hero-title {
  font-size: 4rem;
  font-weight: 900;
  margin: 0 0 20px 0;
  position: relative;
  display: inline-block;
}

.title-gradient {
  background: linear-gradient(135deg, #6366f1, #8b5cf6, #ec4899);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.title-decoration {
  position: absolute;
  top: -20px;
  right: -60px;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 50%;
  color: white;
  font-size: 2rem;
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
  animation: pulse-glow 2s ease-in-out infinite;
}

.decoration-glow {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 50%;
  opacity: 0.3;
  animation: pulse-ring 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes pulse-ring {
  0% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.1; }
  100% { transform: scale(1.2); opacity: 0; }
}

.hero-subtitle {
  font-size: 1.25rem;
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

.highlight {
  color: #6366f1;
  font-weight: 700;
  font-size: 1.5em;
}

.hero-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-button {
  position: relative;
  padding: 16px 32px;
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 12px;
  overflow: hidden;
  text-decoration: none;
}

.cta-button.primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

.cta-button.primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(99, 102, 241, 0.4);
}

.cta-button.secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #6366f1;
  border: 2px solid rgba(99, 102, 241, 0.2);
  backdrop-filter: blur(10px);
}

.cta-button.secondary:hover {
  background: rgba(99, 102, 241, 0.1);
  border-color: #6366f1;
  transform: translateY(-2px);
}

.button-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  border-radius: 50px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.cta-button.primary:hover .button-glow {
  opacity: 1;
}

/* ===== 🔍 搜索和筛选区域 - 革命性设计 ===== */
.search-filter-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 40px;
  margin: -60px 20px 40px;
  position: relative;
  z-index: 3;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-container {
  margin-bottom: 30px;
}

.search-box {
  position: relative;
  max-width: 700px;
  margin: 0 auto;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border-radius: 60px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.search-input-wrapper:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.search-input-wrapper:focus-within {
  border-color: #6366f1;
  box-shadow: 0 12px 40px rgba(99, 102, 241, 0.15);
}

.search-icon-wrapper {
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-icon {
  color: #6366f1;
  font-size: 18px;
  transition: all 0.3s ease;
}

.search-input {
  flex: 1;
  padding: 20px 0;
  border: none;
  background: transparent;
  font-size: 16px;
  color: #1f2937;
  outline: none;
}

.search-input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.search-actions {
  display: flex;
  align-items: center;
  padding-right: 8px;
  gap: 4px;
}

.clear-search {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  opacity: 0.7;
}

.clear-search:hover {
  background: rgba(239, 68, 68, 0.2);
  opacity: 1;
  transform: scale(1.1);
}

.search-divider {
  width: 1px;
  height: 20px;
  background: #e5e7eb;
  margin: 0 4px;
}

.search-filter-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.search-filter-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
}

/* 搜索状态和建议增强 */
.search-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 20px 0;
  font-size: 14px;
}

.search-results-count {
  color: #6366f1;
  font-weight: 600;
}

.search-loading {
  color: #6366f1;
}

.search-suggestions-enhanced {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  margin-top: 12px;
  z-index: 10;
  overflow: hidden;
  animation: slideDown 0.3s ease-out;
}

.suggestions-header {
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.suggestions-count {
  margin-left: auto;
  background: #6366f1;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.suggestions-list {
  max-height: 300px;
  overflow-y: auto;
}

.suggestion-item-enhanced {
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f1f5f9;
}

.suggestion-item-enhanced:last-child {
  border-bottom: none;
}

.suggestion-item-enhanced:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(139, 92, 246, 0.05));
  transform: translateX(4px);
}

.suggestion-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  flex-shrink: 0;
}

.suggestion-content {
  flex: 1;
  min-width: 0;
}

.suggestion-text {
  display: block;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2px;
}

.suggestion-type {
  display: block;
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 8px;
  border-radius: 8px;
  display: inline-block;
}

.suggestion-arrow {
  color: #9ca3af;
  opacity: 0;
  transition: all 0.2s ease;
}

.suggestion-item-enhanced:hover .suggestion-arrow {
  opacity: 1;
  transform: translateX(4px);
}

.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  margin-top: 8px;
  z-index: 10;
  overflow: hidden;
}

.suggestions-header {
  padding: 12px 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #64748b;
}

.suggestion-item {
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.suggestion-item:hover {
  background: #f8fafc;
}

.suggestion-type {
  margin-left: auto;
  font-size: 12px;
  color: #9ca3af;
  background: #f1f5f9;
  padding: 2px 8px;
  border-radius: 8px;
}

.tab-navigation {
  margin-bottom: 30px;
}

.tab-list {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.tab-item {
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid transparent;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.tab-item:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(99, 102, 241, 0.3);
  transform: translateY(-1px);
}

.tab-item.active {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.tab-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.tab-item:not(.active) .tab-badge {
  background: #f1f5f9;
  color: #64748b;
}

.create-team-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* ===== 高级筛选区域 ===== */
.advanced-filters {
  margin-bottom: 20px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filter-section {
  text-align: center;
}

.filter-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
}

.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.tag-chip {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid #e5e7eb;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.tag-chip:hover {
  border-color: #6366f1;
  background: rgba(99, 102, 241, 0.05);
}

.tag-chip.active {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border-color: transparent;
}

.tag-count {
  background: rgba(0, 0, 0, 0.1);
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
}

.tag-chip:not(.active) .tag-count {
  background: #f1f5f9;
  color: #64748b;
}

.filter-controls {
  display: flex;
  gap: 30px;
  justify-content: center;
  align-items: end;
  flex-wrap: wrap;
}

.sort-control, .view-control {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.control-label {
  font-size: 14px;
  font-weight: 600;
  color: #64748b;
}

.sort-select {
  padding: 10px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sort-select:focus {
  outline: none;
  border-color: #6366f1;
}

.view-toggle {
  display: flex;
  gap: 4px;
  background: #f1f5f9;
  padding: 4px;
  border-radius: 12px;
}

.view-btn {
  padding: 8px 12px;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  color: #64748b;
  transition: all 0.3s ease;
}

.view-btn.active {
  background: white;
  color: #6366f1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.results-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-top: 1px solid #e5e7eb;
  flex-wrap: wrap;
  gap: 16px;
}

.summary-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.results-count {
  font-size: 16px;
  color: #374151;
}

.filter-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #6366f1;
  background: rgba(99, 102, 241, 0.1);
  padding: 4px 12px;
  border-radius: 12px;
}

.clear-all-filters {
  background: none;
  border: 2px solid #e5e7eb;
  padding: 8px 16px;
  border-radius: 12px;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.clear-all-filters:hover {
  border-color: #ef4444;
  color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
}

/* ===== 🎯 团队展示区域 ===== */
.teams-display-section {
  margin-bottom: 40px;
}

.loading-container {
  margin: 40px 0;
}

.loading-grid {
  display: grid;
  gap: 24px;
}

.loading-grid.grid {
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
}

.loading-grid.list {
  grid-template-columns: 1fr;
}

.loading-card {
  background: white;
  border-radius: 20px;
  padding: 24px;
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}

.loading-shimmer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(99, 102, 241, 0.1),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.teams-container {
  display: grid;
  gap: 24px;
  margin: 40px 0;
}

.teams-container.grid {
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
}

.teams-container.list {
  grid-template-columns: 1fr;
}

/* ===== 🎨 革命性团队卡片设计 ===== */
.team-card {
  background: white;
  border-radius: 20px;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.team-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
  border-color: rgba(99, 102, 241, 0.3);
}

.team-card.list-view {
  display: flex;
  align-items: center;
  padding: 20px;
}

.team-card.is-member {
  border-color: #10b981;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.02), rgba(16, 185, 129, 0.05));
}

.team-card.is-starred {
  border-color: #f59e0b;
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.02), rgba(245, 158, 11, 0.05));
}

.team-card.is-public {
  border-color: #6366f1;
}

.card-header {
  padding: 24px 24px 16px;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  position: relative;
}

.team-avatar {
  position: relative;
  flex-shrink: 0;
}

.team-avatar img,
.avatar-placeholder {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  object-fit: cover;
}

.avatar-placeholder {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.status-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: white;
  border: 2px solid white;
}

.status-badge.member {
  background: #10b981;
}

.status-badge.public {
  background: #6366f1;
}

.status-badge.private {
  background: #64748b;
}

.team-info {
  flex: 1;
  min-width: 0;
}

.team-name {
  font-size: 18px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.team-description {
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* ===== 🎯 右上角操作按钮 ===== */
.card-top-actions {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  gap: 8px;
  z-index: 5;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.team-card:hover .card-top-actions {
  opacity: 1;
  transform: translateY(0);
}

.top-action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.95);
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-size: 14px;
}

.top-action-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.top-action-btn.preview-btn {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.top-action-btn.preview-btn:hover {
  background: #6366f1;
  color: white;
  border-color: #6366f1;
}

.top-action-btn.star-btn {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.top-action-btn.star-btn:hover {
  background: rgba(245, 158, 11, 0.2);
  color: #d97706;
}

.top-action-btn.star-btn.active {
  background: #f59e0b;
  color: white;
  border-color: #f59e0b;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

.top-action-btn.star-btn.active:hover {
  background: #d97706;
  transform: scale(1.1) rotate(15deg);
}

.team-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
}

.team-avatar {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  margin-bottom: 16px;
}

.team-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.team-name {
  font-size: 18px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.team-description {
  font-size: 14px;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  color: #9ca3af;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

/* ===== 🎨 分页控制 ===== */
.pagination-container {
  display: flex;
  justify-content: center;
  margin: 40px 0;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  padding: 12px 20px;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.page-btn {
  padding: 8px 16px;
  border: none;
  background: transparent;
  color: #64748b;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.page-btn:hover:not(:disabled) {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 4px;
  margin: 0 8px;
}

.page-number {
  width: 36px;
  height: 36px;
  border: none;
  background: transparent;
  color: #64748b;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.page-number:hover:not(:disabled) {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.page-number.active {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.page-number.ellipsis {
  cursor: default;
  color: #9ca3af;
}

.page-number.ellipsis:hover {
  background: transparent;
  color: #9ca3af;
}

/* ===== 🎨 卡片主体和底部样式 ===== */
.card-body {
  padding: 0 24px 16px;
}

.team-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 16px;
}

.tag {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
}

.tag.more {
  background: #f1f5f9;
  color: #64748b;
}

.team-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.stat-item {
  text-align: center;
  padding: 8px;
  background: #f8fafc;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(99, 102, 241, 0.05);
}

.stat-item i {
  color: #6366f1;
  font-size: 14px;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 16px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1;
}

.stat-label {
  display: block;
  font-size: 11px;
  color: #64748b;
  margin-top: 2px;
}

.card-footer {
  padding: 16px 24px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #f1f5f9;
}

.team-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.created-time {
  font-size: 12px;
  color: #9ca3af;
  display: flex;
  align-items: center;
  gap: 4px;
}

.primary-actions {
  display: flex;
  gap: 8px;
}

.primary-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.primary-btn.enter {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.primary-btn.join {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.primary-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* ===== 增强样式 - 参考AI工具箱设计 ===== */

/* 页面头部增强 */
.page-header {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
  border-radius: 20px;
  margin-bottom: 30px;
}

.create-team-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
}

/* 搜索区域增强 */
.hero-search input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
}

/* Tab筛选器增强 */
.tab-btn:hover {
  border-color: #6366f1;
  color: #6366f1;
  transform: translateY(-1px);
}

.tab-btn.active {
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.tab-btn:not(.active) .tab-count {
  background: #f1f5f9;
  color: #64748b;
}

/* 团队卡片增强 */
.team-card {
  position: relative;
  overflow: hidden;
}

.team-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.team-card:hover::before {
  opacity: 1;
}

.team-card:hover {
  border-color: rgba(99, 102, 241, 0.3);
}

/* 加载状态增强 */
.loading-text {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #6b7280;
  gap: 12px;
}

.loading-text::before {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态增强 */
.empty-state {
  background: white;
  border-radius: 20px;
  padding: 60px 40px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.empty-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

/* 响应式设计增强 */
@media (max-width: 1200px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
  }

  .teams-container {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .page-header {
    padding: 20px 0;
  }

  .page-title {
    font-size: 28px;
  }

  .page-subtitle {
    font-size: 16px;
  }

  .filter-tabs {
    gap: 6px;
    overflow-x: auto;
    padding-bottom: 8px;
  }

  .tab-btn {
    padding: 10px 16px;
    font-size: 13px;
    flex-shrink: 0;
  }

  .teams-container {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .team-card {
    padding: 20px;
  }

  .team-avatar {
    width: 50px;
    height: 50px;
    border-radius: 10px;
  }

  .avatar-placeholder {
    font-size: 20px;
  }

  .team-name {
    font-size: 16px;
  }

  .team-description {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 16px 0;
  }

  .page-title {
    font-size: 24px;
  }

  .hero-search input {
    padding: 16px 16px 16px 48px;
    font-size: 15px;
  }

  .create-team-btn {
    padding: 10px 20px;
    font-size: 14px;
  }

  .team-card {
    padding: 16px;
  }

  .empty-state {
    padding: 40px 20px;
  }

  .empty-title {
    font-size: 18px;
  }

  .empty-description {
    font-size: 14px;
  }
}

/* 动画增强 */
.team-card {
  animation: fadeInUp 0.6s ease-out;
}

.team-card:nth-child(1) { animation-delay: 0.1s; }
.team-card:nth-child(2) { animation-delay: 0.2s; }
.team-card:nth-child(3) { animation-delay: 0.3s; }
.team-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条美化 */
.teams-section::-webkit-scrollbar {
  width: 6px;
}

.teams-section::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.teams-section::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 3px;
}

.teams-section::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
}

/* ===== 🎨 革命性响应式设计 ===== */
@media (max-width: 1200px) {
  .hero-title {
    font-size: 3rem;
  }

  .title-decoration {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    top: -15px;
    right: -45px;
  }

  .teams-container.grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .search-filter-section {
    padding: 30px 20px;
    margin: -40px 10px 30px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .hero-content {
    padding: 40px 20px;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .title-decoration {
    display: none;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .cta-button {
    width: 100%;
    max-width: 280px;
    justify-content: center;
  }

  .search-filter-section {
    padding: 20px 16px;
    margin: -30px 0 20px;
    border-radius: 16px;
  }

  .search-input {
    padding: 16px 50px 16px 50px;
    font-size: 15px;
  }

  .tab-list {
    gap: 6px;
    overflow-x: auto;
    padding-bottom: 8px;
  }

  .tab-item {
    padding: 10px 16px;
    font-size: 13px;
    flex-shrink: 0;
  }

  .filter-controls {
    flex-direction: column;
    gap: 16px;
  }

  .teams-container.grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .team-card {
    margin: 0 -4px;
  }

  .card-header {
    padding: 20px 20px 12px;
  }

  .team-avatar img,
  .avatar-placeholder {
    width: 50px;
    height: 50px;
    border-radius: 12px;
  }

  .avatar-placeholder {
    font-size: 20px;
  }

  .team-name {
    font-size: 16px;
  }

  .team-description {
    font-size: 13px;
  }

  .team-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .stat-item {
    padding: 6px;
  }

  .stat-value {
    font-size: 14px;
  }

  .card-footer {
    padding: 12px 20px 20px;
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .primary-btn {
    justify-content: center;
    padding: 10px 16px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .search-input {
    padding: 14px 45px 14px 45px;
    font-size: 14px;
  }

  .empty-state {
    padding: 60px 16px;
  }

  .empty-title {
    font-size: 18px;
  }

  .empty-description {
    font-size: 14px;
  }

  .empty-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn-outline,
  .btn-primary {
    width: 100%;
    max-width: 200px;
    justify-content: center;
  }
}

/* ===== 🎨 革命性动画效果 ===== */
.team-card {
  animation: fadeInUp 0.6s ease-out;
}

.team-card:nth-child(1) { animation-delay: 0.1s; }
.team-card:nth-child(2) { animation-delay: 0.2s; }
.team-card:nth-child(3) { animation-delay: 0.3s; }
.team-card:nth-child(4) { animation-delay: 0.4s; }
.team-card:nth-child(5) { animation-delay: 0.5s; }
.team-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 搜索建议动画 */
.search-suggestions {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tab切换动画 */
.tab-item {
  position: relative;
  overflow: hidden;
}

.tab-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.tab-item:hover::before {
  left: 100%;
}

/* 按钮点击效果 */
.cta-button,
.primary-btn,
.btn-primary {
  position: relative;
  overflow: hidden;
}

.cta-button::after,
.primary-btn::after,
.btn-primary::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.cta-button:active::after,
.primary-btn:active::after,
.btn-primary:active::after {
  width: 300px;
  height: 300px;
}

/* 滚动条美化 */
.teams-display-section::-webkit-scrollbar {
  width: 6px;
}

.teams-display-section::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.teams-display-section::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 3px;
}

.teams-display-section::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
}

/* 加载动画增强 */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(99, 102, 241, 0.1),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 微交互增强 */
.tag-chip,
.action-btn,
.view-btn,
.sort-select {
  transform-origin: center;
}

.tag-chip:active,
.action-btn:active,
.view-btn:active {
  transform: scale(0.95);
}

/* 焦点状态增强 */
.search-input:focus,
.sort-select:focus {
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
}

/* 悬停状态增强 */
.team-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.team-card:hover::before {
  opacity: 1;
}

/* 状态指示器动画 */
.status-badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* 粒子效果增强 */
.floating-particles .particle {
  animation: particle-float 8s linear infinite;
}

.floating-particles .particle:nth-child(1) { left: 10%; animation-delay: 0s; }
.floating-particles .particle:nth-child(2) { left: 20%; animation-delay: 1s; }
.floating-particles .particle:nth-child(3) { left: 30%; animation-delay: 2s; }
.floating-particles .particle:nth-child(4) { left: 40%; animation-delay: 3s; }
.floating-particles .particle:nth-child(5) { left: 50%; animation-delay: 4s; }
.floating-particles .particle:nth-child(6) { left: 60%; animation-delay: 5s; }
.floating-particles .particle:nth-child(7) { left: 70%; animation-delay: 6s; }
.floating-particles .particle:nth-child(8) { left: 80%; animation-delay: 7s; }
.floating-particles .particle:nth-child(9) { left: 90%; animation-delay: 0.5s; }
.floating-particles .particle:nth-child(10) { left: 15%; animation-delay: 1.5s; }
.floating-particles .particle:nth-child(11) { left: 25%; animation-delay: 2.5s; }
.floating-particles .particle:nth-child(12) { left: 35%; animation-delay: 3.5s; }
.floating-particles .particle:nth-child(13) { left: 45%; animation-delay: 4.5s; }
.floating-particles .particle:nth-child(14) { left: 55%; animation-delay: 5.5s; }
.floating-particles .particle:nth-child(15) { left: 65%; animation-delay: 6.5s; }
.floating-particles .particle:nth-child(16) { left: 75%; animation-delay: 7.5s; }
.floating-particles .particle:nth-child(17) { left: 85%; animation-delay: 0.2s; }
.floating-particles .particle:nth-child(18) { left: 95%; animation-delay: 1.2s; }
.floating-particles .particle:nth-child(19) { left: 5%; animation-delay: 2.2s; }
.floating-particles .particle:nth-child(20) { left: 95%; animation-delay: 3.2s; }
</style>
