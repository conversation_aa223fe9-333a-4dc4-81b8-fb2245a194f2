<template>
  <Layout>
    <div class="team-space-detail">
      <!-- 顶部导航栏 -->
      <header class="team-header">
        <div class="container">
          <nav class="header-nav">
            <div class="nav-left">
              <button class="back-button" @click="goBack">
                <i class="fas fa-arrow-left"></i>
                <span>返回</span>
              </button>
              
              <div class="breadcrumb">
                <span>团队空间</span>
                <i class="fas fa-chevron-right"></i>
                <div class="space-selector">
                  <button class="current-space" @click="toggleSpaceSelector">
                    {{ team.name }}
                    <i class="fas fa-chevron-down"></i>
                  </button>
                  
                  <!-- 空间切换下拉菜单 -->
                  <div class="space-dropdown" v-show="showSpaceSelector">
                    <div class="space-search">
                      <i class="fas fa-search"></i>
                      <input 
                        type="text" 
                        placeholder="搜索团队空间..."
                        v-model="spaceSearchQuery"
                      >
                    </div>
                    
                    <div class="space-list">
                      <div class="space-section">
                        <h4>我的团队</h4>
                        <div 
                          v-for="space in filteredMySpaces" 
                          :key="space.id"
                          class="space-item"
                          :class="{ active: space.id === team.id }"
                          @click="switchSpace(space)"
                        >
                          <img :src="space.avatar" :alt="space.name" class="space-avatar">
                          <div class="space-info">
                            <div class="space-name">{{ space.name }}</div>
                            <div class="space-meta">{{ space.membersCount }} 成员</div>
                          </div>
                          <div class="space-status" v-if="space.isActive">
                            <i class="fas fa-circle"></i>
                          </div>
                        </div>
                      </div>
                      
                      <div class="space-section" v-if="filteredOtherSpaces.length > 0">
                        <h4>其他空间</h4>
                        <div 
                          v-for="space in filteredOtherSpaces" 
                          :key="space.id"
                          class="space-item"
                          @click="switchSpace(space)"
                        >
                          <img :src="space.avatar" :alt="space.name" class="space-avatar">
                          <div class="space-info">
                            <div class="space-name">{{ space.name }}</div>
                            <div class="space-meta">{{ space.membersCount }} 成员</div>
                          </div>
                          <div class="space-actions">
                            <button class="join-btn" @click.stop="joinSpace(space)">
                              <i class="fas fa-plus"></i>
                            </button>
                          </div>
                        </div>
                        
                        <button class="show-more-spaces" @click="showAllSpaces" v-if="hasMoreSpaces">
                          <i class="fas fa-ellipsis-h"></i>
                          查看更多空间
                        </button>
                      </div>
                    </div>
                    
                    <div class="space-actions-footer">
                      <button class="create-space-btn" @click="createNewSpace">
                        <i class="fas fa-plus"></i>
                        创建新空间
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="nav-actions">
              <button class="action-btn" @click="toggleStar" :class="{ starred: team.isStarred }">
                <i :class="team.isStarred ? 'fas fa-star' : 'far fa-star'"></i>
              </button>
              <button class="action-btn" @click="shareTeam">
                <i class="fas fa-share-alt"></i>
              </button>
              <div class="dropdown" v-if="isAdmin">
                <button class="action-btn" @click="toggleDropdown">
                  <i class="fas fa-ellipsis-h"></i>
                </button>
                <div class="dropdown-menu" v-show="showDropdown">
                  <button @click="editTeam">编辑团队</button>
                  <button @click="openMemberManagement">成员管理</button>
                  <button @click="teamSettings">团队设置</button>
                </div>
              </div>
            </div>
          </nav>
        </div>
      </header>

      <!-- 团队英雄区域 -->
      <section class="team-hero">
        <div class="hero-background" :style="{ background: team.themeColor }">
          <div class="hero-pattern"></div>
        </div>
        
        <div class="container">
          <div class="hero-content">
            <div class="team-profile">
              <div class="team-avatar">
                <img v-if="team.avatar" :src="team.avatar" :alt="team.name">
                <div v-else class="avatar-placeholder">
                  <i class="fas fa-users"></i>
                </div>
                <div class="status-indicator" v-if="team.isActive"></div>
              </div>
              
              <div class="team-info">
                <div class="team-badges">
                  <span class="badge" :class="team.isPublic ? 'public' : 'private'">
                    <i :class="team.isPublic ? 'fas fa-globe' : 'fas fa-lock'"></i>
                    {{ team.isPublic ? '公开' : '私有' }}
                  </span>
                  <span class="badge verified" v-if="team.isVerified">
                    <i class="fas fa-shield-check"></i>
                    已认证
                  </span>
                </div>
                
                <h1 class="team-name">{{ team.name }}</h1>
                <p class="team-description">{{ team.description }}</p>
                
                <div class="team-meta">
                  <span class="meta-item">
                    <i class="fas fa-calendar-plus"></i>
                    创建于 {{ formatDate(team.createdAt) }}
                  </span>
                  <span class="meta-item" v-if="team.location">
                    <i class="fas fa-map-marker-alt"></i>
                    {{ team.location }}
                  </span>
                </div>
                
                <div class="team-tags" v-if="team.tags?.length">
                  <span v-for="tag in team.tags" :key="tag" class="tag">{{ tag }}</span>
                </div>
              </div>
            </div>
            
            <div class="team-stats">
              <div class="stat-card" @click="scrollToMembers">
                <div class="stat-number">{{ team.membersCount }}</div>
                <div class="stat-label">成员</div>
              </div>
              <div class="stat-card" @click="showContent">
                <div class="stat-number">{{ team.contentCount }}</div>
                <div class="stat-label">内容</div>
              </div>
              <div class="stat-card" @click="showDiscussions">
                <div class="stat-number">{{ team.discussionsCount }}</div>
                <div class="stat-label">讨论</div>
              </div>
            </div>
            
            <div class="team-actions">
              <button 
                v-if="!team.isMember" 
                class="btn btn-primary" 
                @click="joinTeam"
                :disabled="joinLoading"
              >
                <i class="fas fa-plus"></i>
                {{ team.isPublic ? '加入团队' : '申请加入' }}
              </button>
              
              <button 
                v-else 
                class="btn btn-outline" 
                @click="inviteMembers"
              >
                <i class="fas fa-user-plus"></i>
                邀请成员
              </button>
              
              <button class="btn btn-outline" @click="startChat">
                <i class="fas fa-comments"></i>
                团队聊天
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- 主要内容区域 -->
      <main class="team-main">
        <div class="container">
          <div class="content-layout">
            <!-- 侧边栏 -->
            <aside class="sidebar">
              <!-- 在线成员 -->
              <div class="widget">
                <h3 class="widget-title">
                  <i class="fas fa-circle online-dot"></i>
                  在线成员 ({{ onlineMembers.length }})
                </h3>
                <div class="members-list">
                  <div 
                    v-for="member in onlineMembers.slice(0, 6)" 
                    :key="member.id" 
                    class="member-item"
                  >
                    <img 
                      :src="member.avatar" 
                      :alt="member.name" 
                      class="member-avatar clickable"
                      @click="viewMemberProfile(member)"
                    >
                    <div class="member-info">
                      <div 
                        class="member-name clickable"
                        @click="viewMemberProfile(member)"
                      >{{ member.name }}</div>
                      <div class="member-status">{{ member.status || '在线' }}</div>
                    </div>
                    <button class="quick-chat-btn" @click.stop="startDirectChat(member)">
                      <i class="fas fa-comment"></i>
                    </button>
                  </div>
                  <button v-if="team.membersCount > 6" class="view-all" @click="setActiveTab('members')">
                    查看全部成员
                  </button>
                </div>
              </div>
              
              <!-- 成员贡献度 -->
              <div class="widget">
                <h3 class="widget-title">
                  <i class="fas fa-chart-line"></i>
                  成员贡献度
                </h3>
                <div class="contribution-list">
                  <div 
                    v-for="(member, index) in topContributors" 
                    :key="member.id" 
                    class="contribution-item"
                  >
                    <div class="rank-badge" :class="getRankClass(index)">
                      {{ index + 1 }}
                    </div>
                    <img 
                      :src="member.avatar" 
                      :alt="member.name" 
                      class="contributor-avatar clickable"
                      @click="viewMemberProfile(member)"
                    >
                    <div class="contributor-info">
                      <div 
                        class="contributor-name clickable"
                        @click="viewMemberProfile(member)"
                      >{{ member.name }}</div>
                      <div class="contribution-score">{{ member.contributionScore }} 分</div>
                    </div>
                    <div class="contribution-chart">
                      <div class="progress-bar">
                        <div 
                          class="progress-fill" 
                          :style="{ width: (member.contributionScore / maxContribution * 100) + '%' }"
                        ></div>
                      </div>
                    </div>
                  </div>
                  <button class="view-all" @click="showContributionDetails">
                    查看详细贡献
                  </button>
                </div>
              </div>
              
              <!-- 快速工具 -->
              <div class="widget" v-if="team.isMember">
                <h3 class="widget-title">
                  <i class="fas fa-tools"></i>
                  快速工具
                </h3>
                <div class="tools-grid">
                  <button class="tool-btn" @click="openTool('calendar')">
                    <i class="fas fa-calendar"></i>
                    <span>日历</span>
                  </button>
                  <button class="tool-btn" @click="openTool('files')">
                    <i class="fas fa-folder"></i>
                    <span>文件</span>
                  </button>
                  <button class="tool-btn" @click="openTool('tasks')">
                    <i class="fas fa-tasks"></i>
                    <span>任务</span>
                  </button>
                  <button class="tool-btn" @click="openTool('notes')">
                    <i class="fas fa-sticky-note"></i>
                    <span>笔记</span>
                  </button>
                </div>
              </div>
              
              <!-- 团队动态 -->
              <div class="widget">
                <h3 class="widget-title">
                  <i class="fas fa-stream"></i>
                  团队动态
                </h3>
                <div class="activity-list">
                  <div
                    v-for="activity in recentActivities.slice(0, 5)"
                    :key="activity.id"
                    class="activity-item"
                  >
                    <div class="activity-icon">
                      <i :class="activity.icon"></i>
                    </div>
                    <div class="activity-content">
                      <div class="activity-text">
                        <span
                          class="activity-user clickable"
                          @click="viewMemberProfile(activity.user)"
                        >{{ activity.user?.name }}</span>
                        {{ activity.action }}
                      </div>
                      <div class="activity-time">{{ formatTime(activity.time) }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 热门内容 -->
              <div class="widget">
                <h3 class="widget-title">
                  <i class="fas fa-fire"></i>
                  热门内容
                </h3>
                <div class="popular-content-list">
                  <div
                    v-for="item in popularContent.slice(0, 4)"
                    :key="item.id"
                    class="popular-item"
                    @click="openContent(item)"
                  >
                    <div class="content-icon">
                      <i :class="getContentIcon(item.type)"></i>
                    </div>
                    <div class="content-info">
                      <div class="content-title">{{ item.title }}</div>
                      <div class="content-meta">
                        <span class="views">{{ item.views }} 浏览</span>
                        <span class="likes">{{ item.likes || 0 }} 点赞</span>
                      </div>
                    </div>
                  </div>
                  <button class="view-all" @click="setActiveTab('content')">
                    查看全部内容
                  </button>
                </div>
              </div>

              <!-- 团队成就 -->
              <div class="widget">
                <h3 class="widget-title">
                  <i class="fas fa-trophy"></i>
                  团队成就
                </h3>
                <div class="achievements-compact">
                  <div
                    v-for="achievement in teamAchievements.slice(0, 3)"
                    :key="achievement.id"
                    class="achievement-compact-item"
                  >
                    <div class="achievement-icon">
                      <i :class="achievement.icon"></i>
                    </div>
                    <div class="achievement-text">
                      <div class="achievement-title">{{ achievement.title }}</div>
                      <div class="achievement-desc">{{ achievement.description }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </aside>
            
            <!-- 主内容区 -->
            <div class="main-content">
              <!-- 标签导航 -->
              <nav class="content-nav">
                <div class="nav-tabs">
                  <button 
                    v-for="tab in tabs" 
                    :key="tab.key"
                    class="nav-tab"
                    :class="{ active: activeTab === tab.key }"
                    @click="setActiveTab(tab.key)"
                  >
                    <i :class="tab.icon"></i>
                    <span>{{ tab.label }}</span>
                    <span v-if="tab.count" class="tab-count">{{ tab.count }}</span>
                  </button>
                </div>
                
                <div class="nav-actions">
                  <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input 
                      type="text" 
                      placeholder="搜索..."
                      v-model="searchQuery"
                    >
                  </div>
                  
                  <button 
                    v-if="team.isMember && canCreate" 
                    class="btn btn-primary btn-sm"
                    @click="createContent"
                  >
                    <i class="fas fa-plus"></i>
                    创建
                  </button>
                </div>
              </nav>
              
              <!-- 标签内容 -->
              <div class="tab-content">
                <!-- 内容管理 -->
                <div v-if="activeTab === 'content'" class="tab-panel">
                  <div class="content-management">
                    <!-- 内容过滤器 -->
                    <div class="content-filters">
                      <div class="filter-buttons">
                        <button
                          v-for="filter in contentFilters"
                          :key="filter.key"
                          class="filter-btn"
                          :class="{ active: activeContentFilter === filter.key }"
                          @click="setContentFilter(filter.key)"
                        >
                          <i :class="filter.icon"></i>
                          {{ filter.label }}
                          <span class="filter-count" v-if="filter.count">({{ filter.count }})</span>
                        </button>
                      </div>

                      <div class="content-view-toggle">
                        <button
                          class="view-btn"
                          :class="{ active: contentView === 'grid' }"
                          @click="contentView = 'grid'"
                          title="网格视图"
                        >
                          <i class="fas fa-th"></i>
                        </button>
                        <button
                          class="view-btn"
                          :class="{ active: contentView === 'list' }"
                          @click="contentView = 'list'"
                          title="列表视图"
                        >
                          <i class="fas fa-list"></i>
                        </button>
                      </div>
                    </div>

                    <!-- 内容列表 -->
                    <div class="content-container" :class="contentView">
                      <div
                        v-for="content in filteredContent"
                        :key="content.id"
                        class="content-card"
                        @click="openContent(content)"
                      >
                        <div class="content-thumbnail">
                          <img v-if="content.thumbnail" :src="content.thumbnail" :alt="content.title">
                          <div v-else class="thumbnail-placeholder">
                            <i :class="getContentIcon(content.type)"></i>
                          </div>
                          <div class="content-type-badge">{{ getContentTypeLabel(content.type) }}</div>
                        </div>

                        <div class="content-body">
                          <h3 class="content-title">{{ content.title }}</h3>
                          <p class="content-description">{{ content.description }}</p>

                          <div class="content-meta">
                            <div class="author-info">
                              <img
                                :src="content.author.avatar"
                                :alt="content.author.name"
                                class="author-avatar clickable"
                                @click.stop="viewMemberProfile(content.author)"
                              >
                              <span
                                class="author-name clickable"
                                @click.stop="viewMemberProfile(content.author)"
                              >{{ content.author.name }}</span>
                            </div>

                            <div class="content-stats">
                              <span class="stat">
                                <i class="fas fa-eye"></i>
                                {{ content.views }}
                              </span>
                              <span class="stat">
                                <i class="fas fa-thumbs-up"></i>
                                {{ content.likes }}
                              </span>
                              <span class="stat">
                                <i class="fas fa-comment"></i>
                                {{ content.comments }}
                              </span>
                            </div>
                          </div>

                          <div class="content-footer">
                            <div class="content-tags" v-if="content.tags?.length">
                              <span v-for="tag in content.tags.slice(0, 3)" :key="tag" class="content-tag">
                                {{ tag }}
                              </span>
                            </div>
                            <div class="content-time">{{ formatTime(content.updatedAt) }}</div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 空状态 -->
                    <div v-if="filteredContent.length === 0" class="empty-state">
                      <i class="fas fa-file-alt"></i>
                      <h3>暂无内容</h3>
                      <p>还没有发布任何内容，开始创建第一个内容吧！</p>
                      <button
                        v-if="team.isMember"
                        class="btn btn-primary"
                        @click="createContent"
                      >
                        <i class="fas fa-plus"></i>
                        创建内容
                      </button>
                    </div>
                  </div>
                </div>

                <!-- 团队讨论 -->
                <div v-else-if="activeTab === 'discussions'" class="tab-panel">
                  <div class="discussions-container">
                    <!-- 讨论过滤器 -->
                    <div class="discussion-filters">
                      <div class="filter-buttons">
                        <button
                          v-for="filter in discussionFilters"
                          :key="filter.key"
                          class="filter-btn"
                          :class="{ active: activeDiscussionFilter === filter.key }"
                          @click="setDiscussionFilter(filter.key)"
                        >
                          <i :class="filter.icon"></i>
                          {{ filter.label }}
                          <span class="filter-count" v-if="filter.count">({{ filter.count }})</span>
                        </button>
                      </div>

                      <button
                        v-if="team.isMember"
                        class="btn btn-primary"
                        @click="showCreateDiscussion = true"
                      >
                        <i class="fas fa-plus"></i>
                        发起讨论
                      </button>
                    </div>

                    <!-- 讨论列表 -->
                    <div class="discussions-list">
                      <div
                        v-for="discussion in filteredDiscussions"
                        :key="discussion.id"
                        class="discussion-card"
                        @click="openDiscussion(discussion)"
                      >
                        <div class="discussion-header">
                          <div class="discussion-meta">
                            <img
                              :src="discussion.author.avatar"
                              :alt="discussion.author.name"
                              class="author-avatar clickable"
                              @click.stop="viewMemberProfile(discussion.author)"
                            >
                            <div class="meta-info">
                              <span
                                class="author-name clickable"
                                @click.stop="viewMemberProfile(discussion.author)"
                              >{{ discussion.author.name }}</span>
                              <span class="discussion-time">{{ formatTime(discussion.createdAt) }}</span>
                            </div>
                          </div>

                          <div class="discussion-status">
                            <span class="status-badge" :class="discussion.status">
                              {{ getDiscussionStatusLabel(discussion.status) }}
                            </span>
                          </div>
                        </div>

                        <div class="discussion-content">
                          <h3 class="discussion-title">{{ discussion.title }}</h3>
                          <p class="discussion-preview">{{ discussion.preview }}</p>

                          <div class="discussion-tags" v-if="discussion.tags?.length">
                            <span v-for="tag in discussion.tags" :key="tag" class="discussion-tag">
                              {{ tag }}
                            </span>
                          </div>
                        </div>

                        <div class="discussion-footer">
                          <div class="discussion-stats">
                            <span class="stat">
                              <i class="fas fa-comment"></i>
                              {{ discussion.repliesCount }} 回复
                            </span>
                            <span class="stat">
                              <i class="fas fa-eye"></i>
                              {{ discussion.viewsCount }} 浏览
                            </span>
                            <span class="stat">
                              <i class="fas fa-thumbs-up"></i>
                              {{ discussion.likesCount }} 点赞
                            </span>
                          </div>

                          <div class="last-reply" v-if="discussion.lastReply">
                            <span class="last-reply-text">
                              最后回复：
                              <span
                                class="last-reply-author clickable"
                                @click.stop="viewMemberProfile(discussion.lastReply.author)"
                              >{{ discussion.lastReply.author.name }}</span>
                            </span>
                            <span class="last-reply-time">{{ formatTime(discussion.lastReply.time) }}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 空状态 -->
                    <div v-if="filteredDiscussions.length === 0" class="empty-state">
                      <i class="fas fa-comments"></i>
                      <h3>暂无讨论</h3>
                      <p>还没有人发起讨论，成为第一个发起讨论的人吧！</p>
                      <button
                        v-if="team.isMember"
                        class="btn btn-primary"
                        @click="showCreateDiscussion = true"
                      >
                        <i class="fas fa-plus"></i>
                        发起讨论
                      </button>
                    </div>
                  </div>
                </div>

                <!-- 成员管理 -->
                <div v-else-if="activeTab === 'members'" class="tab-panel">
                  <div class="members-management">
                    <!-- 成员统计概览 -->
                    <div class="members-overview">
                      <div class="overview-stats">
                        <div class="stat-item">
                          <div class="stat-number">{{ team.membersCount }}</div>
                          <div class="stat-label">总成员</div>
                        </div>
                        <div class="stat-item">
                          <div class="stat-number">{{ onlineMembers.length }}</div>
                          <div class="stat-label">在线成员</div>
                        </div>
                        <div class="stat-item">
                          <div class="stat-number">{{ adminCount }}</div>
                          <div class="stat-label">管理员</div>
                        </div>
                        <div class="stat-item">
                          <div class="stat-number">{{ newMembersThisMonth }}</div>
                          <div class="stat-label">本月新增</div>
                        </div>
                      </div>

                      <div class="members-actions">
                        <button class="btn btn-primary" @click="inviteMembers">
                          <i class="fas fa-user-plus"></i>
                          邀请成员
                        </button>
                        <button class="btn btn-outline" @click="exportMemberList">
                          <i class="fas fa-download"></i>
                          导出列表
                        </button>
                      </div>
                    </div>

                    <!-- 成员过滤和搜索 -->
                    <div class="members-toolbar">
                      <div class="filter-buttons">
                        <button
                          v-for="filter in memberFilters"
                          :key="filter.key"
                          class="filter-btn"
                          :class="{ active: activeMemberFilter === filter.key }"
                          @click="setMemberFilter(filter.key)"
                        >
                          <i :class="filter.icon"></i>
                          {{ filter.label }}
                          <span class="filter-count" v-if="filter.count">({{ filter.count }})</span>
                        </button>
                      </div>

                      <div class="search-members">
                        <i class="fas fa-search"></i>
                        <input
                          type="text"
                          placeholder="搜索成员..."
                          v-model="memberSearchQuery"
                        >
                      </div>
                    </div>

                    <!-- 成员网格 -->
                    <div class="members-grid">
                      <div
                        v-for="member in filteredMembersForTab"
                        :key="member.id"
                        class="member-card"
                        @click="viewMemberProfile(member)"
                      >
                        <div class="member-header">
                          <img
                            :src="member.avatar"
                            :alt="member.name"
                            class="member-avatar"
                          >
                          <div class="member-status" :class="{ online: member.isOnline }">
                            <i class="fas fa-circle"></i>
                          </div>
                          <div class="member-role-badge" :class="member.role">
                            {{ getRoleLabel(member.role) }}
                          </div>
                        </div>

                        <div class="member-info">
                          <h3 class="member-name">{{ member.name }}</h3>
                          <p class="member-email">{{ member.email }}</p>

                          <div class="member-contribution">
                            <div class="contribution-label">贡献度</div>
                            <div class="contribution-bar">
                              <div
                                class="contribution-fill"
                                :style="{ width: (member.contributionScore / maxContribution * 100) + '%' }"
                              ></div>
                            </div>
                            <div class="contribution-score">{{ member.contributionScore }} 分</div>
                          </div>

                          <div class="member-meta">
                            <div class="join-date">
                              <i class="fas fa-calendar"></i>
                              {{ formatDate(member.joinedAt) }} 加入
                            </div>
                          </div>
                        </div>

                        <div class="member-actions" v-if="isAdmin">
                          <button
                            class="action-btn"
                            @click.stop="startDirectChat(member)"
                            title="私聊"
                          >
                            <i class="fas fa-comment"></i>
                          </button>
                          <button
                            class="action-btn"
                            @click.stop="editMemberRole(member)"
                            title="编辑角色"
                            v-if="canManageMember(member)"
                          >
                            <i class="fas fa-user-cog"></i>
                          </button>
                          <button
                            class="action-btn danger"
                            @click.stop="removeMember(member)"
                            title="移出团队"
                            v-if="canRemoveMember(member)"
                          >
                            <i class="fas fa-user-minus"></i>
                          </button>
                        </div>
                      </div>
                    </div>

                    <!-- 空状态 -->
                    <div v-if="filteredMembersForTab.length === 0" class="empty-state">
                      <i class="fas fa-users"></i>
                      <h3>暂无成员</h3>
                      <p>没有找到符合条件的成员</p>
                    </div>
                  </div>
                </div>

                <!-- 数据洞察 -->
                <div v-else-if="activeTab === 'analytics'" class="tab-panel">
                  <div class="analytics-dashboard">
                    <!-- 核心指标 -->
                    <div class="metrics-grid">
                      <div class="metric-card">
                        <div class="metric-header">
                          <h3>团队活跃度</h3>
                          <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="metric-value">85%</div>
                        <div class="metric-trend positive">
                          <i class="fas fa-arrow-up"></i>
                          +12% 较上月
                        </div>
                      </div>

                      <div class="metric-card">
                        <div class="metric-header">
                          <h3>内容产出</h3>
                          <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="metric-value">156</div>
                        <div class="metric-trend positive">
                          <i class="fas fa-arrow-up"></i>
                          +23 本月
                        </div>
                      </div>

                      <div class="metric-card">
                        <div class="metric-header">
                          <h3>协作效率</h3>
                          <i class="fas fa-users"></i>
                        </div>
                        <div class="metric-value">92%</div>
                        <div class="metric-trend positive">
                          <i class="fas fa-arrow-up"></i>
                          +8% 较上月
                        </div>
                      </div>

                      <div class="metric-card">
                        <div class="metric-header">
                          <h3>讨论参与</h3>
                          <i class="fas fa-comments"></i>
                        </div>
                        <div class="metric-value">78%</div>
                        <div class="metric-trend neutral">
                          <i class="fas fa-minus"></i>
                          持平
                        </div>
                      </div>
                    </div>

                    <!-- 详细分析 -->
                    <div class="analytics-details">
                      <div class="analytics-section">
                        <h3>成员贡献分析</h3>
                        <div class="contribution-chart">
                          <div class="chart-placeholder">
                            <i class="fas fa-chart-bar"></i>
                            <p>贡献度分布图表</p>
                          </div>
                        </div>
                      </div>

                      <div class="analytics-section">
                        <h3>内容类型分布</h3>
                        <div class="content-distribution">
                          <div class="distribution-item">
                            <div class="distribution-label">文章</div>
                            <div class="distribution-bar">
                              <div class="distribution-fill" style="width: 45%"></div>
                            </div>
                            <div class="distribution-value">45%</div>
                          </div>
                          <div class="distribution-item">
                            <div class="distribution-label">工具</div>
                            <div class="distribution-bar">
                              <div class="distribution-fill" style="width: 30%"></div>
                            </div>
                            <div class="distribution-value">30%</div>
                          </div>
                          <div class="distribution-item">
                            <div class="distribution-label">设计</div>
                            <div class="distribution-bar">
                              <div class="distribution-fill" style="width: 25%"></div>
                            </div>
                            <div class="distribution-value">25%</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 其他标签内容 -->
                <div v-else class="tab-panel">
                  <div class="content-placeholder">
                    <i class="fas fa-construction"></i>
                    <h3>功能开发中</h3>
                    <p>{{ activeTab }} 功能正在开发中，敬请期待</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <!-- 邀请成员模态框 -->
      <div class="modal-overlay" v-show="showInviteModal" @click="closeInviteModal">
        <div class="modal-content invite-member-modal" @click.stop>
          <div class="modal-header">
            <h2>邀请成员</h2>
            <button class="close-btn" @click="closeInviteModal">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <div class="modal-body">
            <div class="invite-form">
              <div class="form-group">
                <label for="userSearch">搜索用户</label>
                <div class="user-search-container">
                  <div class="search-input-wrapper">
                    <i class="fas fa-search"></i>
                    <input
                      type="text"
                      id="userSearch"
                      v-model="userSearchQuery"
                      @input="searchUsers"
                      placeholder="输入用户名、邮箱或姓名..."
                      autocomplete="off"
                    >
                    <div class="search-loading" v-if="searchLoading">
                      <i class="fas fa-spinner fa-spin"></i>
                    </div>
                  </div>

                  <!-- 搜索结果下拉 -->
                  <div class="search-results" v-if="searchResults.length > 0 || (userSearchQuery && !searchLoading)">
                    <div
                      v-for="user in searchResults"
                      :key="user.id"
                      class="search-result-item"
                      @click="selectUser(user)"
                    >
                      <img :src="user.avatar" :alt="user.name" class="result-avatar">
                      <div class="result-info">
                        <div class="result-name">{{ user.name }}</div>
                        <div class="result-email">{{ user.email }}</div>
                        <div class="result-meta" v-if="user.title">{{ user.title }}</div>
                      </div>
                      <div class="result-status" v-if="user.isOnline">
                        <i class="fas fa-circle online"></i>
                      </div>
                    </div>

                    <div v-if="searchResults.length === 0 && userSearchQuery && !searchLoading" class="no-results">
                      <i class="fas fa-user-slash"></i>
                      <span>未找到匹配的用户</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 已选择的用户 -->
              <div class="form-group" v-if="selectedUsers.length > 0">
                <label>已选择的用户 ({{ selectedUsers.length }})</label>
                <div class="selected-users">
                  <div
                    v-for="user in selectedUsers"
                    :key="user.id"
                    class="selected-user-item"
                  >
                    <img :src="user.avatar" :alt="user.name" class="selected-avatar">
                    <div class="selected-info">
                      <div class="selected-name">{{ user.name }}</div>
                      <div class="selected-email">{{ user.email }}</div>
                    </div>
                    <button class="remove-user-btn" @click="removeSelectedUser(user)">
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                </div>
              </div>

              <!-- 邀请角色选择 -->
              <div class="form-group" v-if="selectedUsers.length > 0">
                <label>邀请角色</label>
                <div class="role-selector">
                  <label class="role-option">
                    <input type="radio" v-model="inviteRole" value="member">
                    <span class="role-label">
                      <i class="fas fa-user"></i>
                      <div class="role-info">
                        <div class="role-title">成员</div>
                        <div class="role-desc">可以查看和参与团队内容</div>
                      </div>
                    </span>
                  </label>
                  <label class="role-option">
                    <input type="radio" v-model="inviteRole" value="admin">
                    <span class="role-label">
                      <i class="fas fa-user-shield"></i>
                      <div class="role-info">
                        <div class="role-title">管理员</div>
                        <div class="role-desc">可以管理团队成员和设置</div>
                      </div>
                    </span>
                  </label>
                </div>
              </div>

              <!-- 邀请消息 -->
              <div class="form-group" v-if="selectedUsers.length > 0">
                <label for="inviteMessage">邀请消息 (可选)</label>
                <textarea
                  id="inviteMessage"
                  v-model="inviteMessage"
                  placeholder="添加一些个人消息，让邀请更有温度..."
                  rows="3"
                  maxlength="200"
                ></textarea>
                <div class="char-count">{{ inviteMessage.length }}/200</div>
              </div>

              <div class="form-actions">
                <button type="button" class="btn btn-outline" @click="closeInviteModal">
                  取消
                </button>
                <button
                  type="button"
                  class="btn btn-primary"
                  @click="sendInvitations"
                  :disabled="selectedUsers.length === 0 || inviteLoading"
                >
                  <i class="fas fa-paper-plane" v-if="!inviteLoading"></i>
                  <i class="fas fa-spinner fa-spin" v-else></i>
                  {{ inviteLoading ? '发送中...' : `邀请 ${selectedUsers.length} 位用户` }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 日历模态框 -->
      <div class="modal-overlay" v-show="showCalendarModal" @click="closeCalendar">
        <div class="modal-content calendar-modal" @click.stop>
          <div class="modal-header">
            <h2>团队日历</h2>
            <button class="close-btn" @click="closeCalendar">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <div class="modal-body">
            <div class="calendar-container">
              <!-- 日历头部 -->
              <div class="calendar-header">
                <button class="nav-btn" @click="previousMonth">
                  <i class="fas fa-chevron-left"></i>
                </button>
                <h3 class="calendar-title">
                  {{ formatCalendarMonth(currentDate) }}
                </h3>
                <button class="nav-btn" @click="nextMonth">
                  <i class="fas fa-chevron-right"></i>
                </button>
              </div>

              <!-- 日历网格 -->
              <div class="calendar-grid">
                <!-- 星期标题 -->
                <div class="weekday-header">
                  <div v-for="day in weekdays" :key="day" class="weekday">{{ day }}</div>
                </div>

                <!-- 日期网格 -->
                <div class="dates-grid">
                  <div
                    v-for="date in calendarDates"
                    :key="date.key"
                    class="date-cell"
                    :class="{
                      'other-month': !date.isCurrentMonth,
                      'today': date.isToday,
                      'selected': date.isSelected,
                      'has-events': date.events.length > 0
                    }"
                    @click="selectDate(date.date)"
                  >
                    <div class="date-number">{{ date.day }}</div>
                    <div class="date-events" v-if="date.events.length > 0">
                      <div
                        v-for="event in date.events.slice(0, 2)"
                        :key="event.id"
                        class="event-dot"
                        :class="event.type"
                        :title="event.title"
                      ></div>
                      <div v-if="date.events.length > 2" class="more-events">
                        +{{ date.events.length - 2 }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 选中日期的事件详情 -->
              <div class="event-details" v-if="selectedDate">
                <h4>{{ formatSelectedDate(selectedDate) }} 的事项</h4>
                <div class="events-list">
                  <div
                    v-for="event in getEventsForDate(selectedDate)"
                    :key="event.id"
                    class="event-item"
                    :class="event.type"
                  >
                    <div class="event-time">{{ event.time }}</div>
                    <div class="event-content">
                      <div class="event-title">{{ event.title }}</div>
                      <div class="event-participants" v-if="event.participants?.length">
                        <i class="fas fa-users"></i>
                        {{ event.participants.join(', ') }}
                      </div>
                    </div>
                    <div class="event-type-icon">
                      <i :class="getEventIcon(event.type)"></i>
                    </div>
                  </div>

                  <div v-if="getEventsForDate(selectedDate).length === 0" class="no-events">
                    <i class="fas fa-calendar-day"></i>
                    <span>这一天没有安排事项</span>
                  </div>
                </div>

                <div class="event-actions">
                  <button class="btn btn-primary btn-sm" @click="createEvent">
                    <i class="fas fa-plus"></i>
                    添加事项
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 创建讨论模态框 -->
      <div class="modal-overlay" v-show="showCreateDiscussion" @click="closeCreateDiscussion">
        <div class="modal-content create-discussion-modal" @click.stop>
          <div class="modal-header">
            <h2>发起讨论</h2>
            <button class="close-btn" @click="closeCreateDiscussion">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <div class="modal-body">
            <form @submit.prevent="submitDiscussion" class="discussion-form">
              <div class="form-group">
                <label for="discussionTitle">讨论标题 *</label>
                <input
                  type="text"
                  id="discussionTitle"
                  v-model="newDiscussion.title"
                  placeholder="请输入讨论标题..."
                  required
                  maxlength="100"
                >
                <div class="char-count">{{ newDiscussion.title.length }}/100</div>
              </div>

              <div class="form-group">
                <label for="discussionContent">讨论内容 *</label>
                <textarea
                  id="discussionContent"
                  v-model="newDiscussion.content"
                  placeholder="详细描述你想讨论的问题..."
                  rows="6"
                  required
                  maxlength="1000"
                ></textarea>
                <div class="char-count">{{ newDiscussion.content.length }}/1000</div>
              </div>

              <div class="form-group">
                <label for="discussionTags">标签</label>
                <div class="tags-input">
                  <div class="selected-tags">
                    <span
                      v-for="tag in newDiscussion.tags"
                      :key="tag"
                      class="tag-item"
                    >
                      {{ tag }}
                      <button type="button" @click="removeTag(tag)">
                        <i class="fas fa-times"></i>
                      </button>
                    </span>
                  </div>
                  <input
                    type="text"
                    v-model="tagInput"
                    @keydown.enter.prevent="addTag"
                    @keydown="handleTagInput"
                    placeholder="输入标签后按回车添加..."
                    maxlength="20"
                  >
                </div>
                <div class="form-hint">最多可添加5个标签，用于分类和搜索</div>
              </div>

              <div class="form-group">
                <label>讨论类型</label>
                <div class="radio-group">
                  <label class="radio-item">
                    <input
                      type="radio"
                      v-model="newDiscussion.type"
                      value="question"
                    >
                    <span class="radio-label">
                      <i class="fas fa-question-circle"></i>
                      问题求助
                    </span>
                  </label>
                  <label class="radio-item">
                    <input
                      type="radio"
                      v-model="newDiscussion.type"
                      value="idea"
                    >
                    <span class="radio-label">
                      <i class="fas fa-lightbulb"></i>
                      想法分享
                    </span>
                  </label>
                  <label class="radio-item">
                    <input
                      type="radio"
                      v-model="newDiscussion.type"
                      value="announcement"
                    >
                    <span class="radio-label">
                      <i class="fas fa-bullhorn"></i>
                      公告通知
                    </span>
                  </label>
                </div>
              </div>

              <div class="form-actions">
                <button type="button" class="btn btn-outline" @click="closeCreateDiscussion">
                  取消
                </button>
                <button
                  type="submit"
                  class="btn btn-primary"
                  :disabled="!canSubmitDiscussion"
                >
                  <i class="fas fa-paper-plane"></i>
                  发起讨论
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import { ref, reactive, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import Layout from '../../../components/Layout.vue'
import { useToastStore } from '../../../stores/toast'

export default {
  name: 'TeamSpaceDetail',
  components: {
    Layout
  },
  setup() {
    const router = useRouter()
    const toastStore = useToastStore()

    // 响应式数据
    const joinLoading = ref(false)
    const showDropdown = ref(false)
    const showSpaceSelector = ref(false)
    const showMemberModal = ref(false)
    const showMemberActions = ref(false)
    const showCreateDiscussion = ref(false)
    const showInviteModal = ref(false)
    const showCalendarModal = ref(false)
    const activeTab = ref('content')
    const activeDiscussionFilter = ref('all')
    const activeContentFilter = ref('all')
    const activeMemberFilter = ref('all')
    const contentView = ref('grid')
    const searchQuery = ref('')
    const spaceSearchQuery = ref('')
    const memberSearchQuery = ref('')
    const tagInput = ref('')
    const userSearchQuery = ref('')

    // 邀请成员相关数据
    const searchLoading = ref(false)
    const inviteLoading = ref(false)
    const searchResults = ref([])
    const selectedUsers = ref([])
    const inviteRole = ref('member')
    const inviteMessage = ref('')

    // 日历相关数据
    const currentDate = ref(new Date())
    const selectedDate = ref(null)
    const calendarEvents = ref([])

    // 新讨论表单数据
    const newDiscussion = reactive({
      title: '',
      content: '',
      tags: [],
      type: 'question'
    })

    // 团队数据
    const team = reactive({
      id: 1,
      name: 'AI创新实验室',
      description: '专注于人工智能技术研发与应用创新，探索AI在各个领域的可能性',
      avatar: 'https://api.dicebear.com/7.x/shapes/svg?seed=ai-lab',
      themeColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      isPublic: true,
      isMember: true,
      isStarred: true,
      isVerified: true,
      isActive: true,
      location: '北京·中关村',
      createdAt: '2023-06-15T10:00:00Z',
      membersCount: 24,
      contentCount: 156,
      discussionsCount: 34,
      tags: ['AI', '机器学习', '深度学习', '自然语言处理']
    })

    // 团队空间列表
    const mySpaces = ref([
      {
        id: 1,
        name: 'AI创新实验室',
        avatar: 'https://api.dicebear.com/7.x/shapes/svg?seed=ai-lab',
        membersCount: 24,
        isActive: true
      },
      {
        id: 2,
        name: '前端开发团队',
        avatar: 'https://api.dicebear.com/7.x/shapes/svg?seed=frontend',
        membersCount: 18,
        isActive: false
      },
      {
        id: 3,
        name: '产品设计组',
        avatar: 'https://api.dicebear.com/7.x/shapes/svg?seed=design',
        membersCount: 12,
        isActive: true
      }
    ])

    const otherSpaces = ref([
      {
        id: 4,
        name: '数据科学团队',
        avatar: 'https://api.dicebear.com/7.x/shapes/svg?seed=data',
        membersCount: 30,
        isActive: true
      },
      {
        id: 5,
        name: '移动开发组',
        avatar: 'https://api.dicebear.com/7.x/shapes/svg?seed=mobile',
        membersCount: 15,
        isActive: false
      }
    ])

    // 在线成员
    const onlineMembers = ref([
      {
        id: 1,
        name: '张三',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=zhang',
        status: '正在编辑文档',
        isOnline: true
      },
      {
        id: 2,
        name: '李四',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=li',
        status: '在线',
        isOnline: true
      },
      {
        id: 3,
        name: '王五',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=wang',
        status: '正在开会',
        isOnline: true
      }
    ])

    // 所有成员列表（用于成员管理）
    const allMembers = ref([
      {
        id: 1,
        name: '张三',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=zhang',
        role: 'admin',
        joinedAt: '2023-06-15T10:00:00Z',
        contributionScore: 950,
        isOnline: true
      },
      {
        id: 2,
        name: '李四',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=li',
        role: 'member',
        joinedAt: '2023-07-01T10:00:00Z',
        contributionScore: 780,
        isOnline: true
      },
      {
        id: 3,
        name: '王五',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=wang',
        role: 'member',
        joinedAt: '2023-08-15T10:00:00Z',
        contributionScore: 650,
        isOnline: true
      },
      {
        id: 4,
        name: '赵六',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=zhao',
        role: 'member',
        joinedAt: '2023-09-01T10:00:00Z',
        contributionScore: 420,
        isOnline: false
      },
      {
        id: 5,
        name: '钱七',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=qian',
        role: 'member',
        joinedAt: '2023-10-01T10:00:00Z',
        contributionScore: 320,
        isOnline: false
      }
    ])

    // 贡献度排行
    const topContributors = computed(() => {
      return [...allMembers.value]
        .sort((a, b) => b.contributionScore - a.contributionScore)
        .slice(0, 5)
    })

    const maxContribution = computed(() => {
      return Math.max(...allMembers.value.map(m => m.contributionScore))
    })

    // 最近活动
    const recentActivities = ref([
      {
        id: 1,
        icon: 'fas fa-file-alt',
        user: { id: 1, name: '张三' },
        action: '发布了新文章《深度学习在图像识别中的应用》',
        time: '2024-01-20T14:30:00Z'
      },
      {
        id: 2,
        icon: 'fas fa-user-plus',
        user: { id: 2, name: '李四' },
        action: '加入了团队',
        time: '2024-01-20T10:15:00Z'
      },
      {
        id: 3,
        icon: 'fas fa-comments',
        user: { id: 3, name: '王五' },
        action: '在讨论中回复了《AI技术发展趋势》',
        time: '2024-01-19T16:45:00Z'
      }
    ])

    // 标签页配置（移除概览，专注核心功能）
    const tabs = ref([
      { key: 'content', label: '内容', icon: 'fas fa-file-alt', count: 156 },
      { key: 'discussions', label: '讨论', icon: 'fas fa-comments', count: 34 },
      { key: 'members', label: '成员', icon: 'fas fa-users', count: 24 },
      { key: 'analytics', label: '数据洞察', icon: 'fas fa-chart-bar' }
    ])

    // 概览数据
    const teamDynamics = ref([
      { id: 1, text: '张三发布了新文章', time: '2024-01-20T14:30:00Z' },
      { id: 2, text: '团队讨论活跃度提升', time: '2024-01-20T12:15:00Z' },
      { id: 3, text: '新成员加入团队', time: '2024-01-20T10:30:00Z' }
    ])

    const popularContent = ref([
      {
        id: 1,
        type: 'article',
        title: '深度学习最佳实践',
        views: 1250,
        likes: 89,
        description: '详细介绍深度学习在实际项目中的最佳实践方法和技巧',
        author: { id: 1, name: '张三', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=zhang' },
        thumbnail: null,
        tags: ['深度学习', 'AI', '最佳实践'],
        comments: 23,
        updatedAt: '2024-01-20T14:30:00Z'
      },
      {
        id: 2,
        type: 'tool',
        title: 'AI模型训练工具',
        views: 890,
        likes: 67,
        description: '高效的AI模型训练工具，支持多种深度学习框架',
        author: { id: 2, name: '李四', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=li' },
        thumbnail: null,
        tags: ['工具', 'AI', '训练'],
        comments: 15,
        updatedAt: '2024-01-19T16:45:00Z'
      },
      {
        id: 3,
        type: 'design',
        title: 'UI设计规范',
        views: 567,
        likes: 45,
        description: '团队UI设计规范文档，包含组件库和设计原则',
        author: { id: 3, name: '王五', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=wang' },
        thumbnail: null,
        tags: ['设计', 'UI', '规范'],
        comments: 8,
        updatedAt: '2024-01-18T11:20:00Z'
      }
    ])

    // 内容过滤器
    const contentFilters = ref([
      { key: 'all', label: '全部内容', icon: 'fas fa-file-alt', count: 156 },
      { key: 'article', label: '文章', icon: 'fas fa-newspaper', count: 89 },
      { key: 'tool', label: '工具', icon: 'fas fa-tools', count: 34 },
      { key: 'design', label: '设计', icon: 'fas fa-palette', count: 23 },
      { key: 'video', label: '视频', icon: 'fas fa-video', count: 10 }
    ])

    // 成员过滤器
    const memberFilters = ref([
      { key: 'all', label: '全部成员', icon: 'fas fa-users', count: 24 },
      { key: 'online', label: '在线成员', icon: 'fas fa-circle', count: 8 },
      { key: 'admin', label: '管理员', icon: 'fas fa-user-shield', count: 3 },
      { key: 'recent', label: '新成员', icon: 'fas fa-user-plus', count: 5 }
    ])

    const teamAchievements = ref([
      {
        id: 1,
        icon: 'fas fa-trophy',
        title: '创新团队',
        description: '发布了10+创新项目'
      },
      {
        id: 2,
        icon: 'fas fa-star',
        title: '知识分享',
        description: '团队文章获得1000+点赞'
      },
      {
        id: 3,
        icon: 'fas fa-users',
        title: '团队协作',
        description: '成员协作效率提升50%'
      }
    ])

    // 讨论相关数据
    const discussionFilters = ref([
      { key: 'all', label: '全部讨论', icon: 'fas fa-comments', count: 34 },
      { key: 'hot', label: '热门讨论', icon: 'fas fa-fire', count: 8 },
      { key: 'recent', label: '最新讨论', icon: 'fas fa-clock', count: 12 },
      { key: 'my', label: '我的讨论', icon: 'fas fa-user', count: 5 }
    ])

    const discussions = ref([
      {
        id: 1,
        title: 'AI技术发展趋势讨论',
        preview: '大家对未来AI技术的发展有什么看法？特别是在自然语言处理和计算机视觉方面...',
        author: { id: 1, name: '张三', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=zhang' },
        createdAt: '2024-01-20T10:00:00Z',
        status: 'active',
        tags: ['AI', '技术趋势', '讨论'],
        repliesCount: 15,
        viewsCount: 234,
        likesCount: 28,
        lastReply: {
          author: { id: 3, name: '王五' },
          time: '2024-01-20T16:30:00Z'
        }
      },
      {
        id: 2,
        title: '团队协作工具推荐',
        preview: '想了解大家都在使用哪些协作工具，有什么好的推荐吗？',
        author: { id: 2, name: '李四', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=li' },
        createdAt: '2024-01-19T14:00:00Z',
        status: 'resolved',
        tags: ['工具', '协作'],
        repliesCount: 8,
        viewsCount: 156,
        likesCount: 12,
        lastReply: {
          author: { id: 1, name: '张三' },
          time: '2024-01-19T18:00:00Z'
        }
      }
    ])

    // 计算属性
    const isAdmin = computed(() => {
      return team.isMember && true // 简化的管理员判断逻辑
    })

    const canCreate = computed(() => {
      return activeTab.value !== 'overview'
    })

    const filteredMySpaces = computed(() => {
      if (!spaceSearchQuery.value) return mySpaces.value
      return mySpaces.value.filter(space =>
        space.name.toLowerCase().includes(spaceSearchQuery.value.toLowerCase())
      )
    })

    const filteredOtherSpaces = computed(() => {
      if (!spaceSearchQuery.value) return otherSpaces.value
      return otherSpaces.value.filter(space =>
        space.name.toLowerCase().includes(spaceSearchQuery.value.toLowerCase())
      )
    })

    const hasMoreSpaces = computed(() => {
      return otherSpaces.value.length > 5
    })

    const filteredMembers = computed(() => {
      if (!memberSearchQuery.value) return allMembers.value
      return allMembers.value.filter(member =>
        member.name.toLowerCase().includes(memberSearchQuery.value.toLowerCase()) ||
        member.email.toLowerCase().includes(memberSearchQuery.value.toLowerCase())
      )
    })

    const filteredDiscussions = computed(() => {
      let filtered = discussions.value

      switch (activeDiscussionFilter.value) {
        case 'hot':
          filtered = discussions.value.filter(d => d.likesCount > 20)
          break
        case 'recent':
          filtered = discussions.value.filter(d => {
            const daysDiff = (new Date() - new Date(d.createdAt)) / (1000 * 60 * 60 * 24)
            return daysDiff <= 7
          })
          break
        case 'my':
          filtered = discussions.value.filter(d => d.author.id === 1) // 假设当前用户ID为1
          break
        default:
          filtered = discussions.value
      }

      return filtered
    })

    const filteredContent = computed(() => {
      let filtered = popularContent.value

      if (activeContentFilter.value !== 'all') {
        filtered = filtered.filter(content => content.type === activeContentFilter.value)
      }

      return filtered
    })

    const filteredMembersForTab = computed(() => {
      let filtered = allMembers.value

      // 按过滤器筛选
      switch (activeMemberFilter.value) {
        case 'online':
          filtered = filtered.filter(member => member.isOnline)
          break
        case 'admin':
          filtered = filtered.filter(member => member.role === 'admin' || member.role === 'owner')
          break
        case 'recent':
          filtered = filtered.filter(member => {
            const daysDiff = (new Date() - new Date(member.joinedAt)) / (1000 * 60 * 60 * 24)
            return daysDiff <= 30
          })
          break
        default:
          filtered = filtered
      }

      // 按搜索词筛选
      if (memberSearchQuery.value) {
        filtered = filtered.filter(member =>
          member.name.toLowerCase().includes(memberSearchQuery.value.toLowerCase()) ||
          member.email.toLowerCase().includes(memberSearchQuery.value.toLowerCase())
        )
      }

      return filtered
    })

    const adminCount = computed(() => {
      return allMembers.value.filter(member => member.role === 'admin' || member.role === 'owner').length
    })

    const newMembersThisMonth = computed(() => {
      const now = new Date()
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)
      return allMembers.value.filter(member => new Date(member.joinedAt) >= thisMonth).length
    })

    const canSubmitDiscussion = computed(() => {
      return newDiscussion.title.trim().length > 0 &&
             newDiscussion.content.trim().length > 0
    })

    // 日历计算属性
    const weekdays = ['日', '一', '二', '三', '四', '五', '六']

    const calendarDates = computed(() => {
      const year = currentDate.value.getFullYear()
      const month = currentDate.value.getMonth()

      // 获取当月第一天和最后一天
      const firstDay = new Date(year, month, 1)
      const lastDay = new Date(year, month + 1, 0)

      // 获取第一周的开始日期（可能是上个月的日期）
      const startDate = new Date(firstDay)
      startDate.setDate(startDate.getDate() - firstDay.getDay())

      // 获取最后一周的结束日期（可能是下个月的日期）
      const endDate = new Date(lastDay)
      endDate.setDate(endDate.getDate() + (6 - lastDay.getDay()))

      const dates = []
      const current = new Date(startDate)
      const today = new Date()

      while (current <= endDate) {
        const date = new Date(current)
        const events = getEventsForDate(date)

        dates.push({
          key: date.toISOString().split('T')[0],
          date: date,
          day: date.getDate(),
          isCurrentMonth: date.getMonth() === month,
          isToday: date.toDateString() === today.toDateString(),
          isSelected: selectedDate.value && date.toDateString() === selectedDate.value.toDateString(),
          events: events
        })

        current.setDate(current.getDate() + 1)
      }

      return dates
    })

    // 方法
    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }

    const formatTime = (dateString) => {
      const now = new Date()
      const time = new Date(dateString)
      const diff = now - time

      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)

      if (days > 0) return `${days}天前`
      if (hours > 0) return `${hours}小时前`
      if (minutes > 0) return `${minutes}分钟前`
      return '刚刚'
    }

    const getContentIcon = (type) => {
      const icons = {
        article: 'fas fa-file-alt',
        tool: 'fas fa-tools',
        design: 'fas fa-palette',
        video: 'fas fa-video'
      }
      return icons[type] || 'fas fa-file'
    }

    const getRankClass = (index) => {
      if (index === 0) return 'gold'
      if (index === 1) return 'silver'
      if (index === 2) return 'bronze'
      return 'normal'
    }

    const getDiscussionStatusLabel = (status) => {
      const labels = {
        active: '进行中',
        resolved: '已解决',
        closed: '已关闭'
      }
      return labels[status] || '未知'
    }

    // 事件处理
    const goBack = () => {
      router.go(-1)
    }

    const toggleStar = () => {
      team.isStarred = !team.isStarred
      toastStore.success(team.isStarred ? '已收藏团队' : '已取消收藏')
    }

    const shareTeam = () => {
      if (navigator.share) {
        navigator.share({
          title: `${team.name} - 团队空间`,
          url: window.location.href
        })
      } else {
        navigator.clipboard.writeText(window.location.href)
        toastStore.success('链接已复制到剪贴板')
      }
    }

    const toggleDropdown = () => {
      showDropdown.value = !showDropdown.value
    }

    const toggleSpaceSelector = () => {
      showSpaceSelector.value = !showSpaceSelector.value
    }

    const switchSpace = (space) => {
      if (space.id !== team.id) {
        router.push(`/team-space/${space.id}`)
      }
      showSpaceSelector.value = false
    }

    const joinSpace = (space) => {
      toastStore.info(`申请加入 ${space.name}`)
    }

    const showAllSpaces = () => {
      toastStore.info('显示所有团队空间功能开发中...')
    }

    const createNewSpace = () => {
      router.push('/team-space/create')
    }

    // 修复用户跳转路径
    const viewMemberProfile = (member) => {
      if (member && member.id) {
        router.push(`/user/${member.id}`)
      }
    }

    const startDirectChat = (member) => {
      toastStore.info(`与 ${member.name} 的私聊功能开发中...`)
    }

    const joinTeam = async () => {
      joinLoading.value = true
      try {
        await new Promise(resolve => setTimeout(resolve, 1000))
        team.isMember = true
        team.membersCount++
        toastStore.success('成功加入团队')
      } catch (error) {
        toastStore.error('加入团队失败，请重试')
      } finally {
        joinLoading.value = false
      }
    }

    const setActiveTab = (tab) => {
      activeTab.value = tab
      // 默认显示内容标签页
      if (!tab) {
        activeTab.value = 'content'
      }
    }

    // 成员管理相关方法
    const openMemberManagement = () => {
      showMemberModal.value = true
    }

    const closeMemberModal = () => {
      showMemberModal.value = false
    }

    const toggleMemberActions = () => {
      showMemberActions.value = !showMemberActions.value
    }

    const canManageMember = (member) => {
      return isAdmin.value && member.role !== 'owner'
    }

    const canRemoveMember = (member) => {
      return isAdmin.value && member.role !== 'owner' && member.id !== 1 // 不能移除自己
    }

    const updateMemberRole = (member) => {
      toastStore.success(`已更新 ${member.name} 的角色为 ${member.role}`)
    }

    const removeMember = (member) => {
      if (confirm(`确定要将 ${member.name} 移出团队吗？`)) {
        const index = allMembers.value.findIndex(m => m.id === member.id)
        if (index > -1) {
          allMembers.value.splice(index, 1)
          team.membersCount--
          toastStore.success(`已将 ${member.name} 移出团队`)
        }
      }
    }

    const exportMemberList = () => {
      toastStore.info('导出成员列表功能开发中...')
    }

    const bulkManageMembers = () => {
      toastStore.info('批量管理功能开发中...')
    }

    // 讨论相关方法
    const setDiscussionFilter = (filter) => {
      activeDiscussionFilter.value = filter
    }

    const createDiscussion = () => {
      showCreateDiscussion.value = true
    }

    const closeCreateDiscussion = () => {
      showCreateDiscussion.value = false
      // 重置表单
      newDiscussion.title = ''
      newDiscussion.content = ''
      newDiscussion.tags = []
      newDiscussion.type = 'question'
      tagInput.value = ''
    }

    const addTag = () => {
      const tag = tagInput.value.trim()
      if (tag && !newDiscussion.tags.includes(tag) && newDiscussion.tags.length < 5) {
        newDiscussion.tags.push(tag)
        tagInput.value = ''
      }
    }

    const handleTagInput = (event) => {
      if (event.key === ',' || event.key === 'Enter') {
        event.preventDefault()
        addTag()
      }
    }

    const removeTag = (tag) => {
      const index = newDiscussion.tags.indexOf(tag)
      if (index > -1) {
        newDiscussion.tags.splice(index, 1)
      }
    }

    const submitDiscussion = async () => {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 添加到讨论列表
        const discussion = {
          id: Date.now(),
          title: newDiscussion.title,
          preview: newDiscussion.content.substring(0, 100) + '...',
          author: { id: 1, name: '张三', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=zhang' },
          createdAt: new Date().toISOString(),
          status: 'active',
          tags: [...newDiscussion.tags],
          repliesCount: 0,
          viewsCount: 1,
          likesCount: 0,
          lastReply: null
        }

        discussions.value.unshift(discussion)
        team.discussionsCount++

        toastStore.success('讨论发起成功！')
        closeCreateDiscussion()
      } catch (error) {
        toastStore.error('发起讨论失败，请重试')
      }
    }

    const openDiscussion = (discussion) => {
      toastStore.info(`打开讨论: ${discussion.title}`)
    }

    const showDiscussions = () => {
      setActiveTab('discussions')
    }

    // 贡献度相关方法
    const showContributionDetails = () => {
      toastStore.info('贡献度详情功能开发中...')
    }

    // 内容相关方法
    const setContentFilter = (filter) => {
      activeContentFilter.value = filter
    }

    const getContentTypeLabel = (type) => {
      const labels = {
        article: '文章',
        tool: '工具',
        design: '设计',
        video: '视频'
      }
      return labels[type] || '其他'
    }

    const openContent = (content) => {
      toastStore.info(`打开内容: ${content.title}`)
    }

    // 成员相关方法
    const setMemberFilter = (filter) => {
      activeMemberFilter.value = filter
    }

    const getRoleLabel = (role) => {
      const labels = {
        owner: '所有者',
        admin: '管理员',
        member: '成员'
      }
      return labels[role] || '成员'
    }

    const editMemberRole = (member) => {
      toastStore.info(`编辑 ${member.name} 的角色`)
    }

    // 邀请成员相关方法
    const openInviteModal = () => {
      showInviteModal.value = true
      userSearchQuery.value = ''
      searchResults.value = []
      selectedUsers.value = []
      inviteRole.value = 'member'
      inviteMessage.value = ''
    }

    const closeInviteModal = () => {
      showInviteModal.value = false
      userSearchQuery.value = ''
      searchResults.value = []
      selectedUsers.value = []
      inviteRole.value = 'member'
      inviteMessage.value = ''
    }

    const searchUsers = async () => {
      if (!userSearchQuery.value.trim()) {
        searchResults.value = []
        return
      }

      searchLoading.value = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))

        // 模拟搜索结果
        const mockUsers = [
          {
            id: 10,
            name: '李明',
            email: '<EMAIL>',
            avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=liming',
            title: '前端工程师',
            isOnline: true
          },
          {
            id: 11,
            name: '王芳',
            email: '<EMAIL>',
            avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=wangfang',
            title: 'UI设计师',
            isOnline: false
          },
          {
            id: 12,
            name: '陈强',
            email: '<EMAIL>',
            avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=chenqiang',
            title: '产品经理',
            isOnline: true
          }
        ]

        // 过滤已经是团队成员的用户
        const existingMemberIds = allMembers.value.map(m => m.id)
        searchResults.value = mockUsers
          .filter(user =>
            !existingMemberIds.includes(user.id) &&
            (user.name.toLowerCase().includes(userSearchQuery.value.toLowerCase()) ||
             user.email.toLowerCase().includes(userSearchQuery.value.toLowerCase()))
          )
      } catch (error) {
        toastStore.error('搜索用户失败')
      } finally {
        searchLoading.value = false
      }
    }

    const selectUser = (user) => {
      if (!selectedUsers.value.find(u => u.id === user.id)) {
        selectedUsers.value.push(user)
      }
      userSearchQuery.value = ''
      searchResults.value = []
    }

    const removeSelectedUser = (user) => {
      const index = selectedUsers.value.findIndex(u => u.id === user.id)
      if (index > -1) {
        selectedUsers.value.splice(index, 1)
      }
    }

    const sendInvitations = async () => {
      if (selectedUsers.value.length === 0) return

      inviteLoading.value = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1500))

        toastStore.success(`已向 ${selectedUsers.value.length} 位用户发送邀请`)
        closeInviteModal()
      } catch (error) {
        toastStore.error('发送邀请失败，请重试')
      } finally {
        inviteLoading.value = false
      }
    }

    // 日历相关方法
    const openCalendar = () => {
      showCalendarModal.value = true
      loadCalendarEvents()
    }

    const closeCalendar = () => {
      showCalendarModal.value = false
    }

    const loadCalendarEvents = () => {
      // 模拟加载日历事件
      calendarEvents.value = [
        {
          id: 1,
          title: '团队周会',
          date: '2024-01-22',
          time: '10:00',
          type: 'meeting',
          participants: ['张三', '李四']
        },
        {
          id: 2,
          title: '项目评审',
          date: '2024-01-23',
          time: '14:00',
          type: 'review',
          participants: ['王五', '赵六']
        },
        {
          id: 3,
          title: '技术分享',
          date: '2024-01-25',
          time: '16:00',
          type: 'sharing',
          participants: ['张三', '钱七']
        }
      ]
    }

    const selectDate = (date) => {
      selectedDate.value = date
    }

    const getEventsForDate = (date) => {
      const dateStr = date.toISOString().split('T')[0]
      return calendarEvents.value.filter(event => event.date === dateStr)
    }

    const formatCalendarMonth = (date) => {
      return date.toLocaleDateString('zh-CN', { year: 'numeric', month: 'long' })
    }

    const formatSelectedDate = (date) => {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      })
    }

    const previousMonth = () => {
      const newDate = new Date(currentDate.value)
      newDate.setMonth(newDate.getMonth() - 1)
      currentDate.value = newDate
    }

    const nextMonth = () => {
      const newDate = new Date(currentDate.value)
      newDate.setMonth(newDate.getMonth() + 1)
      currentDate.value = newDate
    }

    const getEventIcon = (type) => {
      const icons = {
        meeting: 'fas fa-users',
        review: 'fas fa-clipboard-check',
        sharing: 'fas fa-share-alt',
        deadline: 'fas fa-clock',
        milestone: 'fas fa-flag'
      }
      return icons[type] || 'fas fa-calendar'
    }

    const createEvent = () => {
      toastStore.info('创建事项功能开发中...')
    }

    // 成员焦点滑动
    const scrollToMembers = () => {
      setActiveTab('members')
      // 等待DOM更新后滚动
      nextTick(() => {
        const membersSection = document.querySelector('.tab-content')
        if (membersSection) {
          membersSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          })
        }
      })
    }

    // 简化的事件处理方法
    const showContent = () => setActiveTab('content')
    const inviteMembers = openInviteModal
    const startChat = () => toastStore.info('团队聊天功能开发中...')
    const openTool = (tool) => {
      if (tool === 'calendar') {
        openCalendar()
      } else {
        toastStore.info(`${tool} 工具功能开发中...`)
      }
    }
    const createContent = () => toastStore.info('创建内容功能开发中...')
    const editTeam = () => toastStore.info('编辑团队功能开发中...')
    const teamSettings = () => toastStore.info('团队设置功能开发中...')

    return {
      // 响应式数据
      joinLoading,
      showDropdown,
      showSpaceSelector,
      showMemberModal,
      showMemberActions,
      showCreateDiscussion,
      showInviteModal,
      showCalendarModal,
      activeTab,
      activeDiscussionFilter,
      activeContentFilter,
      activeMemberFilter,
      contentView,
      searchQuery,
      spaceSearchQuery,
      memberSearchQuery,
      tagInput,
      userSearchQuery,
      searchLoading,
      inviteLoading,
      searchResults,
      selectedUsers,
      inviteRole,
      inviteMessage,
      currentDate,
      selectedDate,
      calendarEvents,
      newDiscussion,
      team,
      mySpaces,
      otherSpaces,
      onlineMembers,
      allMembers,
      topContributors,
      maxContribution,
      recentActivities,
      tabs,
      teamDynamics,
      popularContent,
      teamAchievements,
      discussionFilters,
      discussions,
      contentFilters,
      memberFilters,

      // 计算属性
      isAdmin,
      canCreate,
      filteredMySpaces,
      filteredOtherSpaces,
      hasMoreSpaces,
      filteredMembers,
      filteredDiscussions,
      filteredContent,
      filteredMembersForTab,
      adminCount,
      newMembersThisMonth,
      canSubmitDiscussion,
      weekdays,
      calendarDates,

      // 方法
      formatDate,
      formatTime,
      getContentIcon,
      getRankClass,
      getDiscussionStatusLabel,
      goBack,
      toggleStar,
      shareTeam,
      toggleDropdown,
      toggleSpaceSelector,
      switchSpace,
      joinSpace,
      showAllSpaces,
      createNewSpace,
      viewMemberProfile,
      startDirectChat,
      joinTeam,
      setActiveTab,
      openMemberManagement,
      closeMemberModal,
      toggleMemberActions,
      canManageMember,
      canRemoveMember,
      updateMemberRole,
      removeMember,
      exportMemberList,
      bulkManageMembers,
      setDiscussionFilter,
      createDiscussion,
      closeCreateDiscussion,
      addTag,
      handleTagInput,
      removeTag,
      submitDiscussion,
      openDiscussion,
      showDiscussions,
      showContributionDetails,
      setContentFilter,
      getContentTypeLabel,
      openContent,
      setMemberFilter,
      getRoleLabel,
      editMemberRole,
      openInviteModal,
      closeInviteModal,
      searchUsers,
      selectUser,
      removeSelectedUser,
      sendInvitations,
      openCalendar,
      closeCalendar,
      loadCalendarEvents,
      selectDate,
      getEventsForDate,
      formatCalendarMonth,
      formatSelectedDate,
      previousMonth,
      nextMonth,
      getEventIcon,
      createEvent,
      scrollToMembers,
      showContent,
      inviteMembers,
      startChat,
      openTool,
      createContent,
      editTeam,
      teamSettings
    }
  }
}
</script>

<style scoped>
/* 全局样式 */
.team-space-detail {
  background: #f8f9fa;
  min-height: 100vh;
}

.container {
  max-width: 1600px; /* 与个人空间保持一致 */
  margin: 0 auto;
  padding: 0 24px; /* 优化容器内边距 */
}

/* 顶部导航栏 */
.team-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #e9ecef;
  position: sticky;
  top: 0;
  z-index: 100;
  padding: 20px 0; /* 增加导航栏高度 */
}

.header-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 24px; /* 增加间距 */
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  color: #495057;
  padding: 10px 18px; /* 增加按钮内边距 */
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: #e9ecef;
  transform: translateX(-2px);
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 12px; /* 增加面包屑间距 */
  font-size: 14px;
  color: #6c757d;
}

/* 空间选择器 */
.space-selector {
  position: relative;
}

.current-space {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #495057;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  padding: 6px 12px; /* 增加按钮内边距 */
  border-radius: 6px;
  transition: all 0.3s ease;
}

.current-space:hover {
  background: #f8f9fa;
}

.space-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-width: 320px;
  max-width: 400px;
  z-index: 1000;
  margin-top: 8px;
  overflow: hidden;
}

.space-search {
  position: relative;
  padding: 20px; /* 增加搜索区域内边距 */
  border-bottom: 1px solid #f1f3f4;
}

.space-search i {
  position: absolute;
  left: 32px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 14px;
}

.space-search input {
  width: 100%;
  padding: 10px 12px 10px 36px; /* 增加输入框内边距 */
  border: 1px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  background: #f8f9fa;
}

.space-search input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
}

.space-list {
  max-height: 400px;
  overflow-y: auto;
}

.space-section {
  padding: 16px 0; /* 增加区块内边距 */
}

.space-section h4 {
  font-size: 12px;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0 0 12px 0; /* 增加标题下边距 */
  padding: 0 20px;
}

.space-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px; /* 增加空间项内边距 */
  cursor: pointer;
  transition: background 0.3s ease;
}

.space-item:hover {
  background: #f8f9fa;
}

.space-item.active {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.space-avatar {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  object-fit: cover;
}

.space-info {
  flex: 1;
}

.space-name {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 2px;
}

.space-meta {
  font-size: 12px;
  color: #6c757d;
}

.space-status {
  color: #4CAF50;
  font-size: 8px;
}

.space-actions {
  display: flex;
  gap: 4px;
}

.join-btn {
  width: 24px;
  height: 24px;
  background: #667eea;
  border: none;
  border-radius: 4px;
  color: white;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.join-btn:hover {
  background: #5a6fd8;
  transform: scale(1.1);
}

.show-more-spaces {
  width: 100%;
  padding: 16px 20px; /* 增加按钮内边距 */
  background: none;
  border: none;
  color: #667eea;
  font-size: 13px;
  cursor: pointer;
  transition: background 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.show-more-spaces:hover {
  background: #f8f9fa;
}

.space-actions-footer {
  padding: 16px 20px; /* 增加底部区域内边距 */
  border-top: 1px solid #f1f3f4;
  background: #f8f9fa;
}

.create-space-btn {
  width: 100%;
  padding: 12px 16px; /* 增加按钮内边距 */
  background: #667eea;
  border: none;
  border-radius: 6px;
  color: white;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.create-space-btn:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 12px; /* 增加操作按钮间距 */
}

.action-btn {
  width: 40px; /* 增加按钮尺寸 */
  height: 40px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  color: #6c757d;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.action-btn.starred {
  background: #ffc107;
  border-color: #ffc107;
  color: white;
}

.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  min-width: 140px; /* 增加下拉菜单宽度 */
  z-index: 1000;
  margin-top: 4px;
}

.dropdown-menu button {
  display: block;
  width: 100%;
  padding: 14px 18px; /* 增加菜单项内边距 */
  background: none;
  border: none;
  color: #495057;
  font-size: 14px;
  text-align: left;
  cursor: pointer;
  transition: background 0.3s ease;
}

.dropdown-menu button:hover {
  background: #f8f9fa;
}

/* 团队英雄区域 */
.team-hero {
  position: relative;
  padding: 80px 0; /* 增加英雄区域内边距 */
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  background-size: 100px 100px;
}

.hero-content {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: 48px; /* 增加英雄区域内容间距 */
  align-items: center;
  color: white;
}

.team-profile {
  display: flex;
  gap: 28px; /* 增加头像和信息间距 */
  align-items: flex-start;
}

.team-avatar {
  position: relative;
  width: 120px; /* 增加头像尺寸 */
  height: 120px;
  border-radius: 16px;
  overflow: hidden;
  border: 3px solid rgba(255, 255, 255, 0.3);
  flex-shrink: 0;
}

.team-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 42px; /* 增加占位符图标尺寸 */
  color: white;
}

.status-indicator {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 18px; /* 增加状态指示器尺寸 */
  height: 18px;
  background: #4CAF50;
  border-radius: 50%;
  border: 3px solid white;
}

.team-info {
  flex: 1;
}

.team-badges {
  display: flex;
  gap: 10px; /* 增加徽章间距 */
  margin-bottom: 16px; /* 增加徽章下边距 */
}

.badge {
  display: flex;
  align-items: center;
  gap: 6px; /* 增加徽章内容间距 */
  padding: 6px 12px; /* 增加徽章内边距 */
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge.public {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.badge.private {
  background: rgba(158, 158, 158, 0.2);
  color: #9E9E9E;
  border: 1px solid rgba(158, 158, 158, 0.3);
}

.badge.verified {
  background: rgba(33, 150, 243, 0.2);
  color: #2196F3;
  border: 1px solid rgba(33, 150, 243, 0.3);
}

.team-name {
  font-size: 36px; /* 增加团队名称字体大小 */
  font-weight: 700;
  margin: 0 0 16px 0; /* 增加下边距 */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.team-description {
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 20px 0; /* 增加下边距 */
  opacity: 0.9;
  max-width: 500px;
}

.team-meta {
  display: flex;
  gap: 24px; /* 增加元数据间距 */
  margin-bottom: 20px; /* 增加下边距 */
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px; /* 增加图标和文字间距 */
  font-size: 14px;
  opacity: 0.8;
}

.team-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* 增加标签间距 */
}

.tag {
  padding: 6px 12px; /* 增加标签内边距 */
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 团队统计和操作 */
.team-stats {
  display: flex;
  flex-direction: column;
  gap: 20px; /* 增加统计卡片间距 */
}

.stat-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 24px; /* 增加统计卡片内边距 */
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px; /* 增加最小宽度 */
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.stat-number {
  font-size: 28px; /* 增加数字字体大小 */
  font-weight: 700;
  line-height: 1;
  margin-bottom: 6px; /* 增加下边距 */
}

.stat-label {
  font-size: 13px; /* 增加标签字体大小 */
  opacity: 0.8;
  font-weight: 500;
}

.team-actions {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 增加操作按钮间距 */
  min-width: 220px; /* 增加最小宽度 */
}

.btn {
  padding: 14px 24px; /* 增加按钮内边距 */
  border-radius: 8px;
  border: none;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: white;
  color: #667eea;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover:not(:disabled) {
  background: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.btn-outline {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-outline:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.btn-sm {
  padding: 10px 18px; /* 增加小按钮内边距 */
  font-size: 13px;
}

/* 主要内容区域 */
.team-main {
  padding: 48px 0; /* 增加主要内容区域内边距 */
}

.content-layout {
  display: grid;
  grid-template-columns: 360px 1fr; /* 参考个人空间，增加侧边栏宽度 */
  gap: 48px; /* 增加布局间距 */
}

/* 侧边栏 */
.sidebar {
  display: flex;
  flex-direction: column;
  gap: 28px; /* 增加组件间距 */
}

.widget {
  background: white;
  border-radius: 12px;
  padding: 24px; /* 增加组件内边距 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
}

.widget-title {
  display: flex;
  align-items: center;
  gap: 10px; /* 增加标题图标间距 */
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 20px 0; /* 增加标题下边距 */
  color: #2c3e50;
}

.online-dot {
  color: #4CAF50;
  font-size: 8px;
}

/* 成员列表 */
.members-list {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 增加成员项间距 */
}

.member-item {
  display: flex;
  align-items: center;
  gap: 14px; /* 增加成员项内容间距 */
  padding: 12px; /* 增加成员项内边距 */
  border-radius: 8px;
  transition: background 0.3s ease;
}

.member-item:hover {
  background: #f8f9fa;
}

.member-avatar {
  width: 40px; /* 增加头像尺寸 */
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.member-avatar.clickable,
.member-name.clickable,
.activity-user.clickable,
.contributor-name.clickable,
.contributor-avatar.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.member-avatar.clickable:hover,
.contributor-avatar.clickable:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.member-name.clickable:hover,
.activity-user.clickable:hover,
.contributor-name.clickable:hover {
  color: #667eea;
  text-decoration: underline;
}

.member-info {
  flex: 1;
}

.member-name {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px; /* 增加下边距 */
}

.member-status {
  font-size: 12px;
  color: #6c757d;
}

.quick-chat-btn {
  width: 32px; /* 增加按钮尺寸 */
  height: 32px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  color: #6c757d;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-chat-btn:hover {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.view-all {
  padding: 12px; /* 增加按钮内边距 */
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  color: #667eea;
  font-size: 13px; /* 增加字体大小 */
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-all:hover {
  background: #e9ecef;
}

/* 成员贡献度 */
.contribution-list {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 增加贡献项间距 */
}

.contribution-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px; /* 增加贡献项内边距 */
  border-radius: 8px;
  transition: background 0.3s ease;
}

.contribution-item:hover {
  background: #f8f9fa;
}

.rank-badge {
  width: 28px; /* 增加排名徽章尺寸 */
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 700;
  color: white;
  flex-shrink: 0;
}

.rank-badge.gold {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #8b5a00;
}

.rank-badge.silver {
  background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
  color: #666;
}

.rank-badge.bronze {
  background: linear-gradient(135deg, #cd7f32, #daa520);
  color: #5d3a00;
}

.rank-badge.normal {
  background: #e9ecef;
  color: #6c757d;
}

.contributor-avatar {
  width: 36px; /* 增加贡献者头像尺寸 */
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
}

.contributor-info {
  flex: 1;
}

.contributor-name {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px; /* 增加下边距 */
}

.contribution-score {
  font-size: 12px;
  color: #6c757d;
}

.contribution-chart {
  width: 60px; /* 增加图表宽度 */
}

.progress-bar {
  height: 6px; /* 增加进度条高度 */
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #667eea;
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* 快速工具 */
.tools-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px; /* 增加工具按钮间距 */
}

.tool-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px; /* 增加图标和文字间距 */
  padding: 16px 12px; /* 增加工具按钮内边距 */
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  color: #495057;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tool-btn:hover {
  background: #e9ecef;
  color: #667eea;
  transform: translateY(-1px);
}

.tool-btn i {
  font-size: 18px; /* 增加工具图标尺寸 */
}

/* 活动列表 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 增加活动项间距 */
}

.activity-item {
  display: flex;
  gap: 14px; /* 增加活动项内容间距 */
  padding: 12px; /* 增加活动项内边距 */
  border-radius: 6px;
  transition: background 0.3s ease;
}

.activity-item:hover {
  background: #f8f9fa;
}

.activity-icon {
  width: 32px; /* 增加活动图标容器尺寸 */
  height: 32px;
  background: #f8f9fa;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  font-size: 14px; /* 增加图标尺寸 */
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 13px;
  color: #2c3e50;
  line-height: 1.4;
  margin-bottom: 4px; /* 增加下边距 */
}

.activity-time {
  font-size: 11px;
  color: #6c757d;
}

/* 主内容区 */
.main-content {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
}

/* 内容导航 */
.content-nav {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 0 24px; /* 增加导航内边距 */
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-tabs {
  display: flex;
  gap: 0;
}

.nav-tab {
  position: relative;
  padding: 20px 24px; /* 增加标签内边距 */
  border: none;
  background: transparent;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px; /* 增加图标和文字间距 */
  border-bottom: 3px solid transparent;
}

.nav-tab:hover {
  color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.nav-tab.active {
  color: #667eea;
  background: white;
  border-bottom-color: #667eea;
}

.tab-count {
  background: #e9ecef;
  color: #6c757d;
  padding: 3px 8px; /* 增加计数徽章内边距 */
  border-radius: 10px;
  font-size: 11px;
  font-weight: 600;
}

.nav-tab.active .tab-count {
  background: #667eea;
  color: white;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 16px; /* 增加导航操作间距 */
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: 14px; /* 调整搜索图标位置 */
  color: #6c757d;
  font-size: 14px;
}

.search-box input {
  padding: 10px 14px 10px 40px; /* 增加搜索框内边距 */
  border: 1px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  width: 220px; /* 增加搜索框宽度 */
  transition: all 0.3s ease;
}

.search-box input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 标签内容 */
.tab-content {
  padding: 28px; /* 增加标签内容内边距 */
}

.tab-panel {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 概览网格 */
.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); /* 增加最小列宽 */
  gap: 24px; /* 增加网格间距 */
}

.overview-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 24px; /* 增加卡片内边距 */
  border: 1px solid #e9ecef;
}

.overview-card h4 {
  margin: 0 0 20px 0; /* 增加标题下边距 */
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

/* 动态列表 */
.dynamic-list {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 增加动态项间距 */
}

.dynamic-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0; /* 增加动态项内边距 */
  border-bottom: 1px solid #f1f3f4;
}

.dynamic-item:last-child {
  border-bottom: none;
}

.dynamic-text {
  font-size: 14px;
  color: #495057;
}

.dynamic-time {
  font-size: 12px;
  color: #6c757d;
}

/* 内容列表 */
.content-list {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 增加内容项间距 */
}

.content-item {
  display: flex;
  align-items: center;
  gap: 14px; /* 增加内容项内容间距 */
  padding: 12px 0; /* 增加内容项内边距 */
  border-bottom: 1px solid #f1f3f4;
}

.content-item:last-child {
  border-bottom: none;
}

.content-item i {
  color: #667eea;
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.content-title {
  flex: 1;
  font-size: 14px;
  color: #495057;
}

.content-stats {
  font-size: 12px;
  color: #6c757d;
}

/* 团队成就 */
.achievements-list {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 增加成就项间距 */
}

.achievement-item {
  display: flex;
  gap: 14px; /* 增加成就项内容间距 */
  padding: 12px 0; /* 增加成就项内边距 */
  border-bottom: 1px solid #f1f3f4;
}

.achievement-item:last-child {
  border-bottom: none;
}

.achievement-icon {
  width: 32px; /* 增加成就图标容器尺寸 */
  height: 32px;
  background: #667eea;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px; /* 增加图标尺寸 */
  flex-shrink: 0;
}

.achievement-info {
  flex: 1;
}

.achievement-title {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px; /* 增加下边距 */
}

.achievement-desc {
  font-size: 12px;
  color: #6c757d;
}

/* 内容占位符 */
.content-placeholder {
  text-align: center;
  padding: 80px 20px; /* 增加占位符内边距 */
  color: #6c757d;
}

.content-placeholder i {
  font-size: 56px; /* 增加占位符图标尺寸 */
  margin-bottom: 20px; /* 增加下边距 */
  opacity: 0.5;
}

.content-placeholder h3 {
  font-size: 20px; /* 增加标题字体大小 */
  margin: 0 0 12px 0; /* 增加下边距 */
  color: #495057;
}

.content-placeholder p {
  margin: 0;
  font-size: 14px;
}

/* 成员管理模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.member-management-modal {
  width: 100%;
  max-width: 1000px; /* 增加模态框最大宽度 */
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px; /* 增加头部内边距 */
  border-bottom: 1px solid #e9ecef;
}

.modal-header h2 {
  margin: 0;
  font-size: 20px; /* 增加标题字体大小 */
  font-weight: 600;
  color: #2c3e50;
}

.close-btn {
  width: 36px; /* 增加关闭按钮尺寸 */
  height: 36px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.modal-body {
  flex: 1;
  overflow: auto;
  padding: 28px; /* 增加主体内边距 */
}

/* 成员管理工具栏 */
.member-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 28px; /* 增加下边距 */
  gap: 20px; /* 增加间距 */
}

.search-members {
  position: relative;
  flex: 1;
  max-width: 300px; /* 限制搜索框最大宽度 */
}

.search-members i {
  position: absolute;
  left: 14px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 14px;
}

.search-members input {
  width: 100%;
  padding: 12px 14px 12px 40px; /* 增加搜索框内边距 */
  border: 1px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  background: #f8f9fa;
}

.search-members input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
}

.member-actions {
  display: flex;
  gap: 12px; /* 增加操作按钮间距 */
}

/* 成员表格 */
.members-table {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-weight: 600;
  color: #495057;
  font-size: 13px;
}

.table-header > div {
  padding: 16px 20px; /* 增加表头内边距 */
  border-right: 1px solid #e9ecef;
}

.table-header > div:last-child {
  border-right: none;
}

.table-body {
  max-height: 400px; /* 限制表格主体高度 */
  overflow-y: auto;
}

.member-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  border-bottom: 1px solid #f1f3f4;
  transition: background 0.3s ease;
}

.member-row:hover {
  background: #f8f9fa;
}

.member-row:last-child {
  border-bottom: none;
}

.member-row > div {
  padding: 16px 20px; /* 增加表格单元格内边距 */
  border-right: 1px solid #f1f3f4;
  display: flex;
  align-items: center;
}

.member-row > div:last-child {
  border-right: none;
}

.col-member {
  display: flex;
  align-items: center;
  gap: 14px; /* 增加成员信息间距 */
}

.col-member .member-avatar {
  width: 40px; /* 增加表格中头像尺寸 */
  height: 40px;
}

.col-member .member-info {
  flex: 1;
}

.col-member .member-name {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px; /* 增加下边距 */
}

.col-member .member-email {
  font-size: 12px;
  color: #6c757d;
}

.col-member .member-status {
  display: flex;
  align-items: center;
  gap: 6px; /* 增加状态图标间距 */
  font-size: 11px;
  color: #6c757d;
  margin-left: 12px; /* 增加左边距 */
}

.col-member .member-status.online {
  color: #4CAF50;
}

.col-member .member-status i {
  font-size: 6px;
}

.role-select {
  padding: 6px 10px; /* 增加选择框内边距 */
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-size: 13px;
  background: white;
  cursor: pointer;
}

.role-select:focus {
  outline: none;
  border-color: #667eea;
}

.role-select:disabled {
  background: #f8f9fa;
  cursor: not-allowed;
}

.contribution-score {
  font-size: 13px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 6px; /* 增加下边距 */
}

.contribution-bar {
  width: 60px; /* 增加贡献度条宽度 */
  height: 6px; /* 增加高度 */
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.contribution-fill {
  height: 100%;
  background: #667eea;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.action-buttons {
  display: flex;
  gap: 8px; /* 增加操作按钮间距 */
}

.action-btn {
  width: 32px; /* 增加操作按钮尺寸 */
  height: 32px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.action-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.action-btn.danger:hover {
  background: #dc3545;
  border-color: #dc3545;
  color: white;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .content-layout {
    grid-template-columns: 320px 1fr;
    gap: 40px;
  }
}

@media (max-width: 1200px) {
  .container {
    padding: 0 20px;
  }

  .content-layout {
    grid-template-columns: 300px 1fr;
    gap: 32px;
  }

  .hero-content {
    gap: 32px;
  }
}

@media (max-width: 992px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }

  .content-layout {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .sidebar {
    order: 2;
  }

  .main-content {
    order: 1;
  }

  .team-profile {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .team-stats {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
  }

  .team-actions {
    align-items: center;
    max-width: 300px;
    margin: 0 auto;
  }

  .member-management-modal {
    max-width: 90vw;
  }

  .member-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .search-members {
    max-width: none;
  }

  .member-actions {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .team-header {
    padding: 16px 0;
  }

  .team-hero {
    padding: 40px 0;
  }

  .team-name {
    font-size: 28px;
  }

  .team-description {
    font-size: 14px;
  }

  .team-meta {
    flex-direction: column;
    gap: 12px;
  }

  .team-stats {
    flex-direction: column;
    align-items: center;
  }

  .widget {
    padding: 20px;
  }

  .tab-content {
    padding: 20px;
  }

  .nav-tabs {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .nav-tabs::-webkit-scrollbar {
    display: none;
  }

  .nav-tab {
    white-space: nowrap;
    padding: 16px 20px;
  }

  .search-box input {
    width: 180px;
  }

  .overview-grid {
    grid-template-columns: 1fr;
  }

  .space-dropdown {
    min-width: 280px;
  }

  .table-header,
  .member-row {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .table-header > div,
  .member-row > div {
    border-right: none;
    border-bottom: 1px solid #f1f3f4;
    padding: 12px 16px;
  }

  .table-header > div:last-child,
  .member-row > div:last-child {
    border-bottom: none;
  }

  .col-member {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .col-member .member-status {
    margin-left: 0;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 12px;
  }

  .nav-left {
    gap: 16px;
  }

  .back-button {
    padding: 8px 12px;
  }

  .team-hero {
    padding: 30px 0;
  }

  .team-avatar {
    width: 80px;
    height: 80px;
  }

  .avatar-placeholder {
    font-size: 28px;
  }

  .team-name {
    font-size: 24px;
  }

  .team-description {
    font-size: 13px;
  }

  .team-meta {
    gap: 8px;
  }

  .team-stats {
    gap: 12px;
  }

  .stat-card {
    min-width: 100px;
    padding: 16px;
  }

  .stat-number {
    font-size: 20px;
  }

  .widget {
    padding: 16px;
  }

  .tab-content {
    padding: 16px;
  }

  .nav-actions {
    flex-direction: column;
    gap: 12px;
  }

  .search-box input {
    width: 150px;
  }

  .space-dropdown {
    min-width: 260px;
    left: -100px;
  }

  .modal-overlay {
    padding: 10px;
  }

  .modal-header {
    padding: 16px 20px;
  }

  .modal-body {
    padding: 20px;
  }

  .member-toolbar {
    gap: 12px;
  }

  .member-actions {
    flex-direction: column;
  }

  .tools-grid {
    grid-template-columns: 1fr;
  }

  .contribution-list {
    gap: 12px;
  }

  .contribution-item {
    padding: 8px;
  }

  .rank-badge {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }

  .contributor-avatar {
    width: 32px;
    height: 32px;
  }

  .contribution-chart {
    width: 40px;
  }
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 18px;
  margin: 0 0 8px 0;
  color: #495057;
}

.empty-state p {
  margin: 0 0 20px 0;
  font-size: 14px;
}

/* 讨论相关样式 */
.discussions-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.discussion-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  color: #6c757d;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.filter-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.filter-count {
  font-size: 11px;
  opacity: 0.8;
}

.discussions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.discussion-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.discussion-card:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.discussion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.discussion-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

.author-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.meta-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.author-name {
  font-size: 13px;
  font-weight: 500;
  color: #2c3e50;
}

.discussion-time {
  font-size: 11px;
  color: #6c757d;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.status-badge.active {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.status-badge.resolved {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.status-badge.closed {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
}

.discussion-content {
  margin-bottom: 16px;
}

.discussion-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.discussion-preview {
  font-size: 14px;
  color: #6c757d;
  line-height: 1.5;
  margin: 0 0 12px 0;
}

.discussion-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.discussion-tag {
  padding: 2px 6px;
  background: #f8f9fa;
  color: #6c757d;
  border-radius: 4px;
  font-size: 11px;
}

.discussion-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.discussion-stats {
  display: flex;
  gap: 16px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6c757d;
}

.last-reply {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.last-reply-text {
  font-size: 12px;
  color: #6c757d;
}

.last-reply-author {
  font-weight: 500;
  color: #495057;
}

.last-reply-time {
  font-size: 11px;
  color: #6c757d;
}

/* 热门内容样式 */
.popular-content-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.popular-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.popular-item:hover {
  background: #f8f9fa;
}

.content-icon {
  width: 28px;
  height: 28px;
  background: #f8f9fa;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  font-size: 12px;
  flex-shrink: 0;
}

.content-info {
  flex: 1;
}

.content-title {
  font-size: 13px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
  line-height: 1.3;
}

.content-meta {
  display: flex;
  gap: 12px;
  font-size: 11px;
  color: #6c757d;
}

.views, .likes {
  display: flex;
  align-items: center;
  gap: 3px;
}

/* 团队成就紧凑样式 */
.achievements-compact {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.achievement-compact-item {
  display: flex;
  gap: 10px;
  align-items: center;
}

.achievement-compact-item .achievement-icon {
  width: 24px;
  height: 24px;
  background: #667eea;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
  flex-shrink: 0;
}

.achievement-text {
  flex: 1;
}

.achievement-text .achievement-title {
  font-size: 12px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 2px;
}

.achievement-text .achievement-desc {
  font-size: 10px;
  color: #6c757d;
  line-height: 1.3;
}

/* 内容管理样式 */
.content-management {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.content-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.content-view-toggle {
  display: flex;
  gap: 4px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  overflow: hidden;
}

.view-btn {
  padding: 8px 12px;
  background: #f8f9fa;
  border: none;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.view-btn.active {
  background: #667eea;
  color: white;
}

.content-container {
  display: grid;
  gap: 20px;
}

.content-container.grid {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.content-container.list {
  grid-template-columns: 1fr;
}

.content-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.content-card:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.content-thumbnail {
  position: relative;
  height: 160px;
  background: #f8f9fa;
  overflow: hidden;
}

.content-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 32px;
}

.content-type-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
}

.content-body {
  padding: 16px;
}

.content-body .content-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.content-description {
  font-size: 13px;
  color: #6c757d;
  line-height: 1.4;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.content-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.author-name {
  font-size: 12px;
  font-weight: 500;
  color: #495057;
}

.content-stats {
  display: flex;
  gap: 12px;
}

.content-stats .stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #6c757d;
}

.content-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.content-tag {
  padding: 2px 6px;
  background: #f8f9fa;
  color: #6c757d;
  border-radius: 4px;
  font-size: 10px;
}

.content-time {
  font-size: 11px;
  color: #6c757d;
}

/* 成员管理样式 */
.members-management {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.members-overview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  flex-wrap: wrap;
}

.overview-stats {
  display: flex;
  gap: 32px;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
}

.stat-item .stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-item .stat-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.members-actions {
  display: flex;
  gap: 12px;
}

.members-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.member-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.member-card:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.member-header {
  position: relative;
  text-align: center;
  margin-bottom: 16px;
}

.member-card .member-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  margin: 0 auto 12px;
}

.member-card .member-status {
  position: absolute;
  top: 45px;
  right: calc(50% - 35px);
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  color: #6c757d;
}

.member-card .member-status.online {
  color: #4CAF50;
}

.member-card .member-status i {
  font-size: 6px;
}

.member-role-badge {
  position: absolute;
  top: 0;
  right: 0;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
}

.member-role-badge.owner {
  background: #ffc107;
  color: #8b5a00;
}

.member-role-badge.admin {
  background: #667eea;
  color: white;
}

.member-role-badge.member {
  background: #e9ecef;
  color: #6c757d;
}

.member-card .member-info {
  text-align: center;
  margin-bottom: 16px;
}

.member-card .member-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.member-card .member-email {
  font-size: 12px;
  color: #6c757d;
  margin: 0 0 12px 0;
}

.member-contribution {
  margin-bottom: 12px;
}

.contribution-label {
  font-size: 11px;
  color: #6c757d;
  margin-bottom: 4px;
  text-align: center;
}

.member-card .contribution-bar {
  height: 4px;
  background: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 4px;
}

.member-card .contribution-fill {
  height: 100%;
  background: #667eea;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.member-card .contribution-score {
  font-size: 11px;
  color: #6c757d;
  text-align: center;
}

.member-meta {
  text-align: center;
  margin-bottom: 16px;
}

.join-date {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 11px;
  color: #6c757d;
}

.member-card .member-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
}

/* 数据洞察样式 */
.analytics-dashboard {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.metric-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.metric-header h3 {
  font-size: 14px;
  font-weight: 600;
  color: #6c757d;
  margin: 0;
}

.metric-header i {
  color: #667eea;
  font-size: 18px;
}

.metric-value {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 8px;
}

.metric-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.metric-trend.positive {
  color: #4CAF50;
}

.metric-trend.negative {
  color: #f44336;
}

.metric-trend.neutral {
  color: #6c757d;
}

.analytics-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.analytics-section {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 24px;
}

.analytics-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 20px 0;
}

.chart-placeholder {
  height: 200px;
  background: #f8f9fa;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6c757d;
}

.chart-placeholder i {
  font-size: 32px;
  margin-bottom: 8px;
}

.content-distribution {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.distribution-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.distribution-label {
  min-width: 60px;
  font-size: 13px;
  color: #495057;
  font-weight: 500;
}

.distribution-bar {
  flex: 1;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.distribution-fill {
  height: 100%;
  background: #667eea;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.distribution-value {
  min-width: 40px;
  font-size: 13px;
  color: #6c757d;
  font-weight: 500;
  text-align: right;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .content-container.grid {
    grid-template-columns: 1fr;
  }

  .members-grid {
    grid-template-columns: 1fr;
  }

  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .analytics-details {
    grid-template-columns: 1fr;
  }

  .members-overview {
    flex-direction: column;
    align-items: stretch;
  }

  .overview-stats {
    justify-content: space-around;
  }

  .content-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .members-toolbar {
    flex-direction: column;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .overview-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .content-thumbnail {
    height: 120px;
  }

  .member-card {
    padding: 16px;
  }
}

/* 创建讨论模态框样式 */
.create-discussion-modal {
  width: 100%;
  max-width: 600px;
}

.discussion-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.form-group input,
.form-group textarea {
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.char-count {
  font-size: 12px;
  color: #6c757d;
  text-align: right;
}

.tags-input {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 8px;
  min-height: 40px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
  transition: border-color 0.3s ease;
}

.tags-input:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #667eea;
  color: white;
  border-radius: 4px;
  font-size: 12px;
}

.tag-item button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.3s ease;
}

.tag-item button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tags-input input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 120px;
  padding: 4px;
  font-size: 14px;
}

.form-hint {
  font-size: 12px;
  color: #6c757d;
  margin-top: 4px;
}

.radio-group {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.radio-item:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.radio-item input[type="radio"] {
  margin: 0;
}

.radio-item input[type="radio"]:checked + .radio-label {
  color: #667eea;
  font-weight: 500;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #495057;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

@media (max-width: 768px) {
  .create-discussion-modal {
    max-width: 95vw;
  }

  .radio-group {
    flex-direction: column;
  }

  .form-actions {
    flex-direction: column-reverse;
  }
}

/* 邀请成员模态框样式 */
.invite-member-modal {
  width: 100%;
  max-width: 700px;
}

.invite-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.user-search-container {
  position: relative;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input-wrapper i {
  position: absolute;
  left: 12px;
  color: #6c757d;
  font-size: 14px;
}

.search-input-wrapper input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.search-input-wrapper input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-loading {
  position: absolute;
  right: 12px;
  color: #667eea;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-top: none;
  border-radius: 0 0 6px 6px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
}

.search-result-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.search-result-item:hover {
  background: #f8f9fa;
}

.result-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.result-info {
  flex: 1;
}

.result-name {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 2px;
}

.result-email {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 2px;
}

.result-meta {
  font-size: 11px;
  color: #9ca3af;
}

.result-status {
  color: #4CAF50;
  font-size: 8px;
}

.no-results {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #6c757d;
  font-size: 14px;
}

.selected-users {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 8px;
}

.selected-user-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
}

.selected-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.selected-info {
  flex: 1;
}

.selected-name {
  font-size: 13px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 2px;
}

.selected-email {
  font-size: 11px;
  color: #6c757d;
}

.remove-user-btn {
  width: 24px;
  height: 24px;
  background: #dc3545;
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
}

.remove-user-btn:hover {
  background: #c82333;
  transform: scale(1.1);
}

.role-selector {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.role-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.role-option:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.role-option input[type="radio"] {
  margin: 0;
}

.role-option input[type="radio"]:checked + .role-label {
  color: #667eea;
}

.role-label {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.role-label i {
  font-size: 18px;
  color: #6c757d;
  width: 20px;
  text-align: center;
}

.role-option input[type="radio"]:checked + .role-label i {
  color: #667eea;
}

.role-info {
  flex: 1;
}

.role-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.role-desc {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.4;
}

/* 日历模态框样式 */
.calendar-modal {
  width: 100%;
  max-width: 900px;
}

.calendar-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 8px;
}

.nav-btn {
  width: 36px;
  height: 36px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.calendar-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.calendar-grid {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.weekday-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.weekday {
  padding: 12px;
  text-align: center;
  font-size: 12px;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
}

.dates-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.date-cell {
  min-height: 80px;
  padding: 8px;
  border-right: 1px solid #f1f3f4;
  border-bottom: 1px solid #f1f3f4;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.date-cell:nth-child(7n) {
  border-right: none;
}

.date-cell:hover {
  background: #f8f9fa;
}

.date-cell.other-month {
  color: #9ca3af;
  background: #fafafa;
}

.date-cell.today {
  background: rgba(102, 126, 234, 0.1);
}

.date-cell.selected {
  background: #667eea;
  color: white;
}

.date-cell.has-events {
  background: rgba(76, 175, 80, 0.05);
}

.date-number {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.date-events {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
}

.event-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #667eea;
}

.event-dot.meeting {
  background: #667eea;
}

.event-dot.review {
  background: #28a745;
}

.event-dot.sharing {
  background: #ffc107;
}

.event-dot.deadline {
  background: #dc3545;
}

.more-events {
  font-size: 8px;
  color: #6c757d;
  margin-top: 2px;
}

.event-details {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.event-details h4 {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 16px 0;
}

.events-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.event-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border-left: 4px solid #667eea;
}

.event-item.meeting {
  border-left-color: #667eea;
}

.event-item.review {
  border-left-color: #28a745;
}

.event-item.sharing {
  border-left-color: #ffc107;
}

.event-time {
  font-size: 12px;
  font-weight: 600;
  color: #667eea;
  min-width: 50px;
}

.event-content {
  flex: 1;
}

.event-title {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.event-participants {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #6c757d;
}

.event-type-icon {
  color: #6c757d;
  font-size: 14px;
}

.no-events {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 40px;
  color: #6c757d;
  font-size: 14px;
}

.event-actions {
  display: flex;
  justify-content: center;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .invite-member-modal,
  .calendar-modal {
    max-width: 95vw;
  }

  .role-selector {
    gap: 8px;
  }

  .role-option {
    padding: 12px;
  }

  .calendar-header {
    flex-direction: column;
    gap: 12px;
  }

  .date-cell {
    min-height: 60px;
    padding: 4px;
  }

  .weekday {
    padding: 8px 4px;
    font-size: 10px;
  }

  .event-details {
    padding: 16px;
  }

  .event-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .selected-users {
    max-height: 150px;
  }

  .search-result-item {
    padding: 8px;
  }

  .result-avatar {
    width: 32px;
    height: 32px;
  }

  .date-cell {
    min-height: 50px;
    padding: 2px;
  }

  .date-number {
    font-size: 12px;
  }

  .event-dot {
    width: 4px;
    height: 4px;
  }
}
</style>
