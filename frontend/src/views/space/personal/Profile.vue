<template>
  <Layout>
    <div class="modern-profile-page">
      <!-- 个人资料头部区域 -->
      <div class="profile-header">
        <div class="header-background">
          <div class="gradient-bg"></div>
          <div class="pattern-overlay"></div>
        </div>

        <div class="container">
          <div class="profile-hero">
            <!-- 个人基础信息 -->
            <div class="profile-main">
              <div class="avatar-section">
                <div class="avatar-container" @click="triggerAvatarUpload">
                  <img v-if="userInfo.avatarUrl" :src="userInfo.avatarUrl" :alt="userInfo.displayName" class="avatar">
                  <div v-else class="avatar-placeholder">
                    <i class="fas fa-user"></i>
                  </div>
                  <div class="avatar-overlay">
                    <i class="fas fa-camera"></i>
                  </div>
                  <div class="online-indicator"></div>
                </div>
                <input
                  type="file"
                  ref="avatarInput"
                  @change="handleAvatarUpload"
                  accept="image/*"
                  style="display: none;"
                />
              </div>

              <div class="profile-info">
                <!-- 加载状态 -->
                <div v-if="userInfo.loading" class="loading-state">
                  <div class="loading-spinner"></div>
                  <p>正在加载用户信息...</p>
                </div>

                <!-- 错误状态 -->
                <div v-else-if="userInfo.error" class="error-state">
                  <i class="fas fa-exclamation-triangle"></i>
                  <p>{{ userInfo.error }}</p>
                  <button @click="loadUserProfile" class="retry-btn">重试</button>
                </div>

                <!-- 正常显示 -->
                <div v-else>
                  <div class="name-section">
                    <h1 class="display-name">{{ userInfo.displayName || '用户' }}</h1>
                    <span class="username">@{{ userInfo.username || 'user' }}</span>
                    <div class="verification-badge" v-if="userInfo.verified">
                      <i class="fas fa-check-circle"></i>
                    </div>
                  </div>

                  <!-- 个人简介编辑 -->
                  <div class="bio-section">
                    <div v-if="!editingBio" class="bio-display" @click="startEditBio">
                      <p class="bio" v-if="userInfo.bio">{{ userInfo.bio }}</p>
                      <p class="bio placeholder" v-else>点击添加个人简介...</p>
                    </div>
                    <div v-else class="bio-edit">
                      <textarea
                        v-model="bioEditValue"
                        @blur="saveBio"
                        @keydown.enter.prevent="saveBio"
                        @keydown.esc="cancelEditBio"
                        placeholder="请输入个人简介..."
                        class="bio-textarea"
                        ref="bioTextarea"
                        maxlength="200"
                      ></textarea>
                      <div class="bio-edit-actions">
                        <span class="char-count">{{ bioEditValue.length }}/200</span>
                        <button @click="saveBio" class="save-btn">保存</button>
                        <button @click="cancelEditBio" class="cancel-btn">取消</button>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="profile-meta">
                  <div class="meta-item">
                    <i class="fas fa-building"></i>
                    <span>{{ userInfo.department || '未设置部门' }}</span>
                  </div>
                  <div class="meta-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span>{{ formatJoinDate(userInfo.createdAt) }}</span>
                  </div>
                  <div class="meta-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>{{ userInfo.location || '未设置位置' }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 统计数据 -->
            <div class="stats-section">
              <div class="stats-grid">
                <div class="stat-card" @click="viewContent('published')">
                  <div class="stat-icon">
                    <i class="fas fa-file-alt"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ userStats.publishedCount || 0 }}</div>
                    <div class="stat-label">发布内容</div>
                  </div>
                </div>

                <div class="stat-card" @click="viewFollowers">
                  <div class="stat-icon">
                    <i class="fas fa-users"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ formatNumber(userStats.followersCount || 0) }}</div>
                    <div class="stat-label">粉丝</div>
                  </div>
                </div>

                <div class="stat-card" @click="viewFollowing">
                  <div class="stat-icon">
                    <i class="fas fa-user-plus"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ formatNumber(userStats.followingCount || 0) }}</div>
                    <div class="stat-label">关注</div>
                  </div>
                </div>

                <div class="stat-card">
                  <div class="stat-icon">
                    <i class="fas fa-eye"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ formatNumber(userStats.totalViews || 0) }}</div>
                    <div class="stat-label">总浏览</div>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="action-buttons">
                <button class="btn btn-primary" @click="shareProfile">
                  <i class="fas fa-share-alt"></i>
                  分享个人主页
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="profile-content">
        <div class="container">
          <div class="content-layout">
            <!-- 左侧边栏 -->
            <div class="sidebar">
              <!-- 技能标签 -->
              <div class="widget skills-widget">
                <h3 class="widget-title">
                  <i class="fas fa-tags"></i>
                  技能标签
                </h3>
                <div class="skills-container">
                  <div v-for="(skill, index) in userSkills" :key="skill.name" class="skill-tag" :class="skill.level">
                    <span class="skill-name">{{ skill.name }}</span>
                    <button @click="removeSkill(index)" class="remove-skill-btn" title="删除技能">
                      <i class="fas fa-times"></i>
                    </button>
                  </div>

                  <!-- 添加技能输入框 -->
                  <div v-if="addingSkill" class="add-skill-input">
                    <input
                      v-model="newSkillName"
                      @blur="saveNewSkill"
                      @keydown.enter.prevent="saveNewSkill"
                      @keydown.esc="cancelAddSkill"
                      placeholder="输入技能名称..."
                      class="skill-input"
                      ref="skillInput"
                      maxlength="20"
                    />
                  </div>

                  <button v-else class="add-skill-btn" @click="startAddSkill">
                    <i class="fas fa-plus"></i>
                    添加技能
                  </button>
                </div>
              </div>

              <!-- 成就徽章 -->
              <div class="widget achievements-widget">
                <h3 class="widget-title">
                  <i class="fas fa-trophy"></i>
                  成就徽章
                </h3>
                <div class="achievements-grid">
                  <div v-for="achievement in userAchievements" :key="achievement.id"
                       class="achievement-badge" :class="{ unlocked: achievement.unlocked }"
                       @click="showAchievementDetail(achievement)">
                    <div class="badge-icon">
                      <i :class="achievement.icon"></i>
                    </div>
                    <div class="badge-info">
                      <div class="badge-name">{{ achievement.name }}</div>
                      <div class="badge-desc">{{ achievement.description }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 最近活动 -->
              <div class="widget activity-widget">
                <h3 class="widget-title">
                  <i class="fas fa-clock"></i>
                  最近活动
                </h3>
                <div class="activity-timeline">
                  <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
                    <div class="activity-icon">
                      <i :class="activity.icon"></i>
                    </div>
                    <div class="activity-content">
                      <div class="activity-text">{{ activity.text }}</div>
                      <div class="activity-time">{{ formatTime(activity.time) }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 主要内容 -->
            <div class="main-content">
              <!-- 导航标签 -->
              <div class="content-nav">
                <div class="nav-tabs">
                  <button v-for="tab in tabs" :key="tab.key"
                          class="nav-tab" :class="{ active: activeTab === tab.key }"
                          @click="setActiveTab(tab.key)">
                    <i :class="tab.icon"></i>
                    <span>{{ tab.title }}</span>
                    <div class="tab-indicator"></div>
                  </button>
                </div>
              </div>

              <!-- 标签内容 -->
              <div class="tab-content">
                <!-- 我的内容 -->
                <div v-if="activeTab === 'content'" class="tab-panel">
                  <SpaceContent />
                </div>

                <!-- 学习进度 -->
                <div v-else-if="activeTab === 'learning'" class="tab-panel">
                  <CourseInfo />
                </div>

                <!-- 团队空间 -->
                <div v-else-if="activeTab === 'team'" class="tab-panel">
                  <TeamSpaceRecommendation :userId="currentUserId" />
                </div>

                <!-- 活动历史 -->
                <div v-else-if="activeTab === 'activity'" class="tab-panel">
                  <div class="activity-history">
                    <div class="activity-filters">
                      <button v-for="filter in activityFilters" :key="filter.key"
                              class="filter-btn" :class="{ active: activeFilter === filter.key }"
                              @click="setActivityFilter(filter.key)">
                        {{ filter.label }}
                      </button>
                    </div>
                    <div class="activity-list">
                      <div v-for="activity in filteredActivities" :key="activity.id" class="activity-card">
                        <div class="activity-header">
                          <div class="activity-type">
                            <i :class="activity.icon"></i>
                            <span>{{ activity.type }}</span>
                          </div>
                          <div class="activity-date">{{ formatDate(activity.date) }}</div>
                        </div>
                        <div class="activity-body">
                          <h4>{{ activity.title }}</h4>
                          <p>{{ activity.description }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import Layout from '../../../components/Layout.vue'
import SpaceContent from '../../../components/personal/SpaceContentNew.vue'
import CourseInfo from '../../../components/personal/CourseInfo.vue'
import TeamSpaceRecommendation from '../../../components/personal/TeamSpaceRecommendation.vue'
import { useToastStore } from '../../../stores/toast'
import { useUserStore } from '../../../stores/user'
import userService from '../../../services/userService'

export default {
  name: 'ModernProfile',
  components: {
    Layout,
    SpaceContent,
    CourseInfo,
    TeamSpaceRecommendation
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const toastStore = useToastStore()
    const userStore = useUserStore()

    // 响应式数据
    const activeTab = ref('content')
    const activeFilter = ref('all')
    const avatarInput = ref(null)

    // 编辑状态
    const editingBio = ref(false)
    const bioEditValue = ref('')
    const bioTextarea = ref(null)

    // 技能编辑状态
    const addingSkill = ref(false)
    const newSkillName = ref('')
    const skillInput = ref(null)

    // 用户信息
    const userInfo = reactive({
      displayName: '',
      username: '',
      bio: '',
      department: '',
      location: '',
      avatarUrl: '',
      verified: false,
      createdAt: '',
      loading: false,
      error: null
    })

    // 用户统计
    const userStats = reactive({
      publishedCount: 0,
      followersCount: 0,
      followingCount: 0,
      totalViews: 0,
      totalLikes: 0,
      totalFavorites: 0,
      loading: false,
      error: null
    })

    // 用户技能（从后台API获取）
    const userSkills = ref([])

    // 加载用户数据的方法 - 使用封装好的userService
    const loadUserProfile = async () => {
      try {
        console.log('开始加载用户资料...')

        // 设置加载状态
        userInfo.loading = true
        userStats.loading = true
        userInfo.error = null
        userStats.error = null

        // 获取当前用户ID（从路由参数或用户store中获取）
        const userId = route.params.userId || userStore.user?.id || 1

        // 使用封装好的userService调用后台Controller的getUserProfile方法
        const profileData = await userService.getUserProfile(userId)

        if (profileData) {

          // 根据空间接口定义.md规范更新用户基本信息
          if (profileData.basicInfo) {
            Object.assign(userInfo, {
              userId: profileData.basicInfo.userId,
              displayName: profileData.basicInfo.displayName || '',
              username: profileData.basicInfo.username || '',
              bio: profileData.basicInfo.bio || '',
              department: profileData.basicInfo.department || '',
              avatarUrl: profileData.basicInfo.avatarUrl || '',
              verified: profileData.basicInfo.verified || false,
              loading: false
            })

            // 更新技能标签
            if (profileData.basicInfo.tags && Array.isArray(profileData.basicInfo.tags)) {
              userSkills.value = profileData.basicInfo.tags.map(tag => ({
                name: tag,
                level: 'intermediate' // 默认级别
              }))
            }
          }

          // 根据空间接口定义.md规范更新用户统计信息
          if (profileData.achievements) {
            Object.assign(userStats, {
              publishedCount: profileData.achievements.articlesPublished || 0,
              totalViews: profileData.achievements.totalViews || 0,
              totalLikes: profileData.achievements.totalLikes || 0,
              totalFavorites: profileData.achievements.totalFavorites || 0,
              loading: false
            })
          }

          // 根据空间接口定义.md规范更新社交信息
          if (profileData.social) {
            userStats.followersCount = profileData.social.followers || 0
            userStats.followingCount = profileData.social.following || 0
          }

          console.log('用户资料加载成功:', profileData)

        } else {
          throw new Error('获取用户资料失败')
        }

        // 从用户资料中获取徽章信息
        if (profileData.achievements && profileData.achievements.badges) {
          userAchievements.value = profileData.achievements.badges.map(badge => ({
            id: Math.random(), // 生成临时ID
            name: badge.name || '',
            description: getBadgeDescription(badge.name, badge.type), // 根据徽章名称和类型生成描述
            icon: badge.icon || 'fas fa-trophy',
            type: badge.type || 'bronze',
            unlocked: true // 从后端返回的徽章都是已解锁的
          }))
        } else {
          // 如果没有徽章数据，显示空数组
          userAchievements.value = []
        }

        // 加载用户活动历史
        try {
          const activitiesData = await userService.getUserActivities(userId, { limit: 10 })
          if (activitiesData && Array.isArray(activitiesData)) {
            activityHistory.value = activitiesData.map(activity => ({
              id: activity.id || Math.random(),
              type: activity.type || '未知活动',
              icon: activity.icon || 'fas fa-circle',
              title: activity.title || '',
              description: activity.description || '',
              date: activity.date || new Date().toISOString(),
              category: activity.category || 'other'
            }))
          }
        } catch (error) {
          console.warn('加载用户活动历史失败:', error)
          // 活动历史加载失败时保持空数组
          activityHistory.value = []
        }

        // 加载最近活动（取活动历史的前5条作为最近活动）
        try {
          const recentActivitiesData = await userService.getUserActivities(userId, { limit: 5 })
          if (recentActivitiesData && Array.isArray(recentActivitiesData)) {
            recentActivities.value = recentActivitiesData.map(activity => ({
              id: activity.id || Math.random(),
              icon: activity.icon || 'fas fa-circle',
              text: activity.text || activity.description || '进行了某项活动',
              time: activity.time || activity.date || new Date().toISOString()
            }))
          }
        } catch (error) {
          console.warn('加载最近活动失败:', error)
          // 最近活动加载失败时保持空数组
          recentActivities.value = []
        }

      } catch (error) {
        console.error('加载用户资料失败:', error)

        // 设置错误状态
        userInfo.loading = false
        userInfo.error = error.message
        userStats.loading = false
        userStats.error = error.message

        toastStore.error('加载用户资料失败: ' + error.message)

        // 使用默认数据作为降级方案
        Object.assign(userInfo, {
          displayName: '用户',
          username: 'user',
          bio: '暂无个人简介',
          department: '未知部门',
          avatarUrl: 'https://api.dicebear.com/7.x/avataaars/svg?seed=default',
          verified: false,
          loading: false
        })

        Object.assign(userStats, {
          publishedCount: 0,
          followersCount: 0,
          followingCount: 0,
          totalViews: 0,
          totalLikes: 0,
          totalFavorites: 0,
          loading: false
        })
      }
    }

    // 成就徽章
    const userAchievements = ref([])

    // 最近活动
    const recentActivities = ref([])

    // 标签配置
    const tabs = ref([
      {
        key: 'content',
        title: '我的内容',
        icon: 'fas fa-file-alt'
      },
      {
        key: 'learning',
        title: '学习进度',
        icon: 'fas fa-graduation-cap'
      },
      {
        key: 'team',
        title: '团队空间',
        icon: 'fas fa-users'
      },
      {
        key: 'activity',
        title: '活动历史',
        icon: 'fas fa-history'
      }
    ])

    // 活动过滤器
    const activityFilters = ref([
      { key: 'all', label: '全部' },
      { key: 'content', label: '内容' },
      { key: 'social', label: '社交' },
      { key: 'learning', label: '学习' },
      { key: 'team', label: '团队' }
    ])

    // 活动历史数据
    const activityHistory = ref([])

    // 计算属性
    const currentUserId = computed(() => {
      return route.params.userId || userStore.user?.id || 1
    })

    const filteredActivities = computed(() => {
      if (activeFilter.value === 'all') {
        return activityHistory.value
      }
      return activityHistory.value.filter(activity => activity.category === activeFilter.value)
    })

    // 方法
    const setActiveTab = (tabKey) => {
      activeTab.value = tabKey
    }

    const setActivityFilter = (filterKey) => {
      activeFilter.value = filterKey
    }

    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }

    const formatJoinDate = (dateString) => {
      const date = new Date(dateString)
      return `${date.getFullYear()}年${date.getMonth() + 1}月加入`
    }

    const formatTime = (timeString) => {
      const now = new Date()
      const time = new Date(timeString)
      const diff = now - time

      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)

      if (days > 0) return `${days}天前`
      if (hours > 0) return `${hours}小时前`
      if (minutes > 0) return `${minutes}分钟前`
      return '刚刚'
    }

    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }

    // 事件处理
    const triggerAvatarUpload = () => {
      avatarInput.value?.click()
    }

    const handleAvatarUpload = async (event) => {
      const file = event.target.files[0]
      if (file) {
        try {
          // 验证文件类型和大小
          if (!file.type.startsWith('image/')) {
            toastStore.error('请选择图片文件')
            return
          }
          if (file.size > 5 * 1024 * 1024) { // 5MB
            toastStore.error('图片大小不能超过5MB')
            return
          }

          const formData = new FormData()
          formData.append('avatar', file)

          const userId = route.params.userId || userStore.user?.id || 1
          const result = await userService.uploadAvatar(userId, formData)

          if (result && result.avatarUrl) {
            userInfo.avatarUrl = result.avatarUrl
            toastStore.success('头像上传成功')
          } else {
            throw new Error('上传失败')
          }
        } catch (error) {
          console.error('头像上传失败:', error)
          toastStore.error('头像上传失败: ' + error.message)
        }
      }
    }

    const shareProfile = () => {
      // 生成个人主页链接
      const userId = route.params.userId || userStore.user?.id || 1
      const profileUrl = `${window.location.origin}/space/personal/${userId}`
      const shareTitle = `${userInfo.displayName || '用户'}的个人空间`
      const shareText = `查看${userInfo.displayName || '用户'}在AI Portal的个人空间，了解TA的技能和成就！`

      // 分享个人主页
      if (navigator.share) {
        navigator.share({
          title: shareTitle,
          text: shareText,
          url: profileUrl
        }).catch(err => {
          console.log('分享失败:', err)
          // 降级到复制链接
          copyProfileLink(profileUrl, shareTitle)
        })
      } else {
        // 复制链接到剪贴板
        copyProfileLink(profileUrl, shareTitle)
      }
    }

    const copyProfileLink = (url, title) => {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
          toastStore.success('个人主页链接已复制到剪贴板')
        }).catch(() => {
          // 降级方案
          fallbackCopyTextToClipboard(url)
        })
      } else {
        fallbackCopyTextToClipboard(url)
      }
    }

    const fallbackCopyTextToClipboard = (text) => {
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      try {
        document.execCommand('copy')
        toastStore.success('个人主页链接已复制到剪贴板')
      } catch (err) {
        toastStore.error('复制失败，请手动复制链接')
      }
      document.body.removeChild(textArea)
    }

    const viewContent = (type) => {
      setActiveTab('content')
      // 可以传递过滤参数
    }

    const viewFollowers = () => {
      toastStore.info('粉丝列表功能开发中...')
    }

    const viewFollowing = () => {
      toastStore.info('关注列表功能开发中...')
    }

    // 个人简介编辑方法
    const startEditBio = () => {
      editingBio.value = true
      bioEditValue.value = userInfo.bio || ''
      nextTick(() => {
        if (bioTextarea.value) {
          bioTextarea.value.focus()
        }
      })
    }

    const saveBio = async () => {
      if (bioEditValue.value.trim() === userInfo.bio) {
        cancelEditBio()
        return
      }

      try {
        const userId = route.params.userId || userStore.user?.id || 1
        await userService.updateUserProfile(userId, {
          bio: bioEditValue.value.trim()
        })

        userInfo.bio = bioEditValue.value.trim()
        editingBio.value = false
        toastStore.success('个人简介更新成功')
      } catch (error) {
        console.error('更新个人简介失败:', error)
        toastStore.error('更新个人简介失败: ' + error.message)
      }
    }

    const cancelEditBio = () => {
      editingBio.value = false
      bioEditValue.value = ''
    }

    // 技能管理方法
    const startAddSkill = () => {
      addingSkill.value = true
      newSkillName.value = ''
      nextTick(() => {
        if (skillInput.value) {
          skillInput.value.focus()
        }
      })
    }

    const saveNewSkill = async () => {
      const skillName = newSkillName.value.trim()
      if (!skillName) {
        cancelAddSkill()
        return
      }

      // 检查是否已存在
      if (userSkills.value.some(skill => skill.name === skillName)) {
        toastStore.warning('该技能已存在')
        cancelAddSkill()
        return
      }

      try {
        const userId = route.params.userId || userStore.user?.id || 1
        const newTags = [...userSkills.value.map(skill => skill.name), skillName]

        await userService.updateUserProfile(userId, {
          tags: newTags
        })

        userSkills.value.push({
          name: skillName,
          level: 'intermediate'
        })

        addingSkill.value = false
        newSkillName.value = ''
        toastStore.success('技能添加成功')
      } catch (error) {
        console.error('添加技能失败:', error)
        toastStore.error('添加技能失败: ' + error.message)
      }
    }

    const cancelAddSkill = () => {
      addingSkill.value = false
      newSkillName.value = ''
    }

    const removeSkill = async (index) => {
      try {
        const userId = route.params.userId || userStore.user?.id || 1
        const newTags = userSkills.value.filter((_, i) => i !== index).map(skill => skill.name)

        await userService.updateUserProfile(userId, {
          tags: newTags
        })

        userSkills.value.splice(index, 1)
        toastStore.success('技能删除成功')
      } catch (error) {
        console.error('删除技能失败:', error)
        toastStore.error('删除技能失败: ' + error.message)
      }
    }

    const showAchievementDetail = (achievement) => {
      toastStore.info(`成就：${achievement.name} - ${achievement.description}`)
    }

    // 根据徽章名称和类型生成描述
    const getBadgeDescription = (name, type) => {
      const descriptions = {
        '新手作者': '发布了第一篇文章',
        '活跃作者': '发布了5篇文章',
        '高产作者': '发布了20篇文章',
        '专业作者': '发布了50篇文章',
        '人气新星': '总阅读量达到1000',
        '人气作者': '总阅读量达到10000',
        '影响力作者': '总阅读量达到50000',
        '超级影响力': '总阅读量达到100000',
        '受欢迎作者': '总点赞数达到100',
        '点赞达人': '总点赞数达到500',
        '点赞之王': '总点赞数达到2000',
        '收藏新手': '总收藏数达到50',
        '收藏达人': '总收藏数达到200',
        '收藏之星': '总收藏数达到1000',
        '全能作者': '在多个维度表现优秀',
        '社区之星': '在社区中具有重要影响力'
      }
      return descriptions[name] || '获得了特殊成就'
    }

    // 生命周期
    onMounted(() => {
      // 加载用户数据
      console.log('个人空间页面已加载')
      loadUserProfile()
    })

    return {
      // 响应式数据
      activeTab,
      activeFilter,
      avatarInput,
      userInfo,
      userStats,
      userSkills,
      userAchievements,
      recentActivities,
      tabs,
      activityFilters,
      filteredActivities,
      currentUserId,

      // 编辑状态
      editingBio,
      bioEditValue,
      bioTextarea,
      addingSkill,
      newSkillName,
      skillInput,

      // 方法
      setActiveTab,
      setActivityFilter,
      formatNumber,
      formatJoinDate,
      formatTime,
      formatDate,
      triggerAvatarUpload,
      handleAvatarUpload,
      shareProfile,
      viewContent,
      viewFollowers,
      viewFollowing,
      showAchievementDetail,
      loadUserProfile,

      // 编辑方法
      startEditBio,
      saveBio,
      cancelEditBio,
      startAddSkill,
      saveNewSkill,
      cancelAddSkill,
      removeSkill
    }
  }
}
</script>

<style scoped>
/* 现代化个人空间样式 */
.modern-profile-page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* 个人资料头部区域 */
.profile-header {
  position: relative;
  padding: 60px 0 40px;
  overflow: hidden;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.gradient-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(102, 126, 234, 0.9) 0%,
    rgba(118, 75, 162, 0.9) 100%);
}

.pattern-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  background-size: 100px 100px;
}

.profile-hero {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 40px;
  align-items: start;
}

/* 个人基础信息 */
.profile-main {
  display: flex;
  gap: 30px;
  align-items: flex-start;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  color: #ff6b6b;
}

.error-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.7;
}

.retry-btn {
  margin-top: 16px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

/* 个人简介编辑 */
.bio-section {
  margin: 16px 0;
}

.bio-display {
  cursor: pointer;
  transition: all 0.3s ease;
}

.bio-display:hover {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 8px;
  margin: -8px;
}

.bio-edit {
  margin: 8px 0;
}

.bio-textarea {
  width: 100%;
  min-height: 80px;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  font-family: inherit;
}

.bio-textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.bio-textarea::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.bio-edit-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.char-count {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.save-btn, .cancel-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.save-btn {
  background: #667eea;
  color: white;
  margin-left: 8px;
}

.save-btn:hover {
  background: #5a67d8;
}

.cancel-btn {
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.cancel-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.avatar-section {
  flex-shrink: 0;
}

.avatar-container {
  position: relative;
  width: 120px;
  height: 120px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.avatar-container:hover {
  transform: scale(1.05);
}

.avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: white;
  border: 4px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  color: white;
  font-size: 24px;
}

.avatar-container:hover .avatar-overlay {
  opacity: 1;
}

.online-indicator {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  background: #4CAF50;
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.profile-info {
  flex: 1;
  color: white;
}

.name-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.display-name {
  font-size: 32px;
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.username {
  font-size: 18px;
  opacity: 0.8;
  font-weight: 500;
}

.verification-badge {
  color: #4CAF50;
  font-size: 20px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.bio {
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 20px 0;
  opacity: 0.9;
  max-width: 500px;
}

.bio.placeholder {
  opacity: 0.6;
  cursor: pointer;
  font-style: italic;
}

.bio.placeholder:hover {
  opacity: 0.8;
}

.profile-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  opacity: 0.8;
}

.meta-item i {
  width: 16px;
  text-align: center;
}

/* 统计数据 */
.stats-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  min-width: 280px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.stat-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
  font-weight: 500;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 12px 20px;
  border-radius: 12px;
  border: none;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
}

.btn-primary {
  background: white;
  color: #667eea;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.btn-outline {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-outline:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

/* 主要内容区域 */
.profile-content {
  background: #f8f9fa;
  min-height: 60vh;
  padding: 40px 0;
}

.content-layout {
  display: grid;
  grid-template-columns: 320px 1fr;
  gap: 40px;
}

/* 左侧边栏 */
.sidebar {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.widget {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
}

.widget-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 20px 0;
  color: #2c3e50;
}

.widget-title i {
  color: #667eea;
}

/* 技能标签 */
.skills-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.skill-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  transition: transform 0.2s ease;
  position: relative;
}

.remove-skill-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 2px;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  transition: all 0.2s ease;
}

.remove-skill-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
}

.skill-tag:hover {
  transform: scale(1.05);
}

.skill-tag.expert {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.skill-tag.advanced {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.skill-tag.intermediate {
  background: linear-gradient(135deg, #FF9800, #F57C00);
  color: white;
}

.skill-tag.beginner {
  background: linear-gradient(135deg, #9E9E9E, #757575);
  color: white;
}

.skill-level {
  font-size: 10px;
  opacity: 0.8;
}

.add-skill-btn {
  padding: 8px 12px;
  border: 2px dashed #dee2e6;
  border-radius: 20px;
  background: transparent;
  color: #6c757d;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-skill-btn:hover {
  border-color: #667eea;
  color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

/* 技能输入框 */
.add-skill-input {
  display: flex;
  align-items: center;
}

.skill-input {
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  font-size: 12px;
  min-width: 120px;
  outline: none;
  transition: all 0.3s ease;
}

.skill-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.skill-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

/* 成就徽章 */
.achievements-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.achievement-badge {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.achievement-badge.unlocked {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-color: rgba(102, 126, 234, 0.2);
}

.achievement-badge:not(.unlocked) {
  background: #f8f9fa;
  opacity: 0.6;
}

.achievement-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.badge-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.achievement-badge.unlocked .badge-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.achievement-badge:not(.unlocked) .badge-icon {
  background: #e9ecef;
  color: #6c757d;
}

.badge-info {
  flex: 1;
}

.badge-name {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 2px;
  color: #2c3e50;
}

.badge-desc {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.4;
}

/* 活动时间线 */
.activity-timeline {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  flex-shrink: 0;
  margin-top: 2px;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 14px;
  color: #2c3e50;
  line-height: 1.4;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: #6c757d;
}

/* 主要内容区域 */
.main-content {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
}

/* 导航标签 */
.content-nav {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 0 24px;
}

.nav-tabs {
  display: flex;
  gap: 0;
}

.nav-tab {
  position: relative;
  padding: 20px 24px;
  border: none;
  background: transparent;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 3px solid transparent;
}

.nav-tab:hover {
  color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.nav-tab.active {
  color: #667eea;
  background: white;
  border-bottom-color: #667eea;
}

.nav-tab i {
  font-size: 16px;
}

.tab-indicator {
  position: absolute;
  bottom: -3px;
  left: 0;
  right: 0;
  height: 3px;
  background: #667eea;
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.nav-tab.active .tab-indicator {
  transform: scaleX(1);
}

/* 标签内容 */
.tab-content {
  padding: 32px;
}

.tab-panel {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 活动历史 */
.activity-history {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.activity-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 8px 16px;
  border: 2px solid #e9ecef;
  border-radius: 20px;
  background: white;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  border-color: #667eea;
  color: #667eea;
}

.filter-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.activity-card:hover {
  background: white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.activity-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 12px;
}

.activity-type {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #667eea;
  font-size: 14px;
  font-weight: 600;
}

.activity-date {
  color: #6c757d;
  font-size: 12px;
}

.activity-body h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.activity-body p {
  margin: 0;
  color: #6c757d;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-layout {
    grid-template-columns: 280px 1fr;
    gap: 30px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    min-width: 240px;
  }
}

@media (max-width: 992px) {
  .profile-hero {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .profile-main {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 20px;
  }

  .stats-section {
    align-self: stretch;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    min-width: auto;
  }

  .content-layout {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .sidebar {
    order: 2;
  }

  .main-content {
    order: 1;
  }
}

@media (max-width: 768px) {
  .profile-header {
    padding: 40px 0 30px;
  }

  .profile-main {
    gap: 16px;
  }

  .avatar-container {
    width: 100px;
    height: 100px;
  }

  .display-name {
    font-size: 24px;
  }

  .username {
    font-size: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .profile-content {
    padding: 24px 0;
  }

  .tab-content {
    padding: 20px;
  }

  .nav-tabs {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .nav-tabs::-webkit-scrollbar {
    display: none;
  }

  .nav-tab {
    padding: 16px 20px;
    white-space: nowrap;
  }
}

@media (max-width: 480px) {
  .profile-header {
    padding: 30px 0 20px;
  }

  .avatar-container {
    width: 80px;
    height: 80px;
  }

  .display-name {
    font-size: 20px;
  }

  .profile-meta {
    flex-direction: column;
    gap: 8px;
  }

  .widget {
    padding: 16px;
  }

  .tab-content {
    padding: 16px;
  }
}
</style>