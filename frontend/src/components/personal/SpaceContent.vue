<template>
  <div class="space-content">
    <div class="section-header">
      <h3 class="section-title">
        <i class="fas fa-folder-open"></i>
        我的内容
      </h3>
      <div class="view-controls">
        <div class="view-toggle">
          <button 
            class="view-btn"
            :class="{ active: viewMode === 'grid' }"
            @click="setViewMode('grid')"
          >
            <i class="fas fa-th"></i>
          </button>
          <button 
            class="view-btn"
            :class="{ active: viewMode === 'list' }"
            @click="setViewMode('list')"
          >
            <i class="fas fa-list"></i>
          </button>
        </div>
      </div>
    </div>
    
    <!-- 标签导航 -->
    <div class="tab-nav">
      <button 
        v-for="tab in tabs" 
        :key="tab.key"
        class="tab-btn"
        :class="{ active: activeTab === tab.key }"
        @click="setActiveTab(tab.key)"
      >
        <i :class="tab.icon"></i>
        {{ tab.label }} ({{ getTabCount(tab.key) }})
      </button>
    </div>
    
    <!-- 资源类型过滤 -->
    <div class="filter-section">
      <div class="resource-filters">
        <button 
          v-for="type in resourceTypes" 
          :key="type.key"
          class="filter-btn"
          :class="{ active: activeResourceType === type.key }"
          @click="setResourceType(type.key)"
        >
          <i :class="type.icon"></i>
          {{ type.label }}
        </button>
      </div>
      
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input 
          type="text" 
          placeholder="搜索内容..."
          v-model="searchQuery"
          @input="filterContent"
        >
      </div>
    </div>
    
    <!-- 内容列表 -->
    <div class="content-area">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>正在加载内容...</p>
      </div>

      <div v-else class="content-grid" :class="{ 'content-list': viewMode === 'list' }">
        <div
          v-for="item in paginatedContent"
          :key="item.id"
          class="content-item"
          :class="item.resourceType"
        >
          <div class="item-header">
            <div class="item-type">
              <i :class="getResourceIcon(item.resourceType)"></i>
              <span>{{ getResourceLabel(item.resourceType) }}</span>
            </div>
            <div class="item-actions">
              <button class="action-btn" @click="shareItem(item)" title="分享">
                <i class="fas fa-share"></i>
              </button>
              <button class="action-btn" @click="recommendToTeam(item)" title="推荐到团队">
                <i class="fas fa-users"></i>
              </button>
              <button 
                v-if="activeTab === 'published'"
                class="action-btn danger" 
                @click="deleteItem(item)" 
                title="删除"
              >
                <i class="fas fa-trash"></i>
              </button>
              <button 
                v-if="activeTab === 'bookmarked'"
                class="action-btn danger" 
                @click="removeBookmark(item)" 
                title="取消收藏"
              >
                <i class="fas fa-bookmark"></i>
              </button>
              <button 
                v-if="activeTab === 'liked'"
                class="action-btn danger" 
                @click="removeLike(item)" 
                title="取消点赞"
              >
                <i class="fas fa-heart"></i>
              </button>
            </div>
          </div>
          
          <div class="item-content">
            <h4 class="item-title">{{ item.title }}</h4>
            <p class="item-description">{{ item.description }}</p>
            
            <div class="item-meta">
              <div class="meta-left">
                <span v-if="activeTab !== 'published'" class="author">
                  作者：{{ item.author }}
                </span>
                <span class="date">
                  {{ getDateLabel() }}：{{ formatDate(item.date) }}
                </span>
              </div>
              
              <div class="meta-right">
                <div class="item-stats">
                  <span class="stat">
                    <i class="fas fa-eye"></i>
                    {{ item.views }}
                  </span>
                  <span class="stat">
                    <i class="fas fa-heart"></i>
                    {{ item.likes }}
                  </span>
                  <span class="stat">
                    <i class="fas fa-bookmark"></i>
                    {{ item.bookmarks }}
                  </span>
                </div>
              </div>
            </div>
            
            <div class="item-tags">
              <span v-for="tag in item.tags" :key="tag" class="tag">{{ tag }}</span>
            </div>
          </div>
          
          <div class="item-footer">
            <button class="btn btn-primary" @click="viewItem(item)">查看详情</button>
            <button v-if="activeTab === 'published'" class="btn btn-outline" @click="editItem(item)">编辑</button>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="filteredContent.length === 0" class="empty-state">
        <div class="empty-icon">
          <i :class="getEmptyIcon()"></i>
        </div>
        <h4>{{ getEmptyTitle() }}</h4>
        <p>{{ getEmptyDescription() }}</p>
      </div>

      <!-- 分页 -->
      <div v-if="filteredContent.length > 0" class="pagination-container">
        <div class="pagination-info">
          显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredContent.length) }} 条，
          共 {{ filteredContent.length }} 条记录
        </div>
        <div class="pagination">
          <button
            class="pagination-btn"
            :disabled="currentPage === 1"
            @click="goToPage(currentPage - 1)"
          >
            <i class="fas fa-chevron-left"></i>
            上一页
          </button>

          <div class="pagination-pages">
            <button
              v-for="page in visiblePages"
              :key="page"
              class="pagination-page"
              :class="{ active: page === currentPage }"
              @click="goToPage(page)"
            >
              {{ page }}
            </button>
          </div>

          <button
            class="pagination-btn"
            :disabled="currentPage === totalPages"
            @click="goToPage(currentPage + 1)"
          >
            下一页
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 确认对话框 -->
    <ConfirmDialog
      :visible="confirmDialog.visible"
      :title="confirmDialog.title"
      :message="confirmDialog.message"
      :details="confirmDialog.details"
      :type="confirmDialog.type"
      :confirm-text="confirmDialog.confirmText"
      :loading="confirmDialog.loading"
      @confirm="handleConfirmAction"
      @close="closeConfirmDialog"
    />

    <!-- 推荐到团队空间模态框 -->
    <RecommendToTeamModal
      :visible="recommendModal.visible"
      :content="recommendModal.content"
      @close="closeRecommendModal"
      @recommend="handleRecommendToTeam"
    />
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '../../stores/toast'
import userService from '../../services/userService'
import { getResourceTypes, processKnowledgeTypeInList } from '../../utils/knowledgeTypeUtils'
import ConfirmDialog from './ConfirmDialog.vue'
import RecommendToTeamModal from './RecommendToTeamModal.vue'

export default {
  name: 'SpaceContent',
  components: {
    ConfirmDialog,
    RecommendToTeamModal
  },
  setup() {
    const router = useRouter()
    const toastStore = useToastStore()
    
    const activeTab = ref('published')
    const activeResourceType = ref('all')
    const viewMode = ref('grid')
    const searchQuery = ref('')

    // 分页相关状态
    const currentPage = ref(1)
    const pageSize = ref(6) // 每页显示6个项目，保持紧凑

    // 确认对话框状态
    const confirmDialog = reactive({
      visible: false,
      title: '',
      message: '',
      details: '',
      type: 'warning',
      confirmText: '确认',
      loading: false,
      action: null,
      data: null
    })

    // 推荐模态框状态
    const recommendModal = reactive({
      visible: false,
      content: null
    })
    
    const tabs = [
      { key: 'published', label: '我的知识', icon: 'fas fa-lightbulb' },
      { key: 'bookmarked', label: '我收藏的', icon: 'fas fa-bookmark' },
      { key: 'liked', label: '我喜欢的', icon: 'fas fa-heart' }
    ]
    
    // 使用工具类获取资源类型配置
    const resourceTypes = getResourceTypes()
    
    // 内容数据
    const publishedContent = ref([])
    const bookmarkedContent = ref([])
    const likedContent = ref([])

    // 加载状态
    const loading = ref(false)

    // 统计数据
    const contentCounts = ref({
      published: 0,
      bookmarked: 0,
      liked: 0
    })

    // 加载用户内容数据
    const loadUserContents = async (associationType = 'published') => {
      try {
        loading.value = true
        let response

        // 根据不同的标签页调用不同的API
        if (associationType === 'published') {
          // 获取用户知识列表
          response = await userService.getUserKnowledge(1, {
            page: currentPage.value,
            pageSize: pageSize.value,
            knowledgeTypeCode: activeResourceType.value === 'all' ? null : activeResourceType.value
          })
        } else if (associationType === 'bookmarked') {
          // 获取用户收藏列表
          response = await userService.getUserFavorites(1, {
            page: currentPage.value,
            pageSize: pageSize.value,
            knowledgeTypeCode: activeResourceType.value === 'all' ? null : activeResourceType.value
          })
        } else if (associationType === 'liked') {
          // 获取用户喜欢列表
          response = await userService.getUserLikes(1, {
            page: currentPage.value,
            pageSize: pageSize.value,
            knowledgeTypeCode: activeResourceType.value === 'all' ? null : activeResourceType.value
          })
        }

        console.log('API响应数据:', response)

        if (response) {
          let contents = []
          let total = 0

          if (associationType === 'published') {
            // 处理知识列表数据
            let rawContents = response.list || []
            // 使用工具类处理知识类型
            rawContents = processKnowledgeTypeInList(rawContents)

            contents = rawContents.map(item => ({
              id: item.id,
              title: item.title,
              description: item.description || item.summary,
              resourceType: item.knowledgeTypeCode || 'article',
              author: item.authorName || '未知作者',
              date: item.createdAt,
              views: item.viewCount || 0,
              likes: item.likeCount || 0,
              bookmarks: item.favoriteCount || 0,
              tags: item.tags || []
            }))
            total = response.total || 0
            console.log('处理后的知识数据:', { contents, total })
            publishedContent.value = contents
            contentCounts.value.published = total
          } else if (associationType === 'bookmarked') {
            // 处理收藏列表数据
            let rawContents = response.list || []
            // 使用工具类处理知识类型
            rawContents = processKnowledgeTypeInList(rawContents)

            contents = rawContents.map(item => {
              const knowledge = item.knowledge || item
              return {
                id: knowledge.id,
                title: knowledge.title,
                description: knowledge.description || knowledge.summary,
                resourceType: knowledge.knowledgeTypeCode || 'article',
                author: knowledge.authorName || '未知作者',
                date: item.createdAt || knowledge.createdAt,
                views: knowledge.viewCount || 0,
                likes: knowledge.likeCount || 0,
                bookmarks: knowledge.favoriteCount || 0,
                tags: knowledge.tags || []
              }
            })
            total = response.total || 0
            bookmarkedContent.value = contents
            contentCounts.value.bookmarked = total
          } else if (associationType === 'liked') {
            // 处理喜欢列表数据
            let rawContents = response.list || []
            // 使用工具类处理知识类型
            rawContents = processKnowledgeTypeInList(rawContents)

            contents = rawContents.map(item => {
              const knowledge = item.knowledge || item
              return {
                id: knowledge.id,
                title: knowledge.title,
                description: knowledge.description || knowledge.summary,
                resourceType: knowledge.knowledgeTypeCode || 'article',
                author: knowledge.authorName || '未知作者',
                date: item.createdAt || knowledge.createdAt,
                views: knowledge.viewCount || 0,
                likes: knowledge.likeCount || 0,
                bookmarks: knowledge.favoriteCount || 0,
                tags: knowledge.tags || []
              }
            })
            total = response.total || 0
            likedContent.value = contents
            contentCounts.value.liked = total
          }
        }
      } catch (error) {
        console.error('加载用户内容失败:', error)
        toastStore.error('加载内容失败')
      } finally {
        loading.value = false
      }
    }

    // 数据加载完成，初始化组件

    // 初始化时加载数据
    onMounted(() => {
      loadUserContents(activeTab.value)
    })

    // 监听标签页变化
    watch(activeTab, (newTab) => {
      currentPage.value = 1
      loadUserContents(newTab)
    })

    // 监听资源类型变化
    watch(activeResourceType, () => {
      currentPage.value = 1
      loadUserContents(activeTab.value)
    })

    const currentContent = computed(() => {
      switch (activeTab.value) {
        case 'published':
          return publishedContent.value
        case 'bookmarked':
          return bookmarkedContent.value
        case 'liked':
          return likedContent.value
        default:
          return []
      }
    })
    
    const filteredContent = computed(() => {
      let content = currentContent.value

      // 只按搜索关键词过滤（资源类型筛选已在API层面完成）
      if (searchQuery.value.trim()) {
        const query = searchQuery.value.toLowerCase()
        content = content.filter(item =>
          item.title.toLowerCase().includes(query) ||
          item.description.toLowerCase().includes(query) ||
          item.tags.some(tag => tag.toLowerCase().includes(query))
        )
      }

      return content
    })

    // 分页相关计算属性
    const totalPages = computed(() => {
      return Math.ceil(filteredContent.value.length / pageSize.value)
    })

    const paginatedContent = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return filteredContent.value.slice(start, end)
    })

    const visiblePages = computed(() => {
      const total = totalPages.value
      const current = currentPage.value
      const pages = []

      if (total <= 7) {
        for (let i = 1; i <= total; i++) {
          pages.push(i)
        }
      } else {
        if (current <= 4) {
          for (let i = 1; i <= 5; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(total)
        } else if (current >= total - 3) {
          pages.push(1)
          pages.push('...')
          for (let i = total - 4; i <= total; i++) {
            pages.push(i)
          }
        } else {
          pages.push(1)
          pages.push('...')
          for (let i = current - 1; i <= current + 1; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(total)
        }
      }

      return pages
    })
    
    const setActiveTab = (tab) => {
      activeTab.value = tab
      currentPage.value = 1 // 切换标签时重置到第一页
    }

    const setResourceType = (type) => {
      activeResourceType.value = type
      currentPage.value = 1 // 切换过滤时重置到第一页
    }

    const setViewMode = (mode) => {
      viewMode.value = mode
    }

    const filterContent = () => {
      currentPage.value = 1 // 搜索时重置到第一页
    }

    // 分页方法
    const goToPage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page
      }
    }
    
    const getTabCount = (tabKey) => {
      switch (tabKey) {
        case 'published':
          return publishedContent.value.length
        case 'bookmarked':
          return bookmarkedContent.value.length
        case 'liked':
          return likedContent.value.length
        default:
          return 0
      }
    }
    
    const getResourceIcon = (type) => {
      const iconMap = {
        article: 'fas fa-file-alt',
        prompt: 'fas fa-code',
        tool: 'fas fa-wrench',
        course: 'fas fa-graduation-cap'
      }
      return iconMap[type] || 'fas fa-file'
    }
    
    const getResourceLabel = (type) => {
      const labelMap = {
        article: '文章',
        prompt: 'Prompt',
        tool: '工具',
        course: '课程'
      }
      return labelMap[type] || '未知'
    }
    
    const getDateLabel = () => {
      switch (activeTab.value) {
        case 'published':
          return '发布时间'
        case 'bookmarked':
          return '收藏时间'
        case 'liked':
          return '点赞时间'
        default:
          return '时间'
      }
    }
    
    const getEmptyIcon = () => {
      switch (activeTab.value) {
        case 'published':
          return 'fas fa-paper-plane'
        case 'bookmarked':
          return 'fas fa-bookmark'
        case 'liked':
          return 'fas fa-heart'
        default:
          return 'fas fa-folder-open'
      }
    }
    
    const getEmptyTitle = () => {
      switch (activeTab.value) {
        case 'published':
          return '还没有创建任何知识'
        case 'bookmarked':
          return '还没有收藏任何内容'
        case 'liked':
          return '还没有喜欢任何内容'
        default:
          return '暂无内容'
      }
    }

    const getEmptyDescription = () => {
      switch (activeTab.value) {
        case 'published':
          return '开始创作您的第一个知识内容吧'
        case 'bookmarked':
          return '浏览并收藏您感兴趣的内容'
        case 'liked':
          return '为优质内容点赞支持作者'
        default:
          return ''
      }
    }
    
    const viewItem = (item) => {
      // 根据资源类型跳转到对应详情页
      const routeMap = {
        article: '/article',
        prompt: '/prompt',
        tool: '/tool',
        course: '/course'
      }
      const route = routeMap[item.resourceType] || '/article'
      router.push(`${route}/${item.id}`)
    }
    
    const editItem = (item) => {
      // 跳转到编辑页面
      const routeMap = {
        article: '/article-edit',
        prompt: '/prompt-edit',
        tool: '/tool-edit',
        course: '/course-edit'
      }
      const route = routeMap[item.resourceType] || '/article-edit'
      router.push(`${route}/${item.id}`)
    }
    
    const shareItem = (item) => {
      // 复制分享链接到剪贴板
      const shareUrl = `${window.location.origin}/${item.resourceType}/${item.id}`
      navigator.clipboard.writeText(shareUrl).then(() => {
        toastStore.success('分享链接已复制到剪贴板')
      }).catch(() => {
        toastStore.error('复制失败，请手动复制链接')
      })
    }

    const recommendToTeam = (item) => {
      recommendModal.content = item
      recommendModal.visible = true
    }

    const deleteItem = (item) => {
      confirmDialog.title = '确认删除'
      confirmDialog.message = `确定要删除"${item.title}"吗？`
      confirmDialog.details = '删除后将无法恢复，请谨慎操作。'
      confirmDialog.type = 'danger'
      confirmDialog.confirmText = '删除'
      confirmDialog.action = 'delete'
      confirmDialog.data = item
      confirmDialog.visible = true
    }

    const removeBookmark = (item) => {
      confirmDialog.title = '取消收藏'
      confirmDialog.message = `确定要取消收藏"${item.title}"吗？`
      confirmDialog.details = '取消收藏后，该内容将从您的收藏列表中移除。'
      confirmDialog.type = 'warning'
      confirmDialog.confirmText = '取消收藏'
      confirmDialog.action = 'removeBookmark'
      confirmDialog.data = item
      confirmDialog.visible = true
    }

    const removeLike = (item) => {
      confirmDialog.title = '取消点赞'
      confirmDialog.message = `确定要取消点赞"${item.title}"吗？`
      confirmDialog.details = '取消点赞后，该内容将从您的点赞列表中移除。'
      confirmDialog.type = 'warning'
      confirmDialog.confirmText = '取消点赞'
      confirmDialog.action = 'removeLike'
      confirmDialog.data = item
      confirmDialog.visible = true
    }
    
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    // 确认对话框处理函数
    const handleConfirmAction = async () => {
      confirmDialog.loading = true

      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        const { action, data } = confirmDialog

        switch (action) {
          case 'delete':
            // 从对应列表中删除项目
            if (activeTab.value === 'published') {
              const index = publishedContent.value.findIndex(item => item.id === data.id)
              if (index > -1) {
                publishedContent.value.splice(index, 1)
              }
            }
            toastStore.success('删除成功')
            break

          case 'removeBookmark':
            const bookmarkIndex = bookmarkedContent.value.findIndex(item => item.id === data.id)
            if (bookmarkIndex > -1) {
              bookmarkedContent.value.splice(bookmarkIndex, 1)
            }
            toastStore.success('已取消收藏')
            break

          case 'removeLike':
            const likeIndex = likedContent.value.findIndex(item => item.id === data.id)
            if (likeIndex > -1) {
              likedContent.value.splice(likeIndex, 1)
            }
            toastStore.success('已取消点赞')
            break
        }

        closeConfirmDialog()
      } catch (error) {
        toastStore.error('操作失败，请重试')
      } finally {
        confirmDialog.loading = false
      }
    }

    const closeConfirmDialog = () => {
      confirmDialog.visible = false
      confirmDialog.action = null
      confirmDialog.data = null
      confirmDialog.loading = false
    }

    // 推荐功能处理函数
    const handleRecommendToTeam = (data) => {
      // 这里可以调用API将内容推荐到选定的团队空间
      console.log('推荐到团队:', data)
    }

    const closeRecommendModal = () => {
      recommendModal.visible = false
      recommendModal.content = null
    }
    
    return {
      activeTab,
      activeResourceType,
      viewMode,
      searchQuery,
      currentPage,
      pageSize,
      tabs,
      resourceTypes,
      filteredContent,
      paginatedContent,
      totalPages,
      visiblePages,
      confirmDialog,
      recommendModal,
      loading,
      contentCounts,
      setActiveTab,
      setResourceType,
      setViewMode,
      filterContent,
      goToPage,
      getTabCount,
      getResourceIcon,
      getResourceLabel,
      getDateLabel,
      getEmptyIcon,
      getEmptyTitle,
      getEmptyDescription,
      viewItem,
      editItem,
      shareItem,
      recommendToTeam,
      deleteItem,
      removeBookmark,
      removeLike,
      formatDate,
      handleConfirmAction,
      closeConfirmDialog,
      handleRecommendToTeam,
      closeRecommendModal,
      loadUserContents
    }
  }
}
</script>

<style scoped>
.space-content {
  background: white;
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  height: fit-content;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: none;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin: 0;
}

.section-title i {
  color: #4f46e5;
  font-size: 20px;
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.view-toggle {
  display: flex;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 4px;
}

.view-btn {
  background: none;
  border: none;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 6px;
  color: #6b7280;
  transition: all 0.2s ease;
}

.view-btn.active {
  background: white;
  color: #4f46e5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tab-nav {
  display: flex;
  gap: 4px;
  margin-bottom: 20px;
  background: #f8fafc;
  border-radius: 12px;
  padding: 6px;
}

.tab-btn {
  flex: 1;
  padding: 12px 20px;
  border: none;
  background: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.tab-btn.active {
  background: white;
  color: #4f46e5;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.15);
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  gap: 20px;
}

.resource-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-btn {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.filter-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.filter-btn.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

.search-box {
  position: relative;
  min-width: 250px;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 14px;
}

.search-box input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
}

.search-box input:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.content-area {
  flex: 1;
  min-height: 400px;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.content-list {
  grid-template-columns: 1fr;
}

.content-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.content-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.item-type {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  background: #f1f5f9;
  padding: 4px 8px;
  border-radius: 6px;
}

.item-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  background: none;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.action-btn.danger:hover {
  background: #fef2f2;
  color: #ef4444;
}

.item-content {
  margin-bottom: 15px;
}

.item-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.item-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 15px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-size: 12px;
  color: #9ca3af;
}

.meta-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.author {
  font-weight: 500;
}

.item-stats {
  display: flex;
  gap: 12px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
}

.item-tags {
  display: flex;
  gap: 6px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.tag {
  background: #e0e7ff;
  color: #3730a3;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.item-footer {
  display: flex;
  gap: 10px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
  opacity: 0.3;
}

.empty-state h4 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 10px 0;
  color: #374151;
}

.empty-state p {
  font-size: 16px;
  margin: 0;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 16px 0;
  border-top: 1px solid #f3f4f6;
}

.pagination-info {
  color: #6b7280;
  font-size: 14px;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-pages {
  display: flex;
  gap: 4px;
  margin: 0 8px;
}

.pagination-page {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-page:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.pagination-page.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

@media (max-width: 1024px) {
  .filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .search-box {
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .space-content {
    padding: 20px;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .tab-nav {
    flex-direction: column;
    gap: 2px;
  }

  .content-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .item-header {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .item-actions {
    justify-content: flex-end;
  }

  .item-meta {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .item-footer {
    flex-direction: column;
  }

  .pagination-container {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .pagination {
    justify-content: center;
  }

  .pagination-pages {
    margin: 0 4px;
  }

  .pagination-page {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
