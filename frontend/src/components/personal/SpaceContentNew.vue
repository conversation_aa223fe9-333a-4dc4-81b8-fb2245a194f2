<template>
  <div class="space-content">
    <!-- 标题和操作 -->
    <div class="section-header">
      <h3 class="section-title">
        <i class="fas fa-file-alt"></i>
        {{ isPublic ? '公开内容' : '我的内容' }}
      </h3>
      <div v-if="!isPublic" class="view-controls">
        <button class="action-btn" @click="exportContent">
          <i class="fas fa-download"></i>
        </button>
        <button class="action-btn" @click="importContent">
          <i class="fas fa-upload"></i>
        </button>
      </div>
    </div>

    <!-- 标签页 -->
    <div class="tab-nav">
      <button
        v-for="tab in tabs"
        :key="tab.key"
        :class="['tab-btn', { active: activeTab === tab.key }]"
        @click="switchTab(tab.key)"
      >
        <i :class="tab.icon"></i>
        {{ tab.label }} ({{ contentCounts[tab.key] || 0 }})
      </button>
    </div>

    <!-- 过滤器 -->
    <div class="filter-section">
      <div class="resource-filters">
        <button
          v-for="type in resourceTypes"
          :key="type.key"
          :class="['filter-btn', { active: activeResourceType === type.key }]"
          @click="activeResourceType = type.key"
        >
          <i :class="type.icon"></i>
          {{ type.label }}
        </button>
      </div>
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input
          type="text"
          placeholder="搜索内容..."
          v-model="searchQuery"
        />
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <div class="content-grid" v-if="!loading">
        <div
          v-for="item in filteredContent"
          :key="item.id"
          class="content-item"
        >
          <div class="item-header">
            <div class="item-type">
              <i :class="getTypeIcon(item.resourceType)"></i>
              <span>{{ getTypeLabel(item.resourceType) }}</span>
            </div>
            <div class="item-actions">
              <button class="action-btn" @click="viewContent(item)">
                <i class="fas fa-eye"></i>
              </button>
              <button class="action-btn" @click="editContent(item)">
                <i class="fas fa-edit"></i>
              </button>
              <button class="action-btn danger" @click="deleteContent(item)">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>

          <div class="item-content">
            <h4 class="item-title">{{ item.title }}</h4>
            <p class="item-description">{{ item.description }}</p>
          </div>

          <div class="item-meta">
            <div class="meta-left">
              <span class="author">{{ item.author }}</span>
              <span>{{ formatDate(item.date) }}</span>
            </div>
            <div class="item-stats">
              <span class="stat">
                <i class="fas fa-eye"></i>
                {{ item.views }}
              </span>
              <span class="stat">
                <i class="fas fa-heart"></i>
                {{ item.likes }}
              </span>
              <span class="stat">
                <i class="fas fa-bookmark"></i>
                {{ item.bookmarks }}
              </span>
            </div>
          </div>

          <div class="item-tags" v-if="item.tags && item.tags.length">
            <span v-for="tag in item.tags" :key="tag" class="tag">{{ tag }}</span>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredContent.length === 0" class="empty-state">
        <div class="empty-icon">
          <i class="fas fa-inbox"></i>
        </div>
        <h4>暂无内容</h4>
        <p>{{ getEmptyMessage() }}</p>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="empty-state">
        <div class="empty-icon">
          <i class="fas fa-spinner fa-spin"></i>
        </div>
        <h4>加载中...</h4>
        <p>正在获取内容数据</p>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container" v-if="!loading && filteredContent.length > 0">
      <div class="pagination-info">
        显示第 {{ (currentPage - 1) * pageSize + 1 }}-{{ Math.min(currentPage * pageSize, totalItems) }} 条，共 {{ totalItems }} 条记录
      </div>
      <div class="pagination">
        <button
          class="pagination-btn"
          :disabled="currentPage === 1"
          @click="currentPage--"
        >
          <i class="fas fa-chevron-left"></i>
          上一页
        </button>
        <div class="pagination-pages">
          <button
            v-for="page in visiblePages"
            :key="page"
            :class="['pagination-page', { active: page === currentPage }]"
            @click="currentPage = page"
          >
            {{ page }}
          </button>
        </div>
        <button
          class="pagination-btn"
          :disabled="currentPage === totalPages"
          @click="currentPage++"
        >
          下一页
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '../../stores/toast'
import userService from '../../services/userService'

export default {
  name: 'SpaceContent',
  props: {
    userId: {
      type: String,
      default: null
    },
    isPublic: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const router = useRouter()
    const toastStore = useToastStore()
    
    // 响应式数据
    const activeTab = ref('published')
    const activeResourceType = ref('all')
    const searchQuery = ref('')
    const currentPage = ref(1)
    const pageSize = ref(10)
    const loading = ref(false)
    
    // 标签页配置
    const tabs = computed(() => {
      if (props.isPublic) {
        return [
          { key: 'published', label: '已发布', icon: 'fas fa-paper-plane' }
        ]
      }
      return [
        { key: 'published', label: '已发布', icon: 'fas fa-paper-plane' },
        { key: 'bookmarked', label: '已收藏', icon: 'fas fa-bookmark' },
        { key: 'liked', label: '已点赞', icon: 'fas fa-heart' }
      ]
    })
    
    const resourceTypes = [
      { key: 'all', label: '全部', icon: 'fas fa-th-large' },
      { key: 'article', label: '文章', icon: 'fas fa-file-alt' },
      { key: 'prompt', label: 'Prompt', icon: 'fas fa-code' },
      { key: 'tool', label: '工具', icon: 'fas fa-wrench' },
      { key: 'course', label: '课程', icon: 'fas fa-graduation-cap' }
    ]
    
    // 内容数据
    const publishedContent = ref([])
    const bookmarkedContent = ref([])
    const likedContent = ref([])
    
    // 统计数据
    const contentCounts = ref({
      published: 0,
      bookmarked: 0,
      liked: 0
    })
    
    // 加载用户内容数据
    const loadUserContents = async (associationType = 'published') => {
      try {
        loading.value = true
        // 设置较大的pageSize确保获取所有数据
        const targetUserId = props.userId || 1 // 如果没有指定userId，默认使用当前用户(1)
        const response = await userService.getUserContents(targetUserId, {
          associationType,
          pageSize: 100  // 设置足够大的页面大小来获取所有数据
        })

        if (response && response.list) {
          const contents = response.list.map(item => ({
            id: item.id,
            title: item.title,
            description: item.description,
            resourceType: item.knowledgeTypeCode,
            author: item.authorName,
            date: item.createdAt,
            views: item.stats.views,
            likes: item.stats.likes,
            bookmarks: item.stats.favorites,
            tags: item.tags || []
          }))

          if (associationType === 'published') {
            publishedContent.value = contents
            contentCounts.value.published = response.total
          } else if (associationType === 'favorited') {
            bookmarkedContent.value = contents
            contentCounts.value.bookmarked = response.total
          } else if (associationType === 'liked') {
            likedContent.value = contents
            contentCounts.value.liked = response.total
          }
        }
      } catch (error) {
        console.error('加载用户内容失败:', error)
        toastStore.error('加载内容失败')
      } finally {
        loading.value = false
      }
    }
    
    // 计算属性
    const currentContent = computed(() => {
      switch (activeTab.value) {
        case 'published':
          return publishedContent.value
        case 'bookmarked':
          return bookmarkedContent.value
        case 'liked':
          return likedContent.value
        default:
          return []
      }
    })
    
    const allFilteredContent = computed(() => {
      let content = currentContent.value

      // 按类型过滤
      if (activeResourceType.value !== 'all') {
        content = content.filter(item => item.resourceType === activeResourceType.value)
      }

      // 按搜索关键词过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        content = content.filter(item =>
          item.title.toLowerCase().includes(query) ||
          item.description.toLowerCase().includes(query) ||
          item.tags.some(tag => tag.toLowerCase().includes(query))
        )
      }

      return content
    })

    const totalItems = computed(() => allFilteredContent.value.length)
    const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value))

    const filteredContent = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return allFilteredContent.value.slice(start, end)
    })
    
    const visiblePages = computed(() => {
      const pages = []
      const start = Math.max(1, currentPage.value - 2)
      const end = Math.min(totalPages.value, currentPage.value + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      
      return pages
    })
    
    // 方法
    const switchTab = (tab) => {
      activeTab.value = tab
      currentPage.value = 1

      // 如果数据还没有加载，则加载对应的内容
      if (tab === 'published' && publishedContent.value.length === 0) {
        loadUserContents('published')
      } else if (tab === 'bookmarked' && bookmarkedContent.value.length === 0) {
        loadUserContents('favorited')
      } else if (tab === 'liked' && likedContent.value.length === 0) {
        loadUserContents('liked')
      }
    }

    // 监听过滤条件变化，重置页码
    watch([activeResourceType, searchQuery], () => {
      currentPage.value = 1
    })
    
    const getTypeIcon = (type) => {
      const typeMap = {
        article: 'fas fa-file-alt',
        prompt: 'fas fa-code',
        tool: 'fas fa-wrench',
        course: 'fas fa-graduation-cap',
        mcp: 'fas fa-puzzle-piece',
        agent_rules: 'fas fa-robot'
      }
      return typeMap[type] || 'fas fa-file'
    }
    
    const getTypeLabel = (type) => {
      const typeMap = {
        article: '文章',
        prompt: 'Prompt',
        tool: '工具',
        course: '课程',
        mcp: 'MCP',
        agent_rules: 'Agent规则'
      }
      return typeMap[type] || '未知'
    }
    
    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }
    
    const getEmptyMessage = () => {
      switch (activeTab.value) {
        case 'published':
          return '您还没有发布任何内容'
        case 'bookmarked':
          return '您还没有收藏任何内容'
        case 'liked':
          return '您还没有点赞任何内容'
        default:
          return '暂无内容'
      }
    }
    
    const viewContent = (item) => {
      toastStore.info(`查看内容: ${item.title}`)
    }
    
    const editContent = (item) => {
      toastStore.info(`编辑内容: ${item.title}`)
    }
    
    const deleteContent = (item) => {
      toastStore.info(`删除内容: ${item.title}`)
    }
    
    const exportContent = () => {
      toastStore.info('导出功能开发中...')
    }
    
    const importContent = () => {
      toastStore.info('导入功能开发中...')
    }
    
    // 监听器
    watch([activeResourceType, searchQuery], () => {
      currentPage.value = 1
    })
    
    // 生命周期
    onMounted(async () => {
      // 加载内容数据
      if (props.isPublic) {
        // 公开模式只加载已发布的内容
        await loadUserContents('published')
      } else {
        // 个人模式加载所有类型的内容数据
        await loadUserContents('published')
        await loadUserContents('favorited')
        await loadUserContents('liked')
      }
    })
    
    return {
      // Props
      isPublic: props.isPublic,

      // 响应式数据
      activeTab,
      activeResourceType,
      searchQuery,
      currentPage,
      pageSize,
      loading,

      // 配置
      tabs,
      resourceTypes,
      
      // 内容数据
      contentCounts,
      
      // 计算属性
      filteredContent,
      totalItems,
      totalPages,
      visiblePages,
      
      // 方法
      switchTab,
      getTypeIcon,
      getTypeLabel,
      formatDate,
      getEmptyMessage,
      viewContent,
      editContent,
      deleteContent,
      exportContent,
      importContent
    }
  }
}
</script>

<style scoped>
.space-content {
  background: white;
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  height: fit-content;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: none;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin: 0;
}

.section-title i {
  color: #4f46e5;
  font-size: 20px;
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.view-toggle {
  display: flex;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 4px;
}

.view-btn {
  background: none;
  border: none;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 6px;
  color: #6b7280;
  transition: all 0.2s ease;
}

.view-btn.active {
  background: white;
  color: #4f46e5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tab-nav {
  display: flex;
  gap: 4px;
  margin-bottom: 20px;
  background: #f8fafc;
  border-radius: 12px;
  padding: 6px;
}

.tab-btn {
  flex: 1;
  padding: 12px 20px;
  border: none;
  background: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.tab-btn.active {
  background: white;
  color: #4f46e5;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.15);
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  gap: 20px;
}

.resource-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-btn {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.filter-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.filter-btn.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

.search-box {
  position: relative;
  min-width: 250px;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 14px;
}

.search-box input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
}

.search-box input:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.content-area {
  flex: 1;
  min-height: 400px;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.content-list {
  grid-template-columns: 1fr;
}

.content-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.content-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.item-type {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  background: #f1f5f9;
  padding: 4px 8px;
  border-radius: 6px;
}

.item-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  background: none;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.action-btn.danger:hover {
  background: #fef2f2;
  color: #ef4444;
}

.item-content {
  margin-bottom: 15px;
}

.item-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.item-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 15px 0;
  height: 42px; /* 固定两行文字高度 (14px * 1.5 * 2) */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-size: 12px;
  color: #9ca3af;
}

.meta-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.author {
  font-weight: 500;
}

.item-stats {
  display: flex;
  gap: 12px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
}

.item-tags {
  display: flex;
  gap: 6px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.tag {
  background: #e0e7ff;
  color: #3730a3;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.item-footer {
  display: flex;
  gap: 10px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
  opacity: 0.3;
}

.empty-state h4 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 10px 0;
  color: #374151;
}

.empty-state p {
  font-size: 16px;
  margin: 0;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 16px 0;
  border-top: 1px solid #f3f4f6;
}

.pagination-info {
  color: #6b7280;
  font-size: 14px;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-pages {
  display: flex;
  gap: 4px;
  margin: 0 8px;
}

.pagination-page {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-page:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.pagination-page.active {
  background: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

@media (max-width: 1024px) {
  .filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .search-box {
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .space-content {
    padding: 20px;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .tab-nav {
    flex-direction: column;
    gap: 2px;
  }

  .content-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .item-header {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .item-actions {
    justify-content: flex-end;
  }

  .item-meta {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .item-footer {
    flex-direction: column;
  }

  .pagination-container {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .pagination {
    justify-content: center;
  }

  .pagination-pages {
    margin: 0 4px;
  }

  .pagination-page {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }
}
</style>
