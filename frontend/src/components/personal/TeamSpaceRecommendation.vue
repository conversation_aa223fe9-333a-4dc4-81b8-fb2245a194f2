<template>
  <div class="team-space-recommendation">
    <div class="section-header">
      <h3 class="section-title">
        <i class="fas fa-users"></i>
        团队空间
      </h3>
      <button class="view-all-btn" @click="viewAllTeams">
        查看全部
        <i class="fas fa-arrow-right"></i>
      </button>
    </div>
    
    <!-- 推荐团队空间滚动区域 -->
    <div class="recommended-teams-section">
      <h4 class="subsection-title">
        <i class="fas fa-star"></i>
        推荐加入
      </h4>
      <div class="recommended-teams-scroll">
        <div class="recommended-teams">
          <div v-for="team in recommendedTeams" :key="team.id" class="recommended-team">
            <div class="team-cover">
              <img v-if="team.cover" :src="team.cover" :alt="team.name">
              <div v-else class="cover-placeholder">
                <i class="fas fa-users"></i>
              </div>
            </div>
            <div class="team-info">
              <h5 class="team-name">{{ team.name }}</h5>
              <p class="team-description">{{ team.description }}</p>
              <div class="team-stats">
                <span class="stat">
                  <i class="fas fa-users"></i>
                  {{ team.membersCount }}人
                </span>
                <span class="stat">
                  <i class="fas fa-file-alt"></i>
                  {{ team.articlesCount }}篇
                </span>
              </div>
            </div>
            <div class="team-actions">
              <button class="btn btn-outline btn-sm" @click="viewTeam(team.id)">
                <i class="fas fa-eye"></i>
                查看
              </button>
              <button class="btn btn-primary btn-sm" @click="applyToJoin(team)">
                <i class="fas fa-plus"></i>
                申请加入
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 我的团队空间 -->
    <div class="my-teams-section">
      <h4 class="subsection-title">
        <i class="fas fa-home"></i>
        我的团队 ({{ myTeams.length }})
      </h4>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>正在加载团队数据...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-state">
        <i class="fas fa-exclamation-triangle"></i>
        <p>{{ error }}</p>
        <button @click="loadUserTeams" class="retry-btn">重试</button>
      </div>

      <!-- 团队列表 -->
      <div v-else class="my-teams-list">
        <div v-for="team in myTeams" :key="team.teamId" class="team-item">
          <div class="team-info" @click="enterTeamSpace(team)">
            <div class="team-avatar">
              <img v-if="team.avatarUrl" :src="team.avatarUrl" :alt="team.name">
              <div v-else class="avatar-placeholder">
                <i class="fas fa-users"></i>
              </div>
            </div>

            <div class="team-details">
              <div class="team-name">{{ team.name }}</div>
              <div class="team-description">{{ team.description }}</div>
              <div class="team-meta">
                <span class="member-count">
                  <i class="fas fa-users"></i>
                  {{ team.memberCount }}名成员
                </span>
                <span class="recommendation-count">
                  <i class="fas fa-star"></i>
                  {{ team.achievements?.articlesRecommended || team.recommendationCount || 0 }}个推荐
                </span>
              </div>
            </div>
          </div>

          <div class="team-actions">
            <button class="btn btn-outline btn-sm" @click="enterTeamSpace(team)">
              <i class="fas fa-arrow-right"></i>
              进入
            </button>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="myTeams.length === 0" class="empty-state">
        <div class="empty-icon">
          <i class="fas fa-users"></i>
        </div>
        <h5>还没有加入任何团队</h5>
        <p>加入团队，与同事协作创作内容</p>
        <button class="btn btn-primary" @click="createTeam">
          <i class="fas fa-plus"></i>
          创建团队
        </button>
      </div>
    </div>
    
    <!-- 最近活动 -->
    <div v-if="recentActivities.length > 0" class="activities-section">
      <h4 class="subsection-title">
        <i class="fas fa-bell"></i>
        团队动态
      </h4>
      <div class="activities-list">
        <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
          <div class="activity-icon">
            <i :class="getActivityIcon(activity.type)"></i>
          </div>
          <div class="activity-content">
            <div class="activity-text">
              <strong>{{ activity.teamName }}</strong>
              {{ activity.description }}
            </div>
            <div class="activity-time">{{ formatTime(activity.time) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '../../stores/toast'
import { useUserStore } from '../../stores/user'
import userService from '../../services/userService'

export default {
  name: 'TeamSpaceRecommendation',
  props: {
    userId: {
      type: [Number, String],
      default: null
    }
  },
  setup(props) {
    const router = useRouter()
    const toastStore = useToastStore()
    const userStore = useUserStore()

    const recommendedTeams = ref([])
    const loading = ref(false)
    const error = ref(null)

    const myTeams = ref([])

    // 加载推荐团队数据
    const loadRecommendedTeams = async () => {
      try {
        const currentUserId = props.userId || userStore.user?.id || 1
        console.log('开始加载推荐团队数据，用户ID:', currentUserId)

        const response = await userService.getRecommendedTeams(currentUserId, { limit: 6 })

        if (response && Array.isArray(response)) {
          recommendedTeams.value = response.map(team => ({
            id: team.teamId || team.id,
            name: team.name || '',
            description: team.description || '',
            cover: team.avatarUrl || null,
            membersCount: team.memberCount || 0,
            articlesCount: team.achievements?.articlesRecommended || 0,
            privacy: team.privacy || 'public',
            tags: team.tags || []
          }))
          console.log('推荐团队数据加载成功:', recommendedTeams.value)
        } else {
          recommendedTeams.value = []
        }
      } catch (err) {
        console.warn('加载推荐团队失败:', err)
        // 推荐团队加载失败时使用空数组，不影响主要功能
        recommendedTeams.value = []
      }
    }

    // 加载用户团队数据 - 符合空间接口定义.md规范
    const loadUserTeams = async () => {
      try {
        loading.value = true
        error.value = null

        // 获取用户ID，优先使用props，然后是store，最后是默认值
        const currentUserId = props.userId || userStore.user?.id || 1

        console.log('开始加载用户团队数据，用户ID:', currentUserId)

        const response = await userService.getUserTeams(currentUserId)

        if (response && Array.isArray(response)) {
          // 根据后端TeamByUserResult结构映射字段
          myTeams.value = response.map(team => ({
            teamId: team.id || team.teamId, // TeamDTO使用id字段，兼容teamId
            name: team.name || '',
            description: team.description || '',
            avatarUrl: team.avatarUrl || null,
            memberCount: team.userCount || team.memberCount || 0, // 使用userCount字段
            recommendationCount: 0, // 暂时设为0，后续可从achievements获取
            joinedAt: team.joinedAt || team.createdAt, // 兼容不同的时间字段
            // 新增字段
            privacy: team.privacy || 'public',
            tags: team.tags || [],
            isActive: team.isActive !== false,
            // 成就数据
            achievements: {
              articlesRecommended: team.achievements?.articlesRecommended || 0,
              totalViews: team.achievements?.totalViews || 0,
              totalLikes: team.achievements?.totalLikes || 0,
              totalFavorites: team.achievements?.totalFavorites || 0
            }
          }))

          console.log('用户团队数据加载成功:', myTeams.value)
        } else {
          myTeams.value = []
        }
      } catch (err) {
        console.error('加载用户团队失败:', err)
        error.value = err.message || '加载团队数据失败'
        toastStore.error('加载团队数据失败: ' + (err.message || '未知错误'))
      } finally {
        loading.value = false
      }
    }

    const recentActivities = ref([])

    // 加载团队活动数据
    const loadTeamActivities = async () => {
      try {
        const currentUserId = props.userId || userStore.user?.id || 1
        console.log('开始加载团队活动数据，用户ID:', currentUserId)

        const response = await userService.getUserTeamActivities(currentUserId, { limit: 5 })

        if (response && Array.isArray(response)) {
          recentActivities.value = response.map(activity => ({
            id: activity.id || Math.random(),
            teamName: activity.teamName || '未知团队',
            description: activity.description || activity.content || '',
            type: activity.type || 'info',
            time: activity.time || activity.createdAt || new Date().toISOString()
          }))
          console.log('团队活动数据加载成功:', recentActivities.value)
        } else {
          recentActivities.value = []
        }
      } catch (err) {
        console.warn('加载团队活动失败:', err)
        // 团队活动加载失败时使用空数组，不影响主要功能
        recentActivities.value = []
      }
    }

    const formatTime = (dateString) => {
      const date = new Date(dateString)
      const now = new Date()
      const diffTime = Math.abs(now - date)
      const diffHours = Math.ceil(diffTime / (1000 * 60 * 60))
      
      if (diffHours < 24) {
        return `${diffHours}小时前`
      } else {
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
        return `${diffDays}天前`
      }
    }
    
    const getActivityIcon = (type) => {
      const icons = {
        article: 'fas fa-file-alt',
        member: 'fas fa-user-plus',
        comment: 'fas fa-comment',
        like: 'fas fa-heart'
      }
      return icons[type] || 'fas fa-info'
    }
    
    const viewTeam = (teamId) => {
      router.push(`/team-space/${teamId}`)
    }

    const applyToJoin = (team) => {
      toastStore.success(`已申请加入 ${team.name}`)
    }

    const enterTeamSpace = (team) => {
      router.push(`/team-space/${team.teamId}`)
    }
    

    
    const createTeam = () => {
      router.push('/team-space/create')
    }
    
    const viewAllTeams = () => {
      router.push('/team-space')
    }

    // 生命周期
    onMounted(() => {
      loadUserTeams()
      loadRecommendedTeams()
      loadTeamActivities()
    })

    return {
      recommendedTeams,
      myTeams,
      recentActivities,
      loading,
      error,
      formatTime,
      getActivityIcon,
      viewTeam,
      applyToJoin,
      enterTeamSpace,
      createTeam,
      viewAllTeams,
      loadUserTeams,
      loadRecommendedTeams,
      loadTeamActivities
    }
  }
}
</script>

<style scoped>
.team-space-recommendation {
  background: white;
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.section-title i {
  color: #8b5cf6;
  font-size: 18px;
}

.view-all-btn {
  background: none;
  border: none;
  color: #4f46e5;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.view-all-btn:hover {
  color: #4338ca;
}

.recommended-teams-section {
  margin-bottom: 30px;
}

.subsection-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 15px 0;
}

.subsection-title i {
  color: #f59e0b;
  font-size: 14px;
}

.recommended-teams-scroll {
  overflow-x: auto;
  padding-bottom: 10px;
}

.recommended-teams {
  display: flex;
  gap: 15px;
  min-width: max-content;
}

.recommended-team {
  width: 280px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 15px;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.recommended-team:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.team-cover {
  width: 100%;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 12px;
}

.team-cover img,
.cover-placeholder {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-placeholder {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.recommended-team .team-info {
  margin-bottom: 15px;
}

.recommended-team .team-name {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 6px 0;
  line-height: 1.3;
}

.recommended-team .team-description {
  color: #6b7280;
  font-size: 13px;
  line-height: 1.4;
  margin: 0 0 10px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.team-stats {
  display: flex;
  gap: 12px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #9ca3af;
}

.my-teams-section {
  margin-bottom: 30px;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-top: 4px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  color: #ef4444;
}

.error-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.7;
}

.retry-btn {
  margin-top: 16px;
  padding: 8px 16px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #374151;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.my-teams-list {
  margin-bottom: 15px;
}

.team-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border: 1px solid #f3f4f6;
  border-radius: 12px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.team-item:hover {
  border-color: #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.team-item .team-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  cursor: pointer;
}

.team-avatar {
  width: 45px;
  height: 45px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.team-avatar img,
.avatar-placeholder {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 18px;
}

.team-details {
  flex: 1;
}

.team-item .team-name {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.team-description {
  color: #6b7280;
  font-size: 13px;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.team-meta {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #9ca3af;
}

.team-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.team-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.team-actions .btn {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-ghost {
  background: none;
  border: 1px solid transparent;
  color: #6b7280;
}

.btn-ghost:hover {
  background: #f9fafb;
  color: #374151;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.team-actions {
  display: flex;
  gap: 8px;
}

.empty-state {
  text-align: center;
  padding: 30px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 36px;
  margin-bottom: 15px;
  opacity: 0.3;
}

.empty-state h5 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #374151;
}

.empty-state p {
  font-size: 14px;
  margin: 0 0 15px 0;
}

.activities-section {
  border-top: 1px solid #f3f4f6;
  padding-top: 20px;
}

.activities-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activity-item {
  display: flex;
  gap: 10px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e0e7ff;
  color: #4f46e5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 14px;
  color: #374151;
  line-height: 1.4;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: #9ca3af;
}

/* 滚动条样式 */
.recommended-teams-scroll::-webkit-scrollbar {
  height: 6px;
}

.recommended-teams-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.recommended-teams-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.recommended-teams-scroll::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

@media (max-width: 768px) {
  .team-space-recommendation {
    padding: 20px;
  }

  .recommended-teams {
    gap: 12px;
  }

  .recommended-team {
    width: 250px;
  }

  .team-item {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .team-item .team-info {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .team-meta {
    justify-content: center;
  }

  .team-actions {
    justify-content: center;
  }
}
</style>
