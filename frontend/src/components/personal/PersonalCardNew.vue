<template>
  <div class="personal-card-wrapper">
    <!-- 个人卡片主体 -->
    <div class="personal-card">
      <div class="card-background">
        <div class="gradient-overlay"></div>
      </div>
    
      <div class="card-content">
        <!-- 右上角统计数据 -->
        <div class="stats-corner">
          <div class="stat-item" @click="viewPublished">
            <div class="stat-number">{{ userStats.publishedCount }}</div>
            <div class="stat-label">发布文章</div>
          </div>
          <div class="stat-divider"></div>
          <div class="stat-item" @click="viewFollowers">
            <div class="stat-number">{{ userStats.followersCount }}</div>
            <div class="stat-label">粉丝</div>
          </div>
          <div class="stat-divider"></div>
          <div class="stat-item" @click="viewFollowing">
            <div class="stat-number">{{ userStats.followingCount }}</div>
            <div class="stat-label">关注</div>
          </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
          <!-- 个人信息 -->
          <div class="personal-info">
            <div class="avatar-section">
              <div class="avatar-container">
                <img v-if="userInfo.avatarUrl" :src="userInfo.avatarUrl" :alt="userInfo.displayName" class="avatar">
                <div v-else class="avatar-placeholder">
                  <i class="fas fa-user"></i>
                </div>
                <button class="edit-avatar-btn" @click="triggerAvatarUpload">
                  <i class="fas fa-camera"></i>
                </button>
                <input
                  type="file"
                  ref="avatarInput"
                  @change="handleAvatarUpload"
                  accept="image/*"
                  style="display: none;"
                />
              </div>
            </div>
            
            <div class="info-section">
              <div class="basic-info">
                <h2 class="user-name">{{ userInfo.displayName }}</h2>
                <p class="user-department">{{ userInfo.department }}</p>
                <div class="user-bio-container">
                  <div v-if="!editingBio" class="user-bio" @click="startEditBio">
                    {{ userInfo.bio || '这个人很懒，什么都没有留下...' }}
                    <i class="fas fa-edit edit-bio-icon"></i>
                  </div>
                  <div v-else class="bio-edit-container">
                    <textarea
                      v-model="editBioText"
                      class="bio-edit-input"
                      @blur="saveBio"
                      @keydown.enter.prevent="saveBio"
                      @keydown.esc="cancelEditBio"
                      ref="bioTextarea"
                      placeholder="请输入个人描述..."
                      maxlength="200"
                    ></textarea>
                    <div class="bio-edit-actions">
                      <button class="bio-save-btn" @click="saveBio">
                        <i class="fas fa-check"></i>
                      </button>
                      <button class="bio-cancel-btn" @click="cancelEditBio">
                        <i class="fas fa-times"></i>
                      </button>
                    </div>
                  </div>
                </div>
                
                <!-- 技能标签 -->
                <div class="skills-section" v-if="userInfo.tags && userInfo.tags.length > 0">
                  <div class="skills-tags">
                    <span v-for="(tag, index) in userInfo.tags" :key="index" class="skill-tag">
                      {{ tag }}
                    </span>
                  </div>
                </div>
              </div>
              
              <div class="action-buttons">
                <button class="btn btn-outline" @click="shareProfile">
                  <i class="fas fa-share"></i>
                  分享主页
                </button>
              </div>
            </div>
          </div>

          <!-- 创作成就 - 紧凑版 -->
          <div class="achievements-sidebar">
            <div class="achievements-header">
              <h4 class="achievements-title">
                <i class="fas fa-trophy"></i>
                创作成就
              </h4>
            </div>

            <div class="achievements-compact">
              <div class="achievement-item" v-for="item in compactAchievements" :key="item.key" :title="item.label + ': ' + item.value">
                <div class="achievement-icon" :class="item.type">
                  <i :class="item.icon"></i>
                </div>
                <div class="achievement-info">
                  <div class="achievement-number">{{ item.value }}</div>
                  <div class="achievement-label">{{ item.label }}</div>
                </div>
              </div>
            </div>

            <!-- 成就徽章 -->
            <div class="achievements-expanded">
              <div class="badges-section">
                <h5 class="badges-title">成就徽章</h5>
                <div class="badges-grid">
                  <div v-for="badge in earnedBadges.slice(0, 8)" :key="badge.id" class="badge-item" :title="badge.name">
                    <div class="badge-icon" :class="badge.type">
                      <i :class="badge.icon"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 头像裁剪弹窗 -->
    <div v-if="showAvatarCropModal" class="modal-overlay" @click="closeAvatarCropModal">
      <div class="modal avatar-crop-modal" @click.stop>
        <div class="modal-header">
          <h3>裁剪头像</h3>
          <button class="modal-close" @click="closeAvatarCropModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="crop-container">
            <img ref="cropImage" :src="cropImageSrc" alt="裁剪图片" />
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-outline" @click="closeAvatarCropModal">取消</button>
          <button class="btn btn-primary" @click="confirmCrop">确认</button>
        </div>
      </div>
    </div>

    <!-- 编辑个人资料弹窗 -->
    <div v-if="showEditProfileModal" class="modal-overlay" @click="closeEditProfileModal">
      <div class="modal" @click.stop>
        <div class="modal-header">
          <h3>编辑个人资料</h3>
          <button class="modal-close" @click="closeEditProfileModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-content">
          <form @submit.prevent="saveProfile" class="edit-profile-form">
            <div class="form-group">
              <label for="name">姓名</label>
              <input type="text" id="name" v-model="editedProfile.name" class="form-input" required>
            </div>
            <div class="form-group">
              <label for="department">部门/职位</label>
              <input type="text" id="department" v-model="editedProfile.department" class="form-input">
            </div>
            <div class="form-group">
              <label for="bio">个人简介</label>
              <textarea id="bio" v-model="editedProfile.bio" class="form-textarea" rows="3"></textarea>
            </div>
            <div class="form-actions">
              <button type="button" class="btn btn-outline" @click="closeEditProfileModal">取消</button>
              <button type="submit" class="btn btn-primary">保存</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 粉丝/关注弹窗 -->
    <div v-if="showFollowModal" class="modal-overlay" @click="closeFollowModal">
      <div class="modal" @click.stop>
        <div class="modal-header">
          <h3>{{ modalTitle }}</h3>
          <button class="modal-close" @click="closeFollowModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-content">
          <div class="search-section">
            <div class="search-box">
              <i class="fas fa-search"></i>
              <input type="text" :placeholder="modalType === 'following' ? '搜索关注的人...' : '搜索粉丝...'" v-model="searchQuery">
            </div>
          </div>
          <div class="users-list">
            <div v-for="user in filteredUsers" :key="user.id" class="user-item">
              <div class="user-info" @click="viewUserProfile(user)">
                <div class="user-avatar">
                  <img v-if="user.avatar" :src="user.avatar" :alt="user.name">
                  <div v-else class="avatar-placeholder">
                    <i class="fas fa-user"></i>
                  </div>
                </div>
                <div class="user-details">
                  <div class="user-name">{{ user.name }}</div>
                  <div class="user-bio">{{ user.bio || '这个人很懒，什么都没有留下...' }}</div>
                </div>
              </div>
              <div class="user-actions">
                <button v-if="modalType === 'following'" class="btn btn-outline" @click="unfollowUser(user)">
                  取消关注
                </button>
                <button v-else class="btn btn-primary" @click="followUser(user)" :disabled="user.isFollowing">
                  {{ user.isFollowing ? '已关注' : '关注' }}
                </button>
              </div>
            </div>
          </div>
          <div v-if="filteredUsers.length === 0" class="empty-state">
            <div class="empty-icon">
              <i class="fas fa-users"></i>
            </div>
            <h4>{{ modalType === 'following' ? '还没有关注任何人' : '还没有粉丝' }}</h4>
            <p>{{ modalType === 'following' ? '去发现更多有趣的人吧！' : '分享更多内容来吸引粉丝吧！' }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useToastStore } from '../../stores/toast'
import userService from '../../services/userService'

export default {
  name: 'PersonalCard',
  props: {
    userId: {
      type: Number,
      default: 1 // 默认用户ID，实际应该从路由或认证状态获取
    }
  },
  setup(props) {
    const router = useRouter()
    const toastStore = useToastStore()

    // 加载状态
    const loading = ref(true)

    // 弹窗相关状态
    const showFollowModal = ref(false)
    const showEditProfileModal = ref(false)
    const showAvatarCropModal = ref(false)

    // 个人描述编辑状态
    const editingBio = ref(false)
    const editBioText = ref('')

    // 头像上传相关状态
    const cropImageSrc = ref('')
    const cropper = ref(null)
    const avatarInput = ref(null)
    const bioTextarea = ref(null)
    const cropImage = ref(null)
    const modalType = ref('followers') // 'followers' 或 'following'
    const searchQuery = ref('')

    const userInfo = reactive({
      displayName: '',
      username: '',
      department: '',
      bio: '',
      avatarUrl: null,
      tags: []
    })

    // 编辑个人资料相关状态
    const editedProfile = reactive({})

    const userStats = reactive({
      publishedCount: 0,
      followersCount: 0,
      followingCount: 0
    })

    // 创作成就数据
    const achievements = reactive({
      totalViews: 0,
      totalLikes: 0,
      totalFavorites: 0,
      articlesPublished: 0
    })

    // 模拟趋势数据（相比上周的变化百分比）
    const viewsTrend = ref(0)
    const likesTrend = ref(0)
    const favoritesTrend = ref(0)
    const engagementTrend = ref(0)

    // 计算互动率
    const engagementRate = computed(() => {
      if (achievements.totalViews === 0) return 0
      const totalEngagements = achievements.totalLikes + achievements.totalFavorites
      return ((totalEngagements / achievements.totalViews) * 100).toFixed(1)
    })

    // 格式化数字显示
    const formatNumber = (num) => {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + 'w'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k'
      }
      return num.toString()
    }

    // 获取徽章描述
    const getBadgeDescription = (badgeName) => {
      const descriptions = {
        '创作达人': '发布了大量优质内容',
        '社区之星': '在社区中表现突出',
        '技术专家': '在技术领域有深入贡献',
        '新手作者': '发布了第一个内容',
        '人气作者': '内容获得了很多关注',
        '设计新星': '在设计领域表现优秀',
        '创意达人': '创作了很多有创意的内容'
      }
      return descriptions[badgeName] || '获得了特殊成就'
    }

    // 加载用户数据
    const loadUserData = async () => {
      try {
        loading.value = true
        const userProfile = await userService.getUserProfile(props.userId)

        // 更新用户基本信息
        Object.assign(userInfo, userProfile.basicInfo)

        // 更新统计数据
        userStats.publishedCount = userProfile.achievements.articlesPublished
        userStats.followersCount = userProfile.social.followers
        userStats.followingCount = userProfile.social.following

        // 更新成就数据
        Object.assign(achievements, userProfile.achievements)

        // 更新徽章数据
        if (userProfile.achievements && userProfile.achievements.badges) {
          earnedBadges.value = userProfile.achievements.badges.map((badge, index) => ({
            id: index + 1,
            name: badge.name,
            icon: badge.icon,
            type: badge.type,
            description: getBadgeDescription(badge.name)
          }))
        }

        // 模拟趋势数据（实际应该从API获取）
        viewsTrend.value = Math.random() * 20 - 10 // -10% 到 +10%
        likesTrend.value = Math.random() * 20 - 10
        favoritesTrend.value = Math.random() * 20 - 10
        engagementTrend.value = Math.random() * 20 - 10

      } catch (error) {
        console.error('加载用户数据失败:', error)
        toastStore.error('加载用户数据失败')
      } finally {
        loading.value = false
      }
    }

    // 紧凑展示的成就数据
    const compactAchievements = computed(() => [
      {
        key: 'views',
        icon: 'fas fa-eye',
        type: 'views',
        value: formatNumber(achievements.totalViews),
        label: '阅读'
      },
      {
        key: 'likes',
        icon: 'fas fa-heart',
        type: 'likes',
        value: formatNumber(achievements.totalLikes),
        label: '点赞'
      },
      {
        key: 'favorites',
        icon: 'fas fa-bookmark',
        type: 'favorites',
        value: formatNumber(achievements.totalFavorites),
        label: '收藏'
      }
    ])

    // 成就徽章数据
    const earnedBadges = ref([])

    // 粉丝和关注数据
    const followersList = ref([])
    const followingList = ref([])

    // 计算属性
    const modalTitle = computed(() => {
      return modalType.value === 'followers' ? '我的粉丝' : '我的关注'
    })

    const currentUserList = computed(() => {
      return modalType.value === 'followers' ? followersList.value : followingList.value
    })

    const filteredUsers = computed(() => {
      if (!searchQuery.value.trim()) {
        return currentUserList.value
      }
      return currentUserList.value.filter(user =>
        user.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        (user.bio && user.bio.toLowerCase().includes(searchQuery.value.toLowerCase()))
      )
    })
    
    // 头像上传相关方法
    const triggerAvatarUpload = () => {
      avatarInput.value?.click()
    }

    const handleAvatarUpload = (event) => {
      const file = event.target.files?.[0]
      if (file) {
        const reader = new FileReader()
        reader.onload = (e) => {
          cropImageSrc.value = e.target?.result
          showAvatarCropModal.value = true
          nextTick(() => {
            initCropper()
          })
        }
        reader.readAsDataURL(file)
      }
    }

    const initCropper = async () => {
      if (cropImage.value) {
        // 这里可以集成Cropper.js或其他裁剪库
        // 为了简化，我们先实现基本功能
        console.log('初始化裁剪器')
      }
    }

    const confirmCrop = () => {
      // 这里实现裁剪确认逻辑
      // 为了简化，我们直接使用原图
      userInfo.avatarUrl = cropImageSrc.value
      closeAvatarCropModal()
      toastStore.showToast('头像更新成功', 'success')
    }

    const closeAvatarCropModal = () => {
      showAvatarCropModal.value = false
      cropImageSrc.value = ''
      if (avatarInput.value) {
        avatarInput.value.value = ''
      }
    }

    // 个人描述编辑相关方法
    const startEditBio = () => {
      editingBio.value = true
      editBioText.value = userInfo.bio || ''
      nextTick(() => {
        bioTextarea.value?.focus()
      })
    }

    const saveBio = () => {
      userInfo.bio = editBioText.value.trim()
      editingBio.value = false
      toastStore.showToast('个人描述更新成功', 'success')
    }

    const cancelEditBio = () => {
      editingBio.value = false
      editBioText.value = ''
    }
    
    const editProfile = () => {
      Object.assign(editedProfile, userInfo)
      showEditProfileModal.value = true
    }
    
    const closeEditProfileModal = () => {
      showEditProfileModal.value = false
    }
    
    const saveProfile = async () => {
      try {
        const updatedProfile = await userService.updateUserProfile(props.userId, editedProfile)
        Object.assign(userInfo, updatedProfile)
        toastStore.success('个人资料已更新')
        closeEditProfileModal()
      } catch (error) {
        console.error('更新个人资料失败:', error)
        toastStore.error('更新个人资料失败')
      }
    }
    
    const shareProfile = () => {
      const profileUrl = `${window.location.origin}/profile/${userInfo.name}`
      navigator.clipboard.writeText(profileUrl).then(() => {
        toastStore.success('个人主页链接已复制到剪贴板')
      }).catch(() => {
        toastStore.error('复制失败，请手动复制链接')
      })
    }
    
    const viewPublished = () => {
      const element = document.querySelector('.space-content')
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    }
    
    const viewFollowers = () => {
      modalType.value = 'followers'
      searchQuery.value = ''
      showFollowModal.value = true
    }

    const viewFollowing = () => {
      modalType.value = 'following'
      searchQuery.value = ''
      showFollowModal.value = true
    }

    const closeFollowModal = () => {
      showFollowModal.value = false
      searchQuery.value = ''
    }

    const viewUserProfile = (user) => {
      router.push(`/user/${user.id}`)
    }

    const followUser = (user) => {
      user.isFollowing = true
      toastStore.success(`已关注 ${user.name}`)
    }

    const unfollowUser = (user) => {
      user.isFollowing = false
      toastStore.success(`已取消关注 ${user.name}`)
    }

    // 组件挂载时加载数据
    onMounted(() => {
      loadUserData()
    })

    return {
      loading,
      userInfo,
      userStats,
      compactAchievements,
      earnedBadges,
      showFollowModal,
      showEditProfileModal,
      showAvatarCropModal,
      modalType,
      modalTitle,
      searchQuery,
      filteredUsers,
      editedProfile,
      editingBio,
      editBioText,
      cropImageSrc,
      avatarInput,
      bioTextarea,
      cropImage,
      triggerAvatarUpload,
      handleAvatarUpload,
      confirmCrop,
      closeAvatarCropModal,
      startEditBio,
      saveBio,
      cancelEditBio,
      editProfile,
      shareProfile,
      viewPublished,
      viewFollowers,
      viewFollowing,
      closeFollowModal,
      closeEditProfileModal,
      saveProfile,
      viewUserProfile,
      followUser,
      unfollowUser
    }
  }
}
</script>

<style scoped>
.personal-card-wrapper {
  position: relative;
}

.personal-card {
  position: relative;
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 30px;
}

.card-background {
  position: relative;
  height: 120px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
}

.card-content {
  position: relative;
  padding: 0 30px 30px;
}

/* 右上角统计数据 */
.stats-corner {
  position: absolute;
  top: -60px;
  right: 30px;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 10;
}

.stats-corner .stat-item {
  text-align: center;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  min-width: 50px;
}

.stats-corner .stat-item:hover {
  background: rgba(79, 70, 229, 0.1);
}

.stats-corner .stat-number {
  font-size: 18px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2px;
  line-height: 1.2;
}

.stats-corner .stat-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  line-height: 1.2;
}

.stats-corner .stat-divider {
  width: 1px;
  height: 30px;
  background: #e5e7eb;
  margin: 0 12px;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  gap: 30px;
  align-items: flex-start;
}

.personal-info {
  display: flex;
  gap: 20px;
  flex: 1;
}

.avatar-section {
  flex-shrink: 0;
  margin-top: -40px;
}

.avatar-container {
  position: relative;
  width: 100px;
  height: 100px;
}

.avatar,
.avatar-placeholder {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 4px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.avatar {
  object-fit: cover;
}

.avatar-placeholder {
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 32px;
}

.edit-avatar-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #4f46e5;
  color: white;
  border: 2px solid white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.2s ease;
}

.edit-avatar-btn:hover {
  background: #4338ca;
  transform: scale(1.1);
}

.info-section {
  flex: 1;
  padding-top: 20px;
}

.basic-info {
  margin-bottom: 20px;
}

.user-name {
  font-size: 36px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  height: 80px; /* 与头像高度一致，实现中线对齐 */
}

.user-department {
  color: #6b7280;
  font-size: 16px;
  margin: 0 0 12px 0;
  font-weight: 500;
}

/* 个人描述容器 */
.user-bio-container {
  position: relative;
}

.user-bio {
  color: #374151;
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
  height: 44.8px; /* 固定两行文字高度 (14px * 1.6 * 2) */
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  cursor: pointer;
  position: relative;
  padding-right: 24px;
  transition: all 0.2s ease;
}

.user-bio:hover {
  background: #f9fafb;
  border-radius: 4px;
  padding: 4px 24px 4px 4px;
  margin: -4px 0;
}

.edit-bio-icon {
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 12px;
  opacity: 0;
  transition: all 0.2s ease;
}

.user-bio:hover .edit-bio-icon {
  opacity: 1;
}

/* 个人描述编辑 */
.bio-edit-container {
  position: relative;
}

.bio-edit-input {
  width: 100%;
  min-height: 44.8px;
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.6;
  color: #374151;
  resize: vertical;
  font-family: inherit;
}

.bio-edit-input:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.bio-edit-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.bio-save-btn,
.bio-cancel-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.bio-save-btn {
  background: #4f46e5;
  color: white;
}

.bio-save-btn:hover {
  background: #4338ca;
}

.bio-cancel-btn {
  background: #f3f4f6;
  color: #6b7280;
}

.bio-cancel-btn:hover {
  background: #e5e7eb;
}

.contact-info {
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #4f46e5;
  font-size: 14px;
  text-decoration: none;
  transition: all 0.2s ease;
}

.contact-item:hover {
  color: #4338ca;
  text-decoration: underline;
}

.skills-section {
  margin-top: 16px;
}

.skills-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.skill-tag {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #e0e7ff;
  color: #3730a3;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 8px;
  border: 1px solid;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-primary {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

.btn-primary:hover {
  background: #4338ca;
  border-color: #4338ca;
}

.btn-outline {
  background: white;
  color: #6b7280;
  border-color: #d1d5db;
}

.btn-outline:hover {
  background: #f9fafb;
  color: #374151;
  border-color: #9ca3af;
}

/* 创作成就侧边栏 */
.achievements-sidebar {
  flex-shrink: 0;
  width: 280px;
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  margin-top: 20px;
}

.achievements-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.achievements-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 6px;
  margin: 0;
}

.achievements-title i {
  color: #f59e0b;
  font-size: 12px;
}



.achievements-compact {
  display: flex;
  gap: 16px;
  justify-content: space-between;
}

.achievement-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 10px 6px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  text-align: center;
  transition: all 0.2s ease;
  flex: 1;
  cursor: pointer;
}

.achievement-item:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.achievement-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
}

.achievement-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1px;
}

.achievement-number {
  font-size: 12px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.2;
}

.achievement-label {
  font-size: 10px;
  color: #6b7280;
  line-height: 1.2;
}

.achievement-icon.views {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.achievement-icon.likes {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.achievement-icon.favorites {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.achievement-icon.engagement {
  background: linear-gradient(135deg, #10b981, #059669);
}





.achievements-expanded {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.badges-title {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 10px 0;
}

.badges-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 4px;
}

.badge-item {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.badge-item:hover {
  transform: scale(1.1);
}

.badge-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
  border: 2px solid #e2e8f0;
  transition: all 0.2s ease;
}

.badge-item:hover .badge-icon {
  border-color: #cbd5e1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.badge-icon.bronze {
  background: linear-gradient(135deg, #cd7f32, #a0522d);
}

.badge-icon.silver {
  background: linear-gradient(135deg, #c0c0c0, #a8a8a8);
}

.badge-icon.gold {
  background: linear-gradient(135deg, #ffd700, #ffb347);
}

.badge-icon.platinum {
  background: linear-gradient(135deg, #e5e4e2, #b8b8b8);
}



/* 头像裁剪弹窗 */
.avatar-crop-modal {
  width: 500px;
  max-width: 90vw;
}

.crop-container {
  width: 100%;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9fafb;
  border-radius: 8px;
  overflow: hidden;
}

.crop-container img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.modal-close {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 16px;
}

.modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-content {
  padding: 24px;
  max-height: calc(80vh - 80px);
  overflow-y: auto;
}

.edit-profile-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.form-input,
.form-textarea {
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 10px;
}

.search-section {
  margin-bottom: 20px;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: 12px;
  color: #9ca3af;
  font-size: 14px;
}

.search-box input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.search-box input:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.users-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.user-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.user-item:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  cursor: pointer;
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  overflow: hidden;
  flex-shrink: 0;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-avatar .avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.user-bio {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-actions {
  flex-shrink: 0;
}

.user-actions .btn {
  padding: 8px 16px;
  font-size: 14px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  color: #d1d5db;
  margin-bottom: 16px;
}

.empty-state h4 {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 14px;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-content {
    padding: 0 20px 20px;
  }

  .stats-corner {
    position: static;
    margin: 0 0 20px 0;
    justify-content: center;
    background: white;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }

  .main-content {
    flex-direction: column;
    gap: 20px;
  }

  .personal-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 15px;
  }

  .avatar-section {
    margin-top: -50px;
  }

  .info-section {
    padding-top: 0;
  }

  .action-buttons {
    justify-content: center;
  }

  .achievements-sidebar {
    width: 100%;
    margin-top: 0;
  }

  .achievements-compact {
    flex-direction: column;
    gap: 8px;
  }

  .achievement-item {
    padding: 8px 4px;
    gap: 4px;
  }

  .achievement-icon {
    width: 20px;
    height: 20px;
    font-size: 9px;
  }

  .achievement-number {
    font-size: 11px;
  }

  .achievement-label {
    font-size: 9px;
  }

  .badges-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 6px;
  }

  .badges-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 4px;
  }

  .badge-icon {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }



  .modal {
    margin: 10px;
    max-height: 90vh;
  }

  .modal-header {
    padding: 16px 20px;
  }

  .modal-content {
    padding: 20px;
  }

  .user-item {
    padding: 12px;
    gap: 12px;
  }

  .user-avatar {
    width: 40px;
    height: 40px;
  }

  .user-avatar .avatar-placeholder {
    font-size: 16px;
  }

  .user-name {
    font-size: 15px;
  }

  .user-bio {
    font-size: 13px;
  }

  .user-actions .btn {
    padding: 6px 12px;
    font-size: 13px;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions .btn {
    width: 100%;
  }
}
</style>
