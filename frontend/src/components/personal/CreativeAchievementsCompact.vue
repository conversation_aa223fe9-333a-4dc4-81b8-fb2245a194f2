<template>
  <div class="creative-achievements-compact">
    <div class="achievements-header">
      <h4 class="achievements-title">
        <i class="fas fa-trophy"></i>
        创作成就
      </h4>
      <button class="expand-btn" @click="showExpanded = !showExpanded">
        <i :class="showExpanded ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
      </button>
    </div>

    <!-- 紧凑的成就展示 -->
    <div class="achievements-grid">
      <div class="achievement-item" v-for="item in compactAchievements" :key="item.key">
        <div class="achievement-icon" :class="item.type">
          <i :class="item.icon"></i>
        </div>
        <div class="achievement-info">
          <div class="achievement-number">{{ item.value }}</div>
          <div class="achievement-label">{{ item.label }}</div>
        </div>
        <div class="achievement-trend" :class="{ positive: item.trend > 0, negative: item.trend < 0 }">
          <i :class="item.trend > 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
          <span>{{ Math.abs(item.trend) }}%</span>
        </div>
      </div>
    </div>

    <!-- 展开的详细视图 -->
    <div v-if="showExpanded" class="achievements-expanded">
      <!-- 月度趋势图表 -->
      <div class="trend-chart">
        <h5 class="chart-title">本月趋势</h5>
        <div class="chart-container">
          <div class="chart-bars">
            <div v-for="(day, index) in monthlyTrend" :key="index" class="chart-bar">
              <div class="bar-fill" :style="{ height: day.percentage + '%' }"></div>
              <div class="bar-label">{{ day.day }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 成就徽章 -->
      <div class="badges-section">
        <h5 class="badges-title">成就徽章</h5>
        <div class="badges-grid">
          <div v-for="badge in earnedBadges.slice(0, 6)" :key="badge.id" class="badge-item" :title="badge.description">
            <div class="badge-icon" :class="badge.type">
              <i :class="badge.icon"></i>
            </div>
            <div class="badge-name">{{ badge.name }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'

export default {
  name: 'CreativeAchievementsCompact',
  setup() {
    const showExpanded = ref(false)

    // 创作成就数据
    const achievements = reactive({
      totalReads: 12580,
      totalLikes: 3240,
      totalBookmarks: 856,
      totalComments: 234
    })

    // 模拟趋势数据（相比上周的变化百分比）
    const readsTrend = ref(12.5)
    const likesTrend = ref(8.3)
    const bookmarksTrend = ref(-2.1)
    const engagementTrend = ref(5.7)

    // 计算互动率
    const engagementRate = computed(() => {
      if (achievements.totalReads === 0) return 0
      const totalEngagements = achievements.totalLikes + achievements.totalBookmarks + achievements.totalComments
      return ((totalEngagements / achievements.totalReads) * 100).toFixed(1)
    })

    // 格式化数字显示
    const formatNumber = (num) => {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + 'w'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k'
      }
      return num.toString()
    }

    // 紧凑展示的成就数据
    const compactAchievements = computed(() => [
      {
        key: 'reads',
        icon: 'fas fa-eye',
        type: 'reads',
        value: formatNumber(achievements.totalReads),
        label: '阅读',
        trend: readsTrend.value
      },
      {
        key: 'likes',
        icon: 'fas fa-heart',
        type: 'likes',
        value: formatNumber(achievements.totalLikes),
        label: '点赞',
        trend: likesTrend.value
      },
      {
        key: 'bookmarks',
        icon: 'fas fa-bookmark',
        type: 'bookmarks',
        value: formatNumber(achievements.totalBookmarks),
        label: '收藏',
        trend: bookmarksTrend.value
      },
      {
        key: 'engagement',
        icon: 'fas fa-chart-line',
        type: 'engagement',
        value: engagementRate.value + '%',
        label: '互动率',
        trend: engagementTrend.value
      }
    ])

    // 月度趋势数据（模拟最近7天）
    const monthlyTrend = ref([
      { day: '1', percentage: 65 },
      { day: '2', percentage: 78 },
      { day: '3', percentage: 45 },
      { day: '4', percentage: 89 },
      { day: '5', percentage: 92 },
      { day: '6', percentage: 67 },
      { day: '7', percentage: 85 }
    ])

    // 成就徽章数据
    const earnedBadges = ref([
      {
        id: 1,
        name: '新手作者',
        icon: 'fas fa-pen',
        type: 'bronze',
        description: '发布第一篇文章'
      },
      {
        id: 2,
        name: '人气作者',
        icon: 'fas fa-fire',
        type: 'gold',
        description: '单篇文章获得1000+阅读'
      },
      {
        id: 3,
        name: '持续创作',
        icon: 'fas fa-calendar-check',
        type: 'silver',
        description: '连续7天发布内容'
      },
      {
        id: 4,
        name: '互动达人',
        icon: 'fas fa-comments',
        type: 'gold',
        description: '获得100+评论'
      },
      {
        id: 5,
        name: '收藏之星',
        icon: 'fas fa-star',
        type: 'silver',
        description: '单篇文章获得50+收藏'
      },
      {
        id: 6,
        name: '技术专家',
        icon: 'fas fa-code',
        type: 'platinum',
        description: '在技术分类获得认可'
      }
    ])

    return {
      showExpanded,
      compactAchievements,
      monthlyTrend,
      earnedBadges
    }
  }
}
</script>

<style scoped>
.creative-achievements-compact {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.achievements-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.achievements-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
}

.achievements-title i {
  color: #f59e0b;
  font-size: 14px;
}

.expand-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 6px;
  border-radius: 6px;
  transition: all 0.2s ease;
  font-size: 14px;
}

.expand-btn:hover {
  background: #e2e8f0;
  color: #374151;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.achievement-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  text-align: center;
  transition: all 0.2s ease;
}

.achievement-item:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.achievement-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
}

.achievement-icon.reads {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.achievement-icon.likes {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.achievement-icon.bookmarks {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.achievement-icon.engagement {
  background: linear-gradient(135deg, #10b981, #059669);
}

.achievement-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.achievement-number {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
}

.achievement-label {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.2;
}

.achievement-trend {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 11px;
  font-weight: 500;
  position: absolute;
  top: 8px;
  right: 8px;
}

.achievement-trend.positive {
  color: #059669;
}

.achievement-trend.negative {
  color: #dc2626;
}

.achievements-expanded {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.trend-chart {
  margin-bottom: 20px;
}

.chart-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
}

.chart-container {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e2e8f0;
}

.chart-bars {
  display: flex;
  align-items: end;
  gap: 8px;
  height: 80px;
}

.chart-bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.bar-fill {
  width: 100%;
  background: linear-gradient(to top, #4f46e5, #7c3aed);
  border-radius: 2px 2px 0 0;
  min-height: 4px;
  transition: all 0.3s ease;
}

.bar-label {
  font-size: 10px;
  color: #6b7280;
  font-weight: 500;
}

.badges-section {
  margin-top: 20px;
}

.badges-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
}

.badges-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.badge-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 12px 8px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.badge-item:hover {
  background: #f9fafb;
  border-color: #cbd5e1;
}

.badge-icon {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
}

.badge-icon.bronze {
  background: linear-gradient(135deg, #cd7f32, #a0522d);
}

.badge-icon.silver {
  background: linear-gradient(135deg, #c0c0c0, #a8a8a8);
}

.badge-icon.gold {
  background: linear-gradient(135deg, #ffd700, #ffb347);
}

.badge-icon.platinum {
  background: linear-gradient(135deg, #e5e4e2, #b8b8b8);
}

.badge-name {
  font-size: 11px;
  color: #374151;
  font-weight: 500;
  line-height: 1.2;
}

@media (max-width: 768px) {
  .achievements-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
  }

  .achievement-item {
    padding: 12px 8px;
    gap: 6px;
  }

  .achievement-icon {
    width: 24px;
    height: 24px;
    font-size: 12px;
  }

  .achievement-number {
    font-size: 14px;
  }

  .achievement-label {
    font-size: 10px;
  }

  .badges-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }

  .badge-item {
    padding: 8px 6px;
    gap: 4px;
  }

  .badge-icon {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }

  .badge-name {
    font-size: 10px;
  }

  .chart-bars {
    height: 60px;
    gap: 4px;
  }
}
</style>
