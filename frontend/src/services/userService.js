import api from './api'

/**
 * 用户相关API服务
 */
class UserService {
  /**
   * 获取用户完整信息
   * @param {number} userId - 用户ID
   * @returns {Promise} 用户信息
   */
  async getUserProfile(userId) {
    return api.get(`/v1/users/${userId}/profile`)
  }

  /**
   * 更新用户资料
   * @param {number} userId - 用户ID
   * @param {Object} profileData - 更新数据
   * @returns {Promise} 更新结果
   */
  async updateUserProfile(userId, profileData) {
    return api.put(`/v1/users/${userId}/profile`, profileData)
  }

  /**
   * 获取用户关联的内容列表
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 内容列表
   */
  async getUserContents(userId, params = {}) {
    return api.get(`/v1/users/${userId}/contents`, params)
  }

  /**
   * 获取用户加入的团队列表
   * @param {number} userId - 用户ID
   * @returns {Promise} 团队列表
   */
  async getUserTeams(userId) {
    return api.get(`/v1/users/${userId}/teams`)
  }

  /**
   * 获取用户学习信息
   * @param {number} userId - 用户ID
   * @returns {Promise} 学习信息
   */
  async getUserLearnings(userId) {
    return api.get(`/v1/users/${userId}/learnings`)
  }
}

export default new UserService()
