import api from './api'

/**
 * 用户相关API服务
 */
class UserService {
  /**
   * 获取用户完整信息
   * @param {number} userId - 用户ID
   * @returns {Promise} 用户信息
   */
  async getUserProfile(userId) {
    return api.get(`/v1/users/${userId}/profile`)
  }

  /**
   * 更新用户资料
   * @param {number} userId - 用户ID
   * @param {Object} profileData - 更新数据
   * @returns {Promise} 更新结果
   */
  async updateUserProfile(userId, profileData) {
    return api.put(`/v1/users/${userId}/profile`, profileData)
  }

  /**
   * 获取用户关联的内容列表
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 内容列表
   */
  async getUserContents(userId, params = {}) {
    return api.get(`/v1/users/${userId}/contents`, params)
  }

  /**
   * 获取用户加入的团队列表
   * @param {number} userId - 用户ID
   * @returns {Promise} 团队列表
   */
  async getUserTeams(userId) {
    return api.get(`/v1/users/${userId}/teams`)
  }

  /**
   * 获取用户学习信息
   * @param {number} userId - 用户ID
   * @returns {Promise} 学习信息
   */
  async getUserLearnings(userId) {
    return api.get(`/v1/users/${userId}/learnings`)
  }

  /**
   * 获取用户活动历史
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 活动历史
   */
  async getUserActivities(userId, params = {}) {
    return api.get(`/v1/users/${userId}/activities`, params)
  }

  /**
   * 获取用户成就徽章
   * @param {number} userId - 用户ID
   * @returns {Promise} 成就徽章列表
   */
  async getUserAchievements(userId) {
    return api.get(`/v1/users/${userId}/achievements`)
  }

  /**
   * 关注用户
   * @param {number} userId - 要关注的用户ID
   * @returns {Promise} 关注结果
   */
  async followUser(userId) {
    return api.post(`/v1/users/${userId}/follow`)
  }

  /**
   * 取消关注用户
   * @param {number} userId - 要取消关注的用户ID
   * @returns {Promise} 取消关注结果
   */
  async unfollowUser(userId) {
    return api.delete(`/v1/users/${userId}/follow`)
  }

  /**
   * 上传用户头像
   * @param {number} userId - 用户ID
   * @param {FormData} formData - 包含头像文件的表单数据
   * @returns {Promise} 上传结果
   */
  async uploadAvatar(userId, formData) {
    return api.post(`/v1/users/${userId}/avatar`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  /**
   * 获取用户推荐团队
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 推荐团队列表
   */
  async getRecommendedTeams(userId, params = {}) {
    return api.get(`/v1/users/${userId}/recommended-teams`, params)
  }

  /**
   * 获取用户团队活动
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 团队活动列表
   */
  async getUserTeamActivities(userId, params = {}) {
    return api.get(`/v1/users/${userId}/team-activities`, params)
  }

  /**
   * 获取用户知识列表
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数 {knowledgeTypeCode, page, pageSize}
   * @returns {Promise} 知识列表
   */
  async getUserKnowledge(userId, params = {}) {
    return api.get(`/v1/users/${userId}/knowledge`, params)
  }

  /**
   * 获取用户喜欢的内容列表
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数 {knowledgeTypeCode, page, pageSize}
   * @returns {Promise} 喜欢的内容列表
   */
  async getUserLikes(userId, params = {}) {
    return api.get(`/v1/users/${userId}/likes`, params)
  }

  /**
   * 获取用户收藏的内容列表
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数 {knowledgeTypeCode, page, pageSize}
   * @returns {Promise} 收藏的内容列表
   */
  async getUserFavorites(userId, params = {}) {
    return api.get(`/v1/users/${userId}/favorites`, params)
  }
}

export default new UserService()
