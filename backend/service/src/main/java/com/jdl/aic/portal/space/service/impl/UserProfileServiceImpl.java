package com.jdl.aic.portal.space.service.impl;


import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.FavoriteDTO;
import com.jdl.aic.core.service.client.dto.community.request.GetUserFavoritesRequest;
import com.jdl.aic.core.service.client.dto.user.UserDTO;
import com.jdl.aic.core.service.client.service.KnowledgeService;
import com.jdl.aic.core.service.portal.client.FavoriteDataService;
import com.jdl.aic.core.service.portal.client.LikeDataService;
import com.jdl.aic.core.service.portal.client.UserDataService;
import com.jdl.aic.portal.common.dto.UserProfileDTO;
import com.jdl.aic.portal.common.exception.BusinessException;
import com.jdl.aic.portal.common.utils.DataLoader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 用户个人空间服务实现
 */
@Service
public class UserProfileServiceImpl implements UserProfileService {

    @Autowired
    private DataLoader dataLoader;
    @Autowired
    private UserDataService userDataService;
    @Autowired
    private FavoriteDataService favoriteDataService;
    @Autowired
    private KnowledgeService knowledgeService;
    @Autowired
    private LikeDataService likeDataService;

    @Override
    @SuppressWarnings("unchecked")
    public UserProfileDTO getUserProfile(Long userId) {
        Result<UserDTO> result = userDataService.getUserById(userId);
        UserDTO userDTO = result.getData();
        Map<String, Object> user = dataLoader.getUserById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        UserProfileDTO profileDTO = new UserProfileDTO();

        // 基础信息
        UserProfileDTO.BasicInfoDTO basicInfo = new UserProfileDTO.BasicInfoDTO();
        basicInfo.setUserId(userDTO.getId());
        basicInfo.setUsername(userDTO.getUsername());
        basicInfo.setDisplayName(userDTO.getDisplayName());
        basicInfo.setAvatarUrl(userDTO.getAvatarUrl());
        basicInfo.setBio(userDTO.getBio());
        basicInfo.setDepartment(userDTO.getDepartment());

        // 解析标签
        List<String> tags = userDTO.getTags();
        basicInfo.setTags(tags != null ? tags : new ArrayList<>());

        profileDTO.setBasicInfo(basicInfo);

        Result<UserDataService.UserStatsDTO> statsResult = userDataService.getUserStats(userId);
        if (statsResult != null && statsResult.isSuccess() && statsResult.getData() != null) {
            UserDataService.UserStatsDTO stats = statsResult.getData();
            UserProfileDTO.AchievementsDTO achievements = new UserProfileDTO.AchievementsDTO();

            // 映射统计数据
            achievements.setArticlesPublished(stats.getCreatedKnowledgeCount() != null ?
                    stats.getCreatedKnowledgeCount() : 0);
            achievements.setTotalViews(stats.getTotalReadCount() != null ?
                    stats.getTotalReadCount().longValue() : 0L);
            achievements.setTotalLikes(stats.getTotalLikeCount() != null ?
                    stats.getTotalLikeCount().longValue() : 0L);
            achievements.setTotalFavorites(stats.getFavoriteKnowledgeCount() != null ?
                    stats.getFavoriteKnowledgeCount().longValue() : 0L);
        }
        // 成就信息
        Map<String, Object> achievementsData = (Map<String, Object>) user.get("achievements");
        UserProfileDTO.AchievementsDTO achievements = new UserProfileDTO.AchievementsDTO();
        if (achievementsData != null) {
            achievements.setArticlesPublished(((Number) achievementsData.get("articlesPublished")).intValue());
            achievements.setTotalViews(((Number) achievementsData.get("totalViews")).longValue());
            achievements.setTotalLikes(((Number) achievementsData.get("totalLikes")).longValue());
            achievements.setTotalFavorites(((Number) achievementsData.get("totalFavorites")).longValue());

            // 解析徽章
            List<Map<String, Object>> badgesData = (List<Map<String, Object>>) achievementsData.get("badges");
            List<UserProfileDTO.BadgeDTO> badges = new ArrayList<>();
            if (badgesData != null) {
                for (Map<String, Object> badgeData : badgesData) {
                    UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
                    badge.setName((String) badgeData.get("name"));
                    badge.setIcon((String) badgeData.get("icon"));
                    badge.setType((String) badgeData.get("type"));
                    badges.add(badge);
                }
            }
            achievements.setBadges(badges);
        } else {
            achievements.setArticlesPublished(0);
            achievements.setTotalViews(0L);
            achievements.setTotalLikes(0L);
            achievements.setTotalFavorites(0L);
            achievements.setBadges(new ArrayList<>());
        }

        profileDTO.setAchievements(achievements);
        // 社交信息
        Map<String, Object> socialData = (Map<String, Object>) user.get("social");
        UserProfileDTO.SocialDTO social = new UserProfileDTO.SocialDTO();
        if (socialData != null) {
            social.setFollowers(((Number) socialData.get("followers")).intValue());
            social.setFollowing(((Number) socialData.get("following")).intValue());
        } else {
            social.setFollowers(0);
            social.setFollowing(0);
        }

        profileDTO.setSocial(social);

        return profileDTO;
    }

    @Override
    public UserProfileDTO updateUserProfile(Long userId, Map<String, Object> profileData) {
        //Map<String, Object> updatedUser = dataLoader.updateUserProfile(userId, profileData);
        UserDTO userDTO = new UserDTO();

        // 更新允许修改的字段
        if (profileData.containsKey("avatarUrl")) {
            userDTO.setAvatarUrl(profileData.get("avatarUrl").toString());
        }
        if (profileData.containsKey("bio")) {
            userDTO.setBio(profileData.get("bio").toString());
        }
        if (profileData.containsKey("tags")) {
            userDTO.setTags((List<String>) profileData.get("tags"));
        }
        userDataService.updateUser(userId, userDTO);
        return getUserProfile(userId);
    }

    @Override
    public Map<String, Object> getUserContents(Long userId, String associationType, String knowledgeTypeCode, Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;
        PageRequest pageRequest = new PageRequest();
        pageRequest.setSize(pageSize);
        pageRequest.setPage(page);
        // 获取知识的类型(MCP、提示词、文章、工具)，在知识接口中

        //knowledgeService.getKnowledgeById()

        // 从知识表中获取默认获取作者为本人的知识集合，支持按上述类型筛选
        // 从收藏表中获取收藏的知识集合，支持按上述类型筛选
        // 从点赞表中获取点赞的知识集合，支持按上述类型筛选
        GetUserFavoritesRequest getUserFavoritesRequest = new GetUserFavoritesRequest();
        getUserFavoritesRequest.setUserId(userId);
        getUserFavoritesRequest.setPageRequest(pageRequest);
        Result<PageResult<FavoriteDTO>> favoritesResult = favoriteDataService.getUserFavorites(getUserFavoritesRequest);
        if (favoritesResult != null && favoritesResult.isSuccess() && favoritesResult.getData() != null) {
         PageResult<FavoriteDTO> favorites = favoritesResult.getData();
         List<FavoriteDTO> favoriteList = favorites.getRecords();
         for (FavoriteDTO favorite : favoriteList) {
             System.out.println(favorite.getContentId());
         }
        }


        List<Map<String, Object>> allContents = dataLoader.getUserContents(userId, associationType, knowledgeTypeCode);
        int total = allContents.size();
        // 手动分页
        int fromIndex = (page - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        List<Map<String, Object>> pagedContents = allContents.subList(fromIndex, toIndex);

        // 统计各类型数量
        Map<String, Integer> countsByType = new HashMap<>();
        Map<String, Object> allUserContents = new HashMap<>();
        allUserContents.put("all", dataLoader.getUserContents(userId, associationType, null).size());


        // 统计各知识类型的数量
        List<Map<String, Object>> allUserContentsList = dataLoader.getUserContents(userId, associationType, null);
        for (Map<String, Object> content : allUserContentsList) {
            String type = (String) content.get("knowledgeTypeCode");
            countsByType.put(type, countsByType.getOrDefault(type, 0) + 1);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("page", page);
        result.put("pageSize", pageSize);
        result.put("total", total);
        result.put("list", pagedContents);
        result.put("countsByType", countsByType);
        return result;
    }

    @Override
    public List<Map<String, Object>> getUserTeams(Long userId) {
        return dataLoader.getUserTeams(userId);
    }

    @Override
    public Map<String, Object> getUserLearnings(Long userId) {
        Map<String, Object> learningData = dataLoader.getUserLearnings(userId);

        if (learningData != null) {
            return learningData;
        } else {
            // 返回默认学习数据
            Map<String, Object> defaultLearning = new HashMap<>();
            defaultLearning.put("userId", userId);
            defaultLearning.put("totalLearningHours", 0.0);
            defaultLearning.put("coursesCompleted", 0);
            defaultLearning.put("consecutiveLearningDays", 0);
            defaultLearning.put("inProgress", new ArrayList<>());
            defaultLearning.put("weeklyGoals", new ArrayList<>());
            return defaultLearning;
        }
    }
}
