package com.jdl.aic.portal.space.service;

import com.jdl.aic.portal.common.dto.UserProfileDTO;

import java.util.List;
import java.util.Map;

/**
 * 用户个人空间服务接口
 */
public interface UserProfileService {
    
    /**
     * 获取用户完整资料信息
     * @param userId 用户ID
     * @return 用户资料DTO
     */
    UserProfileDTO getUserProfile(Long userId);
    
    /**
     * 更新用户资料
     * @param userId 用户ID
     * @param profileData 更新数据
     * @return 更新后的用户资料
     */
    UserProfileDTO updateUserProfile(Long userId, Map<String, Object> profileData);
    
    /**
     * 获取用户关联的内容列表
     * @param userId 用户ID
     * @param associationType 关联类型：published, favorited, liked
     * @param knowledgeTypeCode 知识类型代码
     * @param page 页码
     * @param pageSize 每页大小
     * @return 分页内容列表
     */
    Map<String, Object> getUserContents(Long userId, String associationType, String knowledgeTypeCode, Integer page, Integer pageSize);
    
    /**
     * 获取用户加入的团队列表
     * @param userId 用户ID
     * @return 团队列表
     */
    List<Map<String, Object>> getUserTeams(Long userId);

    /**
     * 获取用户学习信息
     * @param userId 用户ID
     * @return 学习信息
     */
    Map<String, Object> getUserLearnings(Long userId);
}
