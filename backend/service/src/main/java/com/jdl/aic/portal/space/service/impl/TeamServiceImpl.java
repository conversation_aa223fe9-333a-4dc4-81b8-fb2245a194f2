package com.jdl.aic.portal.space.service.impl;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.request.team.GetTeamListRequest;
import com.jdl.aic.core.service.client.dto.team.TeamDTO;
import com.jdl.aic.core.service.client.dto.user.UserDTO;
import com.jdl.aic.core.service.portal.client.TeamDataService;
import com.jdl.aic.core.service.portal.client.UserDataService;
import com.jdl.aic.portal.common.dto.TeamProfileDTO;
import com.jdl.aic.portal.common.exception.BusinessException;
import com.jdl.aic.portal.common.utils.DataLoader;
import com.jdl.aic.portal.space.service.TeamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 团队空间服务实现
 */
@Service
public class TeamServiceImpl implements TeamService {

    @Autowired
    private DataLoader dataLoader;
    @Autowired
    private TeamDataService teamDataService;
    @Autowired
    private UserDataService userDataService;

    @Override
    @SuppressWarnings("unchecked")
    public TeamProfileDTO getTeamProfile(Long teamId) {
        Map<String, Object> team = dataLoader.getTeamById(teamId);
        if (team == null) {
            throw new BusinessException("团队不存在");
        }

        TeamProfileDTO profileDTO = new TeamProfileDTO();

        // 基础信息
        TeamProfileDTO.BasicInfoDTO basicInfo = new TeamProfileDTO.BasicInfoDTO();
        basicInfo.setTeamId(((Number) team.get("teamId")).longValue());
        basicInfo.setName((String) team.get("name"));
        basicInfo.setAvatarUrl((String) team.get("avatarUrl"));
        basicInfo.setDescription((String) team.get("description"));

        // 处理创建时间
        String createdAtStr = (String) team.get("createdAt");
        if (createdAtStr != null) {
            try {
                // 这里可以根据需要解析日期字符串
                basicInfo.setCreatedAt(new Date());
            } catch (Exception e) {
                basicInfo.setCreatedAt(new Date());
            }
        }

        basicInfo.setPrivacy((String) team.get("privacy"));

        // 获取成员数量
        List<Map<String, Object>> members = (List<Map<String, Object>>) team.get("members");
        basicInfo.setMemberCount(members != null ? members.size() : 0);

        // 解析标签
        List<String> tags = (List<String>) team.get("tags");
        basicInfo.setTags(tags != null ? tags : new ArrayList<>());

        profileDTO.setBasicInfo(basicInfo);

        // 成就信息
        Map<String, Object> achievementsData = (Map<String, Object>) team.get("achievements");
        TeamProfileDTO.AchievementsDTO achievements = new TeamProfileDTO.AchievementsDTO();
        if (achievementsData != null) {
            achievements.setArticlesRecommended(((Number) achievementsData.get("articlesRecommended")).intValue());
            achievements.setTotalViews(((Number) achievementsData.get("totalViews")).longValue());
            achievements.setTotalLikes(((Number) achievementsData.get("totalLikes")).longValue());
            achievements.setTotalFavorites(((Number) achievementsData.get("totalFavorites")).longValue());
        } else {
            achievements.setArticlesRecommended(0);
            achievements.setTotalViews(0L);
            achievements.setTotalLikes(0L);
            achievements.setTotalFavorites(0L);
        }

        profileDTO.setAchievements(achievements);

        return profileDTO;
    }


    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> getTeamMembers(Long teamId, Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;

        Map<String, Object> team = dataLoader.getTeamById(teamId);
        if (team == null) {
            throw new BusinessException("团队不存在");
        }

        List<Map<String, Object>> members = (List<Map<String, Object>>) team.get("members");
        if (members == null) {
            members = new ArrayList<>();
        }

        int total = members.size();

        // 手动分页
        int fromIndex = (page - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        List<Map<String, Object>> pagedMembers = members.subList(fromIndex, toIndex);

        // 获取成员详细信息
        List<Map<String, Object>> memberList = new ArrayList<>();
        for (Map<String, Object> member : pagedMembers) {
            Long userId = ((Number) member.get("userId")).longValue();
            Map<String, Object> user = dataLoader.getUserById(userId);

            if (user != null) {
                Map<String, Object> memberInfo = new HashMap<>();
                memberInfo.put("userId", user.get("userId"));
                memberInfo.put("displayName", user.get("displayName"));
                memberInfo.put("username", user.get("username"));
                memberInfo.put("avatarUrl", user.get("avatarUrl"));
                memberInfo.put("role", member.get("role"));
                memberInfo.put("joinedAt", member.get("joinedAt"));

                // 成就信息
                Map<String, Object> userAchievements = (Map<String, Object>) user.get("achievements");
                Map<String, Object> achievements = new HashMap<>();
                if (userAchievements != null) {
                    achievements.put("articlesPublished", userAchievements.get("articlesPublished"));
                    achievements.put("totalViews", userAchievements.get("totalViews"));
                    achievements.put("totalLikes", userAchievements.get("totalLikes"));
                    achievements.put("totalFavorites", userAchievements.get("totalFavorites"));
                } else {
                    achievements.put("articlesPublished", 0);
                    achievements.put("totalViews", 0L);
                    achievements.put("totalLikes", 0L);
                    achievements.put("totalFavorites", 0L);
                }
                memberInfo.put("achievements", achievements);

                memberList.add(memberInfo);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("page", page);
        result.put("pageSize", pageSize);
        result.put("total", total);
        result.put("list", memberList);

        return result;
    }

    @Override
    public Map<String, Object> getAllTeams(Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;

        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(page);
        pageRequest.setSize(pageSize);
        GetTeamListRequest getTeamListRequest = new GetTeamListRequest();
        getTeamListRequest.setIsActive(true);
        Result<PageResult<TeamDTO>> activeTeamsResult = teamDataService.getTeamList(pageRequest, getTeamListRequest);

        if (activeTeamsResult == null || !activeTeamsResult.isSuccess() || activeTeamsResult.getData() == null) {
            throw new BusinessException("获取活跃团队失败");
        }
        PageResult<TeamDTO> pageResult = activeTeamsResult.getData();
        List<TeamDTO> activeTeams = pageResult.getRecords();
        // 将activeTeamsResult的数据转换为与dataLoader.getTeams()相同的格式
        List<Map<String, Object>> allTeams = new ArrayList<>();
        for (TeamDTO teamDTO : activeTeams) {
            Map<String, Object> team = new HashMap<>();
            team.put("teamId", teamDTO.getId());
            team.put("name", teamDTO.getName());
            team.put("description", teamDTO.getDescription());
            team.put("createdAt", teamDTO.getCreatedAt());
            team.put("isActive", teamDTO.getIsActive() != null ? teamDTO.getIsActive() : true);
            team.put("tags", teamDTO.getTags() != null ? teamDTO.getTags() : new ArrayList<>());

            // 设置默认值，因为TeamDTO中可能没有这些字段
            team.put("avatarUrl", "");
            team.put("privacy", teamDTO.getPrivacy());
            team.put("inviteSetting", teamDTO.getInviteSetting());

            // 添加默认成就数据
            Map<String, Object> achievements = new HashMap<>();
            achievements.put("articlesRecommended", 0);
            achievements.put("totalViews", 0L);
            achievements.put("totalLikes", 0L);
            achievements.put("totalFavorites", 0L);
            team.put("achievements", achievements);

            // 添加空的成员列表（如果需要成员信息，可以通过其他接口获取）
            PageRequest pageRequestMembers = new PageRequest();
            pageRequestMembers.setPage(1);
            pageRequestMembers.setSize(10);
            Result<PageResult<UserDTO>> teamMembersResult = userDataService.getTeamMembers(teamDTO.getId(), pageRequestMembers);
            if (teamMembersResult != null && teamMembersResult.isSuccess() && teamMembersResult.getData() != null) {
                team.put("members", teamMembersResult.getData().getRecords());
                team.put("memberCount", teamMembersResult.getData().getPagination().getTotalElements());
            }
            //team.put("members", new ArrayList<>());
            allTeams.add(team);
        }

        int total = allTeams.size();

        // 手动分页
        int fromIndex = (page - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        List<Map<String, Object>> pagedTeams = allTeams.subList(fromIndex, toIndex);

        Map<String, Object> result = new HashMap<>();
        result.put("page", pageResult.getPagination().getCurrentPage());
        result.put("pageSize", pageResult.getPagination().getPageSize());
        result.put("total", pageResult.getPagination().getTotalElements());
        result.put("list", allTeams);

        return result;
    }

    @Override
    public Map<String, Object> getTeamActivities(Long teamId, Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;

        // 模拟活动数据
        List<Map<String, Object>> activities = new ArrayList<>();

        Map<String, Object> activity1 = new HashMap<>();
        activity1.put("id", 1);
        activity1.put("type", "recommend");
        activity1.put("user", "魏立明");
        activity1.put("action", "推荐了内容");
        activity1.put("target", "专业邮件写作助手");
        activity1.put("time", "2024-01-15T10:30:00Z");
        activities.add(activity1);

        Map<String, Object> activity2 = new HashMap<>();
        activity2.put("id", 2);
        activity2.put("type", "join");
        activity2.put("user", "张三");
        activity2.put("action", "加入了团队");
        activity2.put("target", "");
        activity2.put("time", "2024-01-14T15:20:00Z");
        activities.add(activity2);

        int total = activities.size();

        // 手动分页
        int fromIndex = (page - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        List<Map<String, Object>> pagedActivities = activities.subList(fromIndex, toIndex);

        Map<String, Object> result = new HashMap<>();
        result.put("page", page);
        result.put("pageSize", pageSize);
        result.put("total", total);
        result.put("list", pagedActivities);

        return result;
    }

    @Override
    public Map<String, Object> getTeamContributors(Long teamId, Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;

        // 模拟贡献者数据
        List<Map<String, Object>> contributors = new ArrayList<>();

        Map<String, Object> contributor1 = new HashMap<>();
        contributor1.put("id", 1);
        contributor1.put("name", "魏立明");
        contributor1.put("avatar", "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face");
        contributor1.put("articlesCount", 8);
        contributor1.put("totalViews", 5200);
        contributor1.put("contributionScore", 95);
        contributors.add(contributor1);

        Map<String, Object> contributor2 = new HashMap<>();
        contributor2.put("id", 2);
        contributor2.put("name", "张三");
        contributor2.put("avatar", null);
        contributor2.put("articlesCount", 6);
        contributor2.put("totalViews", 3800);
        contributor2.put("contributionScore", 87);
        contributors.add(contributor2);

        int total = contributors.size();

        // 手动分页
        int fromIndex = (page - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        List<Map<String, Object>> pagedContributors = contributors.subList(fromIndex, toIndex);

        Map<String, Object> result = new HashMap<>();
        result.put("page", page);
        result.put("pageSize", pageSize);
        result.put("total", total);
        result.put("list", pagedContributors);

        return result;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> createTeam(Map<String, Object> teamData, Long creatorId) {
        // 验证必填字段
        String name = (String) teamData.get("name");
        String description = (String) teamData.get("description");

        if (name == null || name.trim().isEmpty()) {
            throw new BusinessException("团队名称不能为空");
        }
        if (description == null || description.trim().isEmpty()) {
            throw new BusinessException("团队描述不能为空");
        }

        // 将入参赋值到teamDTO
        TeamDTO teamDTO = new TeamDTO();
        teamDTO.setName(name.trim());
        teamDTO.setDescription(description.trim());
        teamDTO.setPrivacy((Integer) teamData.get("privacy"));
        teamDTO.setIsActive(true);
        if (teamData.get("avatarUrl") != null) {
            teamDTO.setAvatarUrl(teamData.get("avatarUrl").toString());
        }
        teamDTO.setCreatedBy(creatorId.toString());
        teamDTO.setCreatedAt(LocalDateTime.now());

        // 处理标签 - 根据TeamDTO文档，tags是List<String>类型
        List<String> tags = (List<String>) teamData.get("tags");
        teamDTO.setTags(tags != null ? tags : new ArrayList<>());

        // 注意：avatarUrl、privacy、inviteSetting等字段在TeamDTO中可能不存在
        // 这些字段可能需要在teamDataService.createTeam()的实现中处理
        // 或者通过其他方式传递给底层服务

        // 调用teamDataService创建团队
        Result<TeamDTO> createResult = teamDataService.createTeam(teamDTO);

        if (createResult == null || !createResult.isSuccess() || createResult.getData() == null) {
            throw new BusinessException("创建团队失败: " + (createResult != null ? createResult.getMessage() : "未知错误"));
        }

        TeamDTO createdTeam = createResult.getData();

        // 返回创建结果
        Map<String, Object> result = new HashMap<>();
        result.put("teamId", createdTeam.getId());
        result.put("name", createdTeam.getName());

        return result;
    }

    @Override
    @SuppressWarnings("unchecked")
    public boolean applyToJoinTeam(Long teamId, Long userId, String reason) {
        Map<String, Object> team = dataLoader.getTeamById(teamId);
        if (team == null) {
            throw new BusinessException("团队不存在");
        }

        // 检查是否已是成员
        List<Map<String, Object>> members = (List<Map<String, Object>>) team.get("members");
        if (members != null) {
            boolean isMember = members.stream()
                    .anyMatch(member -> Objects.equals(((Number) member.get("userId")).longValue(), userId));
            if (isMember) {
                throw new BusinessException("您已经是该团队的成员");
            }
        }

        // 这里应该创建申请记录，简化处理直接返回成功
        // 实际项目中需要实现申请审批流程
        return true;
    }

    @Override
    public boolean updateTeam(Long teamId, Map<String, Object> teamData, Long userId) {
        // TODO: 实现更新团队信息的逻辑
        // 1. 验证用户是否有权限更新团队（团队管理员或创建者）
        // 2. 验证团队数据的有效性
        // 3. 更新团队信息

        System.out.println("更新团队信息: teamId=" + teamId + ", userId=" + userId);
        return true;
    }

    @Override
    public boolean deleteTeam(Long teamId, Long userId) {
        // TODO: 实现删除团队的逻辑
        // 1. 验证用户是否有权限删除团队（团队创建者）
        // 2. 检查团队是否可以删除（是否有成员、内容等）
        // 3. 删除团队及相关数据

        System.out.println("删除团队: teamId=" + teamId + ", userId=" + userId);
        return true;
    }

    @Override
    public boolean inviteUsersToTeam(Long teamId, List<Long> userIds, Long inviterId, String message) {
        // TODO: 实现邀请用户加入团队的逻辑
        // 1. 验证邀请人是否有权限邀请（团队管理员）
        // 2. 验证被邀请用户是否存在且未加入团队
        // 3. 发送邀请通知
        // 4. 创建邀请记录

        System.out.println("邀请用户加入团队: teamId=" + teamId + ", userIds=" + userIds +
                ", inviterId=" + inviterId + ", message=" + message);
        return true;
    }

    @Override
    public boolean removeTeamMember(Long teamId, Long userId, Long operatorId) {
        // TODO: 实现移除团队成员的逻辑
        // 1. 验证操作人是否有权限移除成员（团队管理员）
        // 2. 验证被移除用户是否为团队成员
        // 3. 移除成员关系
        // 4. 发送通知

        System.out.println("移除团队成员: teamId=" + teamId + ", userId=" + userId + ", operatorId=" + operatorId);
        return true;
    }

    @Override
    public boolean starTeam(Long teamId, Long userId) {
        // 验证团队是否存在
        Map<String, Object> team = dataLoader.getTeamById(teamId);
        if (team == null) {
            throw new BusinessException("团队不存在");
        }

        // TODO: 实现收藏团队的逻辑
        // 1. 检查用户是否已经收藏了该团队
        // 2. 如果未收藏，则添加收藏关系
        // 3. 更新团队的收藏统计
        // 4. 记录用户行为日志

        System.out.println("收藏团队: teamId=" + teamId + ", userId=" + userId);

        // 简化实现：直接返回成功
        // 实际项目中需要操作数据库，维护用户-团队收藏关系表
        return true;
    }

    @Override
    public boolean unstarTeam(Long teamId, Long userId) {
        // 验证团队是否存在
        Map<String, Object> team = dataLoader.getTeamById(teamId);
        if (team == null) {
            throw new BusinessException("团队不存在");
        }

        // TODO: 实现取消收藏团队的逻辑
        // 1. 检查用户是否已经收藏了该团队
        // 2. 如果已收藏，则移除收藏关系
        // 3. 更新团队的收藏统计
        // 4. 记录用户行为日志

        System.out.println("取消收藏团队: teamId=" + teamId + ", userId=" + userId);

        // 简化实现：直接返回成功
        // 实际项目中需要操作数据库，维护用户-团队收藏关系表
        return true;
    }

    @Override
    public boolean updateMemberRole(Long teamId, Long userId, String role, Long operatorId) {
        // TODO: 实现更新成员角色的逻辑
        // 1. 验证操作人是否有权限更新角色（团队管理员或创建者）
        // 2. 验证新角色的有效性
        // 3. 更新成员角色
        // 4. 发送通知

        System.out.println("更新成员角色: teamId=" + teamId + ", userId=" + userId +
                ", role=" + role + ", operatorId=" + operatorId);
        return true;
    }
}
