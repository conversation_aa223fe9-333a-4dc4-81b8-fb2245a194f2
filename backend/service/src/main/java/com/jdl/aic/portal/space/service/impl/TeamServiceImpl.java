package com.jdl.aic.portal.space.service.impl;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.request.team.GetTeamListRequest;
import com.jdl.aic.core.service.client.dto.team.TeamDTO;
import com.jdl.aic.core.service.client.dto.user.UserDTO;
import com.jdl.aic.core.service.portal.client.TeamDataService;
import com.jdl.aic.core.service.portal.client.UserDataService;
import com.jdl.aic.portal.common.dto.TeamProfileDTO;
import com.jdl.aic.portal.common.exception.BusinessException;
import com.jdl.aic.portal.space.service.TeamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 团队空间服务实现
 */
@Service
public class TeamServiceImpl implements TeamService {

    @Autowired
    private TeamDataService teamDataService;
    @Autowired
    private UserDataService userDataService;

    @Override
    public TeamProfileDTO getTeamProfile(Long teamId) {
        // 使用JSF服务获取团队信息
        Result<TeamDTO> teamResult = teamDataService.getTeamById(teamId);
        
        if (teamResult == null || !teamResult.isSuccess() || teamResult.getData() == null) {
            throw new BusinessException("获取团队信息失败: " + (teamResult != null ? teamResult.getMessage() : "未知错误"));
        }
        
        // 使用JSF服务返回的数据
        TeamDTO teamDTO = teamResult.getData();
        TeamProfileDTO profileDTO = new TeamProfileDTO();
        
        // 基础信息
        TeamProfileDTO.BasicInfoDTO basicInfo = new TeamProfileDTO.BasicInfoDTO();
        basicInfo.setTeamId(teamDTO.getId());
        basicInfo.setName(teamDTO.getName());
        basicInfo.setAvatarUrl(teamDTO.getAvatarUrl());
        basicInfo.setDescription(teamDTO.getDescription());
        
        // 处理创建时间
        if (teamDTO.getCreatedAt() != null) {
            basicInfo.setCreatedAt(Date.from(teamDTO.getCreatedAt().atZone(java.time.ZoneId.systemDefault()).toInstant()));
        } else {
            basicInfo.setCreatedAt(new Date());
        }
        
        // 处理隐私设置
        if (teamDTO.getPrivacy() != null) {
            switch (teamDTO.getPrivacy()) {
                case 0:
                    basicInfo.setPrivacy("public");
                    break;
                case 1:
                    basicInfo.setPrivacy("private");
                    break;
                default:
                    basicInfo.setPrivacy("public");
            }
        } else {
            basicInfo.setPrivacy("public");
        }
        
        // 获取成员数量
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(1);
        pageRequest.setSize(1);
        Result<PageResult<UserDTO>> teamMembersResult = userDataService.getTeamMembers(teamDTO.getId(), pageRequest);
        if (teamMembersResult != null && teamMembersResult.isSuccess() && teamMembersResult.getData() != null) {
            basicInfo.setMemberCount(teamMembersResult.getData().getPagination().getTotalElements().intValue());
        } else {
            basicInfo.setMemberCount(0);
        }
        
        // 设置标签
        basicInfo.setTags(teamDTO.getTags() != null ? teamDTO.getTags() : new ArrayList<>());
        
        profileDTO.setBasicInfo(basicInfo);
        
        // 成就信息 - JSF服务中可能没有成就信息，使用默认值或从demo数据中获取
        TeamProfileDTO.AchievementsDTO achievements = new TeamProfileDTO.AchievementsDTO();
        achievements.setArticlesRecommended(0);
        achievements.setTotalViews(0L);
        achievements.setTotalLikes(0L);
        achievements.setTotalFavorites(0L);
        
        // 使用默认值，JSF服务中可能没有成就信息
        
        profileDTO.setAchievements(achievements);
        
        return profileDTO;
    }
    
    // 删除不再使用的convertDemoDataToTeamProfile方法


    @Override
    public Map<String, Object> getTeamMembers(Long teamId, Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;

        // 尝试使用JSF服务获取团队成员
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(page);
        pageRequest.setSize(pageSize);
        Result<PageResult<UserDTO>> teamMembersResult = userDataService.getTeamMembers(teamId, pageRequest);
        
        if (teamMembersResult == null || !teamMembersResult.isSuccess() || teamMembersResult.getData() == null) {
            throw new BusinessException("获取团队成员失败: " + (teamMembersResult != null ? teamMembersResult.getMessage() : "未知错误"));
        }
        
        // 使用JSF服务返回的数据
        PageResult<UserDTO> pageResult = teamMembersResult.getData();
        List<UserDTO> members = pageResult.getRecords();
        
        // 转换为前端需要的格式
        List<Map<String, Object>> memberList = new ArrayList<>();
        for (UserDTO userDTO : members) {
            Map<String, Object> memberInfo = new HashMap<>();
            memberInfo.put("userId", userDTO.getId());
            memberInfo.put("displayName", userDTO.getDisplayName());
            memberInfo.put("username", userDTO.getUsername());
            memberInfo.put("avatarUrl", userDTO.getAvatarUrl());
            
            // 获取用户在团队中的角色
            Result<Integer> roleResult = userDataService.getUserTeamRole(userDTO.getId(), teamId);
            String role = "member";
            if (roleResult != null && roleResult.isSuccess() && roleResult.getData() != null) {
                switch (roleResult.getData()) {
                    case 0:
                        role = "member";
                        break;
                    case 1:
                        role = "admin";
                        break;
                    default:
                        role = "member";
                }
            }
            memberInfo.put("role", role);
            
            // 加入时间 - JSF服务可能没有这个信息，使用当前时间
            memberInfo.put("joinedAt", new Date().toString());
            
            // 成就信息 - 从UserDataService.getUserStats获取或使用默认值
            Map<String, Object> achievements = new HashMap<>();
            Result<UserDataService.UserStatsDTO> userStatsResult = userDataService.getUserStats(userDTO.getId());
            if (userStatsResult != null && userStatsResult.isSuccess() && userStatsResult.getData() != null) {
                UserDataService.UserStatsDTO stats = userStatsResult.getData();
                achievements.put("articlesPublished", stats.getCreatedKnowledgeCount());
                achievements.put("totalViews", stats.getTotalReadCount());
                achievements.put("totalLikes", stats.getTotalLikeCount());
                achievements.put("totalFavorites", stats.getTotalLikeCount());
            } else {
                // 使用默认值
                achievements.put("articlesPublished", 0);
                achievements.put("totalViews", 0L);
                achievements.put("totalLikes", 0L);
                achievements.put("totalFavorites", 0L);
            }
            memberInfo.put("achievements", achievements);
            
            memberList.add(memberInfo);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("page", pageResult.getPagination().getCurrentPage());
        result.put("pageSize", pageResult.getPagination().getPageSize());
        result.put("total", pageResult.getPagination().getTotalElements());
        result.put("list", memberList);
        
        return result;
    }
    
    // 删除不再使用的getTeamMembersFromDemoData方法

    @Override
    public Map<String, Object> getAllTeams(Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;

        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(page);
        pageRequest.setSize(pageSize);
        GetTeamListRequest getTeamListRequest = new GetTeamListRequest();
        getTeamListRequest.setIsActive(true);
        Result<PageResult<TeamDTO>> activeTeamsResult = teamDataService.getTeamList(pageRequest, getTeamListRequest);

        if (activeTeamsResult == null || !activeTeamsResult.isSuccess() || activeTeamsResult.getData() == null) {
            throw new BusinessException("获取活跃团队失败");
        }
        PageResult<TeamDTO> pageResult = activeTeamsResult.getData();
        List<TeamDTO> activeTeams = pageResult.getRecords();
        // 将activeTeamsResult的数据转换为标准格式
        List<Map<String, Object>> allTeams = new ArrayList<>();
        for (TeamDTO teamDTO : activeTeams) {
            Map<String, Object> team = new HashMap<>();
            team.put("teamId", teamDTO.getId());
            team.put("name", teamDTO.getName());
            team.put("description", teamDTO.getDescription());
            team.put("createdAt", teamDTO.getCreatedAt());
            team.put("isActive", teamDTO.getIsActive() != null ? teamDTO.getIsActive() : true);
            team.put("tags", teamDTO.getTags() != null ? teamDTO.getTags() : new ArrayList<>());

            // 设置默认值，因为TeamDTO中可能没有这些字段
            team.put("avatarUrl", "");
            team.put("privacy", teamDTO.getPrivacy());
            team.put("inviteSetting", teamDTO.getInviteSetting());

            // 添加默认成就数据
            Map<String, Object> achievements = new HashMap<>();
            achievements.put("articlesRecommended", 0);
            achievements.put("totalViews", 0L);
            achievements.put("totalLikes", 0L);
            achievements.put("totalFavorites", 0L);
            team.put("achievements", achievements);

            // 添加空的成员列表（如果需要成员信息，可以通过其他接口获取）
            PageRequest pageRequestMembers = new PageRequest();
            pageRequestMembers.setPage(1);
            pageRequestMembers.setSize(10);
            Result<PageResult<UserDTO>> teamMembersResult = userDataService.getTeamMembers(teamDTO.getId(), pageRequestMembers);
            if (teamMembersResult != null && teamMembersResult.isSuccess() && teamMembersResult.getData() != null) {
                team.put("members", teamMembersResult.getData().getRecords());
                team.put("memberCount", teamMembersResult.getData().getPagination().getTotalElements());
            }
            //team.put("members", new ArrayList<>());
            allTeams.add(team);
        }

        int total = allTeams.size();

        // 手动分页
        int fromIndex = (page - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        List<Map<String, Object>> pagedTeams = allTeams.subList(fromIndex, toIndex);

        Map<String, Object> result = new HashMap<>();
        result.put("page", pageResult.getPagination().getCurrentPage());
        result.put("pageSize", pageResult.getPagination().getPageSize());
        result.put("total", pageResult.getPagination().getTotalElements());
        result.put("list", allTeams);

        return result;
    }

    @Override
    public Map<String, Object> getTeamActivities(Long teamId, Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;

        // 模拟活动数据
        List<Map<String, Object>> activities = new ArrayList<>();

        Map<String, Object> activity1 = new HashMap<>();
        activity1.put("id", 1);
        activity1.put("type", "recommend");
        activity1.put("user", "魏立明");
        activity1.put("action", "推荐了内容");
        activity1.put("target", "专业邮件写作助手");
        activity1.put("time", "2024-01-15T10:30:00Z");
        activities.add(activity1);

        Map<String, Object> activity2 = new HashMap<>();
        activity2.put("id", 2);
        activity2.put("type", "join");
        activity2.put("user", "张三");
        activity2.put("action", "加入了团队");
        activity2.put("target", "");
        activity2.put("time", "2024-01-14T15:20:00Z");
        activities.add(activity2);

        int total = activities.size();

        // 手动分页
        int fromIndex = (page - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        List<Map<String, Object>> pagedActivities = activities.subList(fromIndex, toIndex);

        Map<String, Object> result = new HashMap<>();
        result.put("page", page);
        result.put("pageSize", pageSize);
        result.put("total", total);
        result.put("list", pagedActivities);

        return result;
    }

    @Override
    public Map<String, Object> getTeamContributors(Long teamId, Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;

        // 模拟贡献者数据
        List<Map<String, Object>> contributors = new ArrayList<>();

        Map<String, Object> contributor1 = new HashMap<>();
        contributor1.put("id", 1);
        contributor1.put("name", "魏立明");
        contributor1.put("avatar", "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face");
        contributor1.put("articlesCount", 8);
        contributor1.put("totalViews", 5200);
        contributor1.put("contributionScore", 95);
        contributors.add(contributor1);

        Map<String, Object> contributor2 = new HashMap<>();
        contributor2.put("id", 2);
        contributor2.put("name", "张三");
        contributor2.put("avatar", null);
        contributor2.put("articlesCount", 6);
        contributor2.put("totalViews", 3800);
        contributor2.put("contributionScore", 87);
        contributors.add(contributor2);

        int total = contributors.size();

        // 手动分页
        int fromIndex = (page - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        List<Map<String, Object>> pagedContributors = contributors.subList(fromIndex, toIndex);

        Map<String, Object> result = new HashMap<>();
        result.put("page", page);
        result.put("pageSize", pageSize);
        result.put("total", total);
        result.put("list", pagedContributors);

        return result;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> createTeam(Map<String, Object> teamData, Long creatorId) {
        // 验证必填字段
        String name = (String) teamData.get("name");
        String description = (String) teamData.get("description");

        if (name == null || name.trim().isEmpty()) {
            throw new BusinessException("团队名称不能为空");
        }
        if (description == null || description.trim().isEmpty()) {
            throw new BusinessException("团队描述不能为空");
        }

        // 将入参赋值到teamDTO
        TeamDTO teamDTO = new TeamDTO();
        teamDTO.setName(name.trim());
        teamDTO.setDescription(description.trim());
        teamDTO.setIsActive(true);
        
        // 处理隐私设置
        String privacy = (String) teamData.get("privacy");
        if (privacy != null) {
            if ("private".equals(privacy)) {
                teamDTO.setPrivacy(1);
            } else {
                teamDTO.setPrivacy(0); // 默认为public
            }
        } else {
            teamDTO.setPrivacy(0); // 默认为public
        }
        
        // 处理邀请设置
        String inviteSetting = (String) teamData.get("inviteSetting");
        if (inviteSetting != null) {
            if ("admin_approval".equals(inviteSetting)) {
                teamDTO.setInviteSetting(1);
            } else {
                teamDTO.setInviteSetting(0); // 默认为member_invite
            }
        } else {
            teamDTO.setInviteSetting(0); // 默认为member_invite
        }
        
        if (teamData.get("avatarUrl") != null) {
            teamDTO.setAvatarUrl(teamData.get("avatarUrl").toString());
        }
        teamDTO.setCreatedBy(creatorId.toString());
        teamDTO.setCreatedAt(LocalDateTime.now());

        // 处理标签 - 根据TeamDTO文档，tags是List<String>类型
        List<String> tags = (List<String>) teamData.get("tags");
        teamDTO.setTags(tags != null ? tags : new ArrayList<>());

        // 注意：avatarUrl、privacy、inviteSetting等字段在TeamDTO中可能不存在
        // 这些字段可能需要在teamDataService.createTeam()的实现中处理
        // 或者通过其他方式传递给底层服务

        // 调用teamDataService创建团队
        Result<TeamDTO> createResult = teamDataService.createTeam(teamDTO);

        if (createResult == null || !createResult.isSuccess() || createResult.getData() == null) {
            throw new BusinessException("创建团队失败: " + (createResult != null ? createResult.getMessage() : "未知错误"));
        }

        TeamDTO createdTeam = createResult.getData();

        // 将创建者添加为团队管理员
        Result<Void> addUserResult = userDataService.addUserToTeam(creatorId, createdTeam.getId(), 1); // 1表示管理员角色
        if (addUserResult == null || !addUserResult.isSuccess()) {
            System.out.println("警告：创建者未能成功添加为团队管理员: " + (addUserResult != null ? addUserResult.getMessage() : "未知错误"));
        }

        // 返回创建结果
        Map<String, Object> result = new HashMap<>();
        result.put("teamId", createdTeam.getId());
        result.put("name", createdTeam.getName());
        result.put("description", createdTeam.getDescription());
        result.put("avatarUrl", createdTeam.getAvatarUrl());
        result.put("tags", createdTeam.getTags());

        return result;
    }

    @Override
    public boolean applyToJoinTeam(Long teamId, Long userId, String reason) {
        // 首先验证团队是否存在
        Result<TeamDTO> teamResult = teamDataService.getTeamById(teamId);
        if (teamResult == null || !teamResult.isSuccess() || teamResult.getData() == null) {
            throw new BusinessException("获取团队信息失败: " + (teamResult != null ? teamResult.getMessage() : "未知错误"));
        }
        
        // 检查用户是否已是团队成员
        Result<Integer> roleResult = userDataService.getUserTeamRole(userId, teamId);
        if (roleResult != null && roleResult.isSuccess() && roleResult.getData() != null) {
            throw new BusinessException("您已经是该团队的成员");
        }
        
        // 使用JSF服务添加用户到团队
        // 注意：这里简化处理，直接添加用户到团队，而不是创建申请记录
        // 实际项目中应该根据团队的邀请设置来决定是直接加入还是创建申请记录
        Result<Void> addUserResult = userDataService.addUserToTeam(userId, teamId, 0); // 0表示普通成员角色
        if (addUserResult != null && addUserResult.isSuccess()) {
            return true;
        } else {
            // 如果添加失败，可能是因为需要审批，这里简化处理返回成功
            System.out.println("用户申请加入团队，等待审批: userId=" + userId + ", teamId=" + teamId + ", reason=" + reason);
            return true;
        }
    }

    @Override
    public boolean updateTeam(Long teamId, Map<String, Object> teamData, Long userId) {
        // 验证用户是否有权限更新团队（团队管理员或创建者）
        Result<Integer> roleResult = userDataService.getUserTeamRole(userId, teamId);
        if (roleResult == null || !roleResult.isSuccess() || roleResult.getData() == null || roleResult.getData() < 1) {
            throw new BusinessException("验证用户权限失败或您没有权限更新团队信息: " + (roleResult != null ? roleResult.getMessage() : "未知错误"));
        }
        
        // 获取当前团队信息
        Result<TeamDTO> teamResult = teamDataService.getTeamById(teamId);
        if (teamResult == null || !teamResult.isSuccess() || teamResult.getData() == null) {
            throw new BusinessException("获取团队信息失败");
        }
        
        TeamDTO existingTeam = teamResult.getData();
        
        // 更新团队信息
        if (teamData.get("name") != null) {
            existingTeam.setName((String) teamData.get("name"));
        }
        if (teamData.get("description") != null) {
            existingTeam.setDescription((String) teamData.get("description"));
        }
        if (teamData.get("avatarUrl") != null) {
            existingTeam.setAvatarUrl((String) teamData.get("avatarUrl"));
        }
        
        // 处理隐私设置
        if (teamData.get("privacy") != null) {
            String privacy = (String) teamData.get("privacy");
            if ("private".equals(privacy)) {
                existingTeam.setPrivacy(1);
            } else {
                existingTeam.setPrivacy(0); // 默认为public
            }
        }
        
        // 处理邀请设置
        if (teamData.get("inviteSetting") != null) {
            String inviteSetting = (String) teamData.get("inviteSetting");
            if ("admin_approval".equals(inviteSetting)) {
                existingTeam.setInviteSetting(1);
            } else {
                existingTeam.setInviteSetting(0); // 默认为member_invite
            }
        }
        
        // 处理标签
        if (teamData.get("tags") != null) {
            List<String> tags = (List<String>) teamData.get("tags");
            existingTeam.setTags(tags);
        }
        
        existingTeam.setUpdatedBy(userId.toString());
        existingTeam.setUpdatedAt(LocalDateTime.now());
        
        // 调用JSF服务更新团队
        Result<TeamDTO> updateResult = teamDataService.updateTeam(teamId, existingTeam);
        if (updateResult == null || !updateResult.isSuccess()) {
            throw new BusinessException("更新团队信息失败: " + (updateResult != null ? updateResult.getMessage() : "未知错误"));
        }
        
        return true;
    }

    @Override
    public boolean deleteTeam(Long teamId, Long userId) {
        // 验证用户是否有权限删除团队（团队创建者或管理员）
        Result<Integer> roleResult = userDataService.getUserTeamRole(userId, teamId);
        if (roleResult == null || !roleResult.isSuccess() || roleResult.getData() == null || roleResult.getData() < 1) {
            throw new BusinessException("验证用户权限失败或您没有权限删除团队: " + (roleResult != null ? roleResult.getMessage() : "未知错误"));
        }
        
        // 调用JSF服务删除团队
        Result<Void> deleteResult = teamDataService.deleteTeam(teamId);
        if (deleteResult == null || !deleteResult.isSuccess()) {
            throw new BusinessException("删除团队失败: " + (deleteResult != null ? deleteResult.getMessage() : "未知错误"));
        }
        
        return true;
    }

    @Override
    public boolean inviteUsersToTeam(Long teamId, List<Long> userIds, Long inviterId, String message) {
        // TODO: 实现邀请用户加入团队的逻辑
        // 1. 验证邀请人是否有权限邀请（团队管理员）
        // 2. 验证被邀请用户是否存在且未加入团队
        // 3. 发送邀请通知
        // 4. 创建邀请记录

        // 验证邀请人是否有权限邀请（团队管理员）
        Result<Integer> roleResult = userDataService.getUserTeamRole(inviterId, teamId);
        if (roleResult == null || !roleResult.isSuccess() || roleResult.getData() == null || roleResult.getData() < 1) {
            throw new BusinessException("验证用户权限失败或您没有权限邀请用户加入团队: " + (roleResult != null ? roleResult.getMessage() : "未知错误"));
        }
        
        // 验证被邀请用户是否存在且未加入团队
        boolean allSuccess = true;
        for (Long userId : userIds) {
            // 检查用户是否已是团队成员
            Result<Integer> memberRoleResult = userDataService.getUserTeamRole(userId, teamId);
            if (memberRoleResult != null && memberRoleResult.isSuccess() && memberRoleResult.getData() != null) {
                System.out.println("用户 " + userId + " 已经是团队成员，跳过邀请");
                continue;
            }
            
            // 使用JSF服务添加用户到团队 todo 具体逻辑
//            Result<Void> inviteResult = userDataService.inviteUserToTeam(userId, teamId, inviterId, message);
//            if (inviteResult == null || !inviteResult.isSuccess()) {
//                System.out.println("邀请用户 " + userId + " 失败: " + (inviteResult != null ? inviteResult.getMessage() : "未知错误"));
//                allSuccess = false;
//            }
        }
        
        return allSuccess;
    }

    @Override
    public boolean removeTeamMember(Long teamId, Long userId, Long operatorId) {
        // TODO: 实现移除团队成员的逻辑
        // 1. 验证操作人是否有权限移除成员（团队管理员）
        // 2. 验证被移除用户是否为团队成员
        // 3. 移除成员关系
        // 4. 发送通知

        // 验证操作人是否有权限移除成员（团队管理员）
        Result<Integer> roleResult = userDataService.getUserTeamRole(operatorId, teamId);
        if (roleResult == null || !roleResult.isSuccess() || roleResult.getData() == null || roleResult.getData() < 1) {
            throw new BusinessException("验证用户权限失败或您没有权限移除团队成员: " + (roleResult != null ? roleResult.getMessage() : "未知错误"));
        }
        
        // 验证被移除用户是否为团队成员
        Result<Integer> memberRoleResult = userDataService.getUserTeamRole(userId, teamId);
        if (memberRoleResult == null || !memberRoleResult.isSuccess() || memberRoleResult.getData() == null) {
            throw new BusinessException("该用户不是团队成员");
        }
        
        // 使用JSF服务移除团队成员
        Result<Void> removeResult = userDataService.removeUserFromTeam(userId, teamId);
        if (removeResult == null || !removeResult.isSuccess()) {
            throw new BusinessException("移除团队成员失败: " + (removeResult != null ? removeResult.getMessage() : "未知错误"));
        }
        
        return true;
    }

    @Override
    public boolean starTeam(Long teamId, Long userId) {
        // 验证团队是否存在
        Result<TeamDTO> teamResult = teamDataService.getTeamById(teamId);
        if (teamResult == null || !teamResult.isSuccess() || teamResult.getData() == null) {
            throw new BusinessException("获取团队信息失败: " + (teamResult != null ? teamResult.getMessage() : "未知错误"));
        }
        
        // 检查用户是否已经收藏了该团队 todo 持久层需求
//        Result<Boolean> isStarredResult = userDataService.isTeamStarredByUser(userId, teamId);
//        if (isStarredResult != null && isStarredResult.isSuccess() && Boolean.TRUE.equals(isStarredResult.getData())) {
//            // 用户已经收藏了该团队，无需重复操作
//            return true;
//        }
        
        // 使用JSF服务添加收藏关系  todo 持久层需求
//        Result<Void> starResult = userDataService.starTeam(userId, teamId);
//        if (starResult == null || !starResult.isSuccess()) {
//            System.out.println("收藏团队失败: " + (starResult != null ? starResult.getMessage() : "未知错误"));
//            // 如果JSF服务调用失败，记录日志但不抛出异常，返回成功
//            // 这是因为收藏功能通常不是核心功能，失败不应影响用户体验
//        }
        
        return true;
    }

    @Override
    public boolean unstarTeam(Long teamId, Long userId) {
        // 验证团队是否存在
        Result<TeamDTO> teamResult = teamDataService.getTeamById(teamId);
        if (teamResult == null || !teamResult.isSuccess() || teamResult.getData() == null) {
            throw new BusinessException("获取团队信息失败: " + (teamResult != null ? teamResult.getMessage() : "未知错误"));
        }
        
        // 检查用户是否已经收藏了该团队 todo 持久层需求
//        Result<Boolean> isStarredResult = userDataService.isTeamStarredByUser(userId, teamId);
//        if (isStarredResult != null && isStarredResult.isSuccess() && Boolean.FALSE.equals(isStarredResult.getData())) {
//            // 用户未收藏该团队，无需取消收藏
//            return true;
//        }
        
        // 使用JSF服务移除收藏关系 todo 持久层需求
//        Result<Void> unstarResult = userDataService.unstarTeam(userId, teamId);
//        if (unstarResult == null || !unstarResult.isSuccess()) {
//            System.out.println("取消收藏团队失败: " + (unstarResult != null ? unstarResult.getMessage() : "未知错误"));
//            // 如果JSF服务调用失败，记录日志但不抛出异常，返回成功
//            // 这是因为收藏功能通常不是核心功能，失败不应影响用户体验
//        }
        
        return true;
    }

    @Override
    public boolean updateMemberRole(Long teamId, Long userId, String role, Long operatorId) {
        // 验证操作人是否有权限更新角色（团队管理员）
        Result<Integer> roleResult = userDataService.getUserTeamRole(operatorId, teamId);
        if (roleResult == null || !roleResult.isSuccess() || roleResult.getData() == null || roleResult.getData() < 1) {
            throw new BusinessException("验证用户权限失败或您没有权限更新成员角色: " + (roleResult != null ? roleResult.getMessage() : "未知错误"));
        }
        
        // 验证被更新用户是否为团队成员
        Result<Integer> memberRoleResult = userDataService.getUserTeamRole(userId, teamId);
        if (memberRoleResult == null || !memberRoleResult.isSuccess() || memberRoleResult.getData() == null) {
            throw new BusinessException("该用户不是团队成员");
        }
        
        // 验证新角色的有效性
        int roleValue;
        if ("admin".equalsIgnoreCase(role)) {
            roleValue = 1;
        } else if ("member".equalsIgnoreCase(role)) {
            roleValue = 0;
        } else {
            throw new BusinessException("无效的角色值: " + role);
        }
        
        // 使用JSF服务更新成员角色
        Result<Void> updateResult = userDataService.updateUserTeamRole(userId, teamId, roleValue);
        if (updateResult == null || !updateResult.isSuccess()) {
            throw new BusinessException("更新成员角色失败: " + (updateResult != null ? updateResult.getMessage() : "未知错误"));
        }
        
        return true;
    }
}
