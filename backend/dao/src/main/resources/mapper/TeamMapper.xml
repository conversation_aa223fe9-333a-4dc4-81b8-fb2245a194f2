<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.portal.dao.mapper.TeamMapper">
    
    <resultMap id="BaseResultMap" type="com.jdl.aic.portal.space.dto.Team">
        <id column="team_id" property="teamId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="avatar_url" property="avatarUrl" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="privacy" property="privacy" jdbcType="VARCHAR"/>
        <result column="invite_setting" property="inviteSetting" jdbcType="VARCHAR"/>
        <result column="tags" property="tags" jdbcType="VARCHAR"/>
        <result column="member_count" property="memberCount" jdbcType="INTEGER"/>
        <result column="articles_recommended" property="articlesRecommended" jdbcType="INTEGER"/>
        <result column="total_views" property="totalViews" jdbcType="BIGINT"/>
        <result column="total_likes" property="totalLikes" jdbcType="BIGINT"/>
        <result column="total_favorites" property="totalFavorites" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        team_id, name, avatar_url, description, created_at, privacy, invite_setting, tags, member_count,
        articles_recommended, total_views, total_likes, total_favorites, create_time, update_time
    </sql>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM team 
        WHERE team_id = #{teamId}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM team
        ORDER BY create_time DESC
    </select>

    <select id="selectByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT t.<include refid="Base_Column_List"/>
        FROM team t
        INNER JOIN team_member tm ON t.team_id = tm.team_id
        WHERE tm.user_id = #{userId}
        ORDER BY tm.joined_at DESC
    </select>

    <insert id="insert" parameterType="com.jdl.aic.portal.space.dto.Team" useGeneratedKeys="true" keyProperty="teamId">
        INSERT INTO team (name, avatar_url, description, created_at, privacy, invite_setting, tags, member_count,
                         articles_recommended, total_views, total_likes, total_favorites, create_time, update_time)
        VALUES (#{name}, #{avatarUrl}, #{description}, #{createdAt}, #{privacy}, #{inviteSetting}, #{tags}, #{memberCount},
                #{articlesRecommended}, #{totalViews}, #{totalLikes}, #{totalFavorites}, NOW(), NOW())
    </insert>

    <update id="updateById" parameterType="com.jdl.aic.portal.space.dto.Team">
        UPDATE team 
        SET name = #{name},
            avatar_url = #{avatarUrl},
            description = #{description},
            privacy = #{privacy},
            invite_setting = #{inviteSetting},
            tags = #{tags},
            update_time = NOW()
        WHERE team_id = #{teamId}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM team WHERE team_id = #{teamId}
    </delete>

    <update id="updateMemberCount">
        UPDATE team 
        SET member_count = #{memberCount},
            update_time = NOW()
        WHERE team_id = #{teamId}
    </update>

    <update id="updateStatistics">
        UPDATE team 
        SET articles_recommended = #{articlesRecommended},
            total_views = #{totalViews},
            total_likes = #{totalLikes},
            total_favorites = #{totalFavorites},
            update_time = NOW()
        WHERE team_id = #{teamId}
    </update>

</mapper>
