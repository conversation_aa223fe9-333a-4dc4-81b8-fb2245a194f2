package com.jdl.aic.portal.dao.entity;

import java.util.Date;

/**
 * 用户学习信息实体类
 */
public class UserLearning {
    private Long id;
    private Long userId;
    private Double totalLearningHours;
    private Integer coursesCompleted;
    private Integer consecutiveLearningDays;
    private String inProgressData; // JSON字符串存储进行中的学习数据
    private String weeklyGoalsData; // JSON字符串存储周目标数据
    private Date createTime;
    private Date updateTime;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Double getTotalLearningHours() {
        return totalLearningHours;
    }

    public void setTotalLearningHours(Double totalLearningHours) {
        this.totalLearningHours = totalLearningHours;
    }

    public Integer getCoursesCompleted() {
        return coursesCompleted;
    }

    public void setCoursesCompleted(Integer coursesCompleted) {
        this.coursesCompleted = coursesCompleted;
    }

    public Integer getConsecutiveLearningDays() {
        return consecutiveLearningDays;
    }

    public void setConsecutiveLearningDays(Integer consecutiveLearningDays) {
        this.consecutiveLearningDays = consecutiveLearningDays;
    }

    public String getInProgressData() {
        return inProgressData;
    }

    public void setInProgressData(String inProgressData) {
        this.inProgressData = inProgressData;
    }

    public String getWeeklyGoalsData() {
        return weeklyGoalsData;
    }

    public void setWeeklyGoalsData(String weeklyGoalsData) {
        this.weeklyGoalsData = weeklyGoalsData;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
