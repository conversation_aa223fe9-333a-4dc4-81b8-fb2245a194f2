package com.jdl.aic.portal.dao.mapper;

import com.jdl.aic.portal.dao.entity.UserLearning;
import org.apache.ibatis.annotations.Param;

/**
 * 用户学习信息数据访问接口
 */
public interface UserLearningMapper {
    
    /**
     * 根据用户ID查询学习信息
     */
    UserLearning selectByUserId(@Param("userId") Long userId);
    
    /**
     * 插入用户学习信息
     */
    int insert(UserLearning userLearning);
    
    /**
     * 更新用户学习信息
     */
    int updateByUserId(UserLearning userLearning);
    
    /**
     * 删除用户学习信息
     */
    int deleteByUserId(@Param("userId") Long userId);
}
