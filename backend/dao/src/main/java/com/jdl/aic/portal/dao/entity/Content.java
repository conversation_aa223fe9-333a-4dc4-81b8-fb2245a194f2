package com.jdl.aic.portal.dao.entity;

import java.util.Date;

/**
 * 内容实体类
 */
public class Content {
    private Long id;
    private String title;
    private String description;
    private String knowledgeTypeCode; // prompt, article, tool, course
    private Long authorId;
    private String authorName;
    private String content; // 内容详情
    private String tags; // JSON字符串存储标签数组
    
    // 统计字段
    private Long views;
    private Long likes;
    private Long favorites;
    
    private Date createdAt;
    private Date createTime;
    private Date updateTime;

    // Get<PERSON> and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getKnowledgeTypeCode() {
        return knowledgeTypeCode;
    }

    public void setKnowledgeTypeCode(String knowledgeTypeCode) {
        this.knowledgeTypeCode = knowledgeTypeCode;
    }

    public Long getAuthorId() {
        return authorId;
    }

    public void setAuthorId(Long authorId) {
        this.authorId = authorId;
    }

    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public Long getViews() {
        return views;
    }

    public void setViews(Long views) {
        this.views = views;
    }

    public Long getLikes() {
        return likes;
    }

    public void setLikes(Long likes) {
        this.likes = likes;
    }

    public Long getFavorites() {
        return favorites;
    }

    public void setFavorites(Long favorites) {
        this.favorites = favorites;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
