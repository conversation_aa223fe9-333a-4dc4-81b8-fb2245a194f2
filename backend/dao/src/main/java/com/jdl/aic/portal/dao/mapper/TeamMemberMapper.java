package com.jdl.aic.portal.dao.mapper;

import com.jdl.aic.portal.dao.entity.TeamMember;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 团队成员数据访问接口
 */
public interface TeamMemberMapper {
    
    /**
     * 根据团队ID查询成员列表
     */
    List<TeamMember> selectByTeamId(@Param("teamId") Long teamId);
    
    /**
     * 根据用户ID查询用户加入的团队
     */
    List<TeamMember> selectByUserId(@Param("userId") Long userId);
    
    /**
     * 根据团队ID和用户ID查询成员关系
     */
    TeamMember selectByTeamIdAndUserId(@Param("teamId") Long teamId, @Param("userId") Long userId);
    
    /**
     * 插入团队成员
     */
    int insert(TeamMember teamMember);
    
    /**
     * 更新成员角色
     */
    int updateRole(@Param("teamId") Long teamId, @Param("userId") Long userId, @Param("role") String role);
    
    /**
     * 删除团队成员
     */
    int deleteByTeamIdAndUserId(@Param("teamId") Long teamId, @Param("userId") Long userId);
    
    /**
     * 统计团队成员数量
     */
    int countByTeamId(@Param("teamId") Long teamId);
}
