package com.jdl.aic.portal.dao.mapper;

import com.jdl.aic.portal.dao.entity.TeamRecommendation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 团队推荐数据访问接口
 */
public interface TeamRecommendationMapper {
    
    /**
     * 根据团队ID查询推荐列表
     */
    List<TeamRecommendation> selectByTeamId(@Param("teamId") Long teamId, 
                                           @Param("offset") Integer offset, 
                                           @Param("limit") Integer limit);
    
    /**
     * 统计团队推荐数量
     */
    int countByTeamId(@Param("teamId") Long teamId);
    
    /**
     * 根据推荐人ID查询推荐列表
     */
    List<TeamRecommendation> selectByRecommenderId(@Param("recommenderId") Long recommenderId);
    
    /**
     * 插入推荐
     */
    int insert(TeamRecommendation recommendation);
    
    /**
     * 删除推荐
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据团队ID和内容ID查询推荐
     */
    TeamRecommendation selectByTeamIdAndContentId(@Param("teamId") Long teamId, @Param("contentId") Long contentId);
}
