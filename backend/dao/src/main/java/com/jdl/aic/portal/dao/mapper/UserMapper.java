package com.jdl.aic.portal.dao.mapper;

import com.jdl.aic.portal.dao.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserMapper {
    
    int insert(User user);
    
    int deleteById(@Param("id") Long id);
    
    int updateById(User user);
    
    User selectById(@Param("id") Long id);
    
    User selectByUsername(@Param("username") String username);
    
    List<User> selectAll();
    
    User selectByEmail(@Param("email") String email);
    
    User selectByProviderAndProviderId(@Param("provider") String provider, @Param("providerId") String providerId);
    
    int updateLastLoginTime(@Param("id") Long id);

    /**
     * 更新用户资料信息
     */
    int updateProfile(User user);

    /**
     * 更新用户统计数据
     */
    int updateStatistics(@Param("id") Long id,
                        @Param("articlesPublished") Integer articlesPublished,
                        @Param("totalViews") Long totalViews,
                        @Param("totalLikes") Long totalLikes,
                        @Param("totalFavorites") Long totalFavorites,
                        @Param("followers") Integer followers,
                        @Param("following") Integer following);
}