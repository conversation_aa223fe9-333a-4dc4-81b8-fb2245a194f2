package com.jdl.aic.portal.dao.mapper;

import com.jdl.aic.portal.dao.entity.Content;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 内容数据访问接口
 */
public interface ContentMapper {
    
    /**
     * 根据ID查询内容
     */
    Content selectById(@Param("id") Long id);
    
    /**
     * 查询所有内容
     */
    List<Content> selectAll();
    
    /**
     * 根据作者ID查询内容（已发布）
     */
    List<Content> selectByAuthorId(@Param("authorId") Long authorId, 
                                  @Param("knowledgeTypeCode") String knowledgeTypeCode,
                                  @Param("offset") Integer offset, 
                                  @Param("limit") Integer limit);
    
    /**
     * 统计作者发布的内容数量
     */
    int countByAuthorId(@Param("authorId") Long authorId, @Param("knowledgeTypeCode") String knowledgeTypeCode);
    
    /**
     * 根据用户ID查询收藏的内容（模拟数据）
     */
    List<Content> selectFavoritedByUserId(@Param("userId") Long userId, 
                                         @Param("knowledgeTypeCode") String knowledgeTypeCode,
                                         @Param("offset") Integer offset, 
                                         @Param("limit") Integer limit);
    
    /**
     * 统计用户收藏的内容数量
     */
    int countFavoritedByUserId(@Param("userId") Long userId, @Param("knowledgeTypeCode") String knowledgeTypeCode);
    
    /**
     * 根据用户ID查询点赞的内容（模拟数据）
     */
    List<Content> selectLikedByUserId(@Param("userId") Long userId, 
                                     @Param("knowledgeTypeCode") String knowledgeTypeCode,
                                     @Param("offset") Integer offset, 
                                     @Param("limit") Integer limit);
    
    /**
     * 统计用户点赞的内容数量
     */
    int countLikedByUserId(@Param("userId") Long userId, @Param("knowledgeTypeCode") String knowledgeTypeCode);
    
    /**
     * 插入内容
     */
    int insert(Content content);
    
    /**
     * 更新内容
     */
    int updateById(Content content);
    
    /**
     * 删除内容
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 更新内容统计数据
     */
    int updateStatistics(@Param("id") Long id, 
                        @Param("views") Long views,
                        @Param("likes") Long likes,
                        @Param("favorites") Long favorites);
}
