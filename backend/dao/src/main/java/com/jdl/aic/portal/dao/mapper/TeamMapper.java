package com.jdl.aic.portal.dao.mapper;

import com.jdl.aic.portal.dao.entity.Team;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 团队数据访问接口
 */
public interface TeamMapper {
    
    /**
     * 根据ID查询团队
     */
    Team selectById(@Param("teamId") Long teamId);
    
    /**
     * 查询所有团队
     */
    List<Team> selectAll();
    
    /**
     * 根据用户ID查询用户加入的团队
     */
    List<Team> selectByUserId(@Param("userId") Long userId);
    
    /**
     * 插入团队
     */
    int insert(Team team);
    
    /**
     * 更新团队信息
     */
    int updateById(Team team);
    
    /**
     * 删除团队
     */
    int deleteById(@Param("teamId") Long teamId);
    
    /**
     * 更新团队成员数量
     */
    int updateMemberCount(@Param("teamId") Long teamId, @Param("memberCount") Integer memberCount);
    
    /**
     * 更新团队统计数据
     */
    int updateStatistics(@Param("teamId") Long teamId, 
                        @Param("articlesRecommended") Integer articlesRecommended,
                        @Param("totalViews") Long totalViews,
                        @Param("totalLikes") Long totalLikes,
                        @Param("totalFavorites") Long totalFavorites);
}
