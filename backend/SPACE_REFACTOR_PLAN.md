# 🔄 后端空间模块重构计划

## 📊 当前空间相关类分布

### 已在正确位置的文件
```
backend/web/src/main/java/com/jdl/aic/portal/space/
├── controller/
│   ├── TeamController.java              # 团队控制器 ✅
│   └── UserProfileController.java       # 用户资料控制器 ✅
├── dto/
│   ├── Team.java                        # 团队DTO ✅
│   ├── TeamMember.java                  # 团队成员DTO ✅
│   └── TeamRecommendation.java          # 团队推荐DTO ✅
└── service/
    ├── TeamService.java                 # 团队服务接口 ✅
    ├── UserProfileService.java         # 用户资料服务接口 ✅
    └── impl/
        ├── TeamServiceImpl.java         # 团队服务实现 ✅
        └── UserProfileServiceImpl.java # 用户资料服务实现 ✅
```

### 需要移动的文件

#### 从service模块移动到web模块
```
backend/service/src/main/java/com/jdl/aic/portal/space/
├── service/
│   └── TeamService.java                 # 团队服务接口 (重复)
└── impl/
    ├── TeamServiceImpl.java             # 团队服务实现 (重复)
    └── UserProfileServiceImpl.java     # 用户资料服务实现 (重复)
```

#### 从common模块移动到web/space模块
```
backend/common/src/main/java/com/jdl/aic/portal/common/
├── dto/
│   ├── TeamProfileDTO.java              # 团队资料DTO
│   └── UserProfileDTO.java              # 用户资料DTO
└── result/
    └── TeamByUserResult.java            # 用户团队结果DTO
```

## 🎯 重构目标

### 1. 统一空间模块结构
将所有个人空间、团队空间、团队详情相关的类统一到：
```
backend/web/src/main/java/com/jdl/aic/portal/space/
```

### 2. 消除重复文件
- 保留web模块中实际使用的文件
- 删除service模块中的重复文件
- 移动common模块中的空间相关DTO

### 3. 优化包结构
```
backend/web/src/main/java/com/jdl/aic/portal/space/
├── controller/                          # 控制器层
│   ├── TeamController.java
│   └── UserProfileController.java
├── service/                             # 服务接口层
│   ├── TeamService.java
│   └── UserProfileService.java
├── service/impl/                        # 服务实现层
│   ├── TeamServiceImpl.java
│   └── UserProfileServiceImpl.java
└── dto/                                 # 数据传输对象
    ├── Team.java
    ├── TeamMember.java
    ├── TeamRecommendation.java
    ├── TeamProfileDTO.java
    ├── UserProfileDTO.java
    └── TeamByUserResult.java
```

## 📋 重构步骤

### Step 1: 分析重复文件
比较service模块和web模块中的重复文件，确定保留哪个版本

### Step 2: 移动common模块的DTO
将TeamProfileDTO、UserProfileDTO、TeamByUserResult移动到web/space/dto

### Step 3: 删除重复文件
删除service模块中的重复文件

### Step 4: 更新包引用
更新所有文件中的import语句，指向新的包路径

### Step 5: 验证编译
确保重构后所有文件能正常编译

## ⚠️ 注意事项

### 保留原则
- 保留web模块中实际被Controller使用的Service实现
- 保留功能更完整、代码更新的版本
- 确保所有依赖关系正确更新

### 风险控制
- 逐步移动，每次移动后验证编译
- 保持原有的功能不变
- 确保所有import路径正确更新

## 🔍 文件对比分析

需要对比以下重复文件：
1. `service/space/service/TeamService.java` vs `web/space/service/TeamService.java`
2. `service/space/service/impl/TeamServiceImpl.java` vs `web/space/service/impl/TeamServiceImpl.java`
3. `service/space/service/impl/UserProfileServiceImpl.java` vs `web/space/service/impl/UserProfileServiceImpl.java`

确定哪个版本是实际使用的，保留正确的版本。
