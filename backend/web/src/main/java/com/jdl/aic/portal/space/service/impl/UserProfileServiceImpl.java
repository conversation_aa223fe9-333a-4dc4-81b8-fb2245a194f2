package com.jdl.aic.portal.space.service.impl;


import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.FavoriteDTO;
import com.jdl.aic.core.service.client.dto.community.LikeDTO;
import com.jdl.aic.core.service.client.dto.community.request.GetUserFavoritesRequest;
import com.jdl.aic.core.service.client.dto.community.request.GetUserLikesRequest;
import com.jdl.aic.core.service.client.dto.knowledge.KnowledgeDTO;
import com.jdl.aic.core.service.client.dto.request.knowledge.GetKnowledgeListRequest;
import com.jdl.aic.core.service.client.dto.user.UserDTO;
import com.jdl.aic.core.service.client.service.KnowledgeService;
import com.jdl.aic.core.service.portal.client.FavoriteDataService;
import com.jdl.aic.core.service.portal.client.LikeDataService;
import com.jdl.aic.core.service.portal.client.TeamDataService;
import com.jdl.aic.core.service.portal.client.UserDataService;
import com.jdl.aic.portal.space.dto.UserProfileDTO;
import com.jdl.aic.portal.common.exception.BusinessException;
import com.jdl.aic.portal.space.dto.TeamByUserResult;
import com.jdl.aic.portal.common.utils.DataLoader;
import com.jdl.aic.portal.common.utils.KnowledgeTypeUtils;
import com.jdl.aic.portal.space.service.UserProfileService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 用户个人空间服务实现
 */
@Service
public class UserProfileServiceImpl implements UserProfileService {

    @Autowired
    private DataLoader dataLoader;
    @Autowired
    private UserDataService userDataService;
    @Autowired
    private FavoriteDataService favoriteDataService;
    @Autowired
    private KnowledgeService knowledgeService;
    @Autowired
    private LikeDataService likeDataService;

    @Autowired
    private TeamDataService teamDataService;


    @Override
    @SuppressWarnings("unchecked")
    public UserProfileDTO getUserProfile(Long userId) {
        Result<UserDTO> result = userDataService.getUserById(userId);
        UserDTO userDTO = result.getData();
        Map<String, Object> user = dataLoader.getUserById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        UserProfileDTO profileDTO = new UserProfileDTO();

        // 基础信息
        UserProfileDTO.BasicInfoDTO basicInfo = new UserProfileDTO.BasicInfoDTO();
        basicInfo.setUserId(userDTO.getId());
        basicInfo.setUsername(userDTO.getUsername());
        basicInfo.setDisplayName(userDTO.getDisplayName());
        basicInfo.setAvatarUrl(userDTO.getAvatarUrl());
        basicInfo.setBio(userDTO.getBio());
        basicInfo.setDepartment(userDTO.getDepartment());

        // 解析标签
        List<String> tags = userDTO.getTags();
        basicInfo.setTags(tags != null ? tags : new ArrayList<>());

        profileDTO.setBasicInfo(basicInfo);

        Result<UserDataService.UserStatsDTO> statsResult = userDataService.getUserStats(userId);
        if (statsResult != null && statsResult.isSuccess() && statsResult.getData() != null) {
            UserDataService.UserStatsDTO stats = statsResult.getData();
            UserProfileDTO.AchievementsDTO achievements = new UserProfileDTO.AchievementsDTO();

            // 映射统计数据
            achievements.setArticlesPublished(stats.getCreatedKnowledgeCount() != null ?
                    stats.getCreatedKnowledgeCount() : 0);
            achievements.setTotalViews(stats.getTotalReadCount() != null ?
                    stats.getTotalReadCount().longValue() : 0L);
            achievements.setTotalLikes(stats.getTotalLikeCount() != null ?
                    stats.getTotalLikeCount().longValue() : 0L);
            achievements.setTotalFavorites(stats.getFavoriteKnowledgeCount() != null ?
                    stats.getFavoriteKnowledgeCount().longValue() : 0L);
        }
        // 成就信息
        Map<String, Object> achievementsData = (Map<String, Object>) user.get("achievements");
        UserProfileDTO.AchievementsDTO achievements = new UserProfileDTO.AchievementsDTO();
        if (achievementsData != null) {
            achievements.setArticlesPublished(((Number) achievementsData.get("articlesPublished")).intValue());
            achievements.setTotalViews(((Number) achievementsData.get("totalViews")).longValue());
            achievements.setTotalLikes(((Number) achievementsData.get("totalLikes")).longValue());
            achievements.setTotalFavorites(((Number) achievementsData.get("totalFavorites")).longValue());

            // 根据用户成就数据动态生成徽章
            List<UserProfileDTO.BadgeDTO> badges = generateBadges(achievements);
            achievements.setBadges(badges);
        } else {
            achievements.setArticlesPublished(0);
            achievements.setTotalViews(0L);
            achievements.setTotalLikes(0L);
            achievements.setTotalFavorites(0L);
            // 即使没有成就数据，也根据基础信息生成徽章
            List<UserProfileDTO.BadgeDTO> badges = generateBadges(achievements);
            achievements.setBadges(badges);
        }

        profileDTO.setAchievements(achievements);
        // 社交信息
        Map<String, Object> socialData = (Map<String, Object>) user.get("social");
        UserProfileDTO.SocialDTO social = new UserProfileDTO.SocialDTO();
        if (socialData != null) {
            social.setFollowers(((Number) socialData.get("followers")).intValue());
            social.setFollowing(((Number) socialData.get("following")).intValue());
        } else {
            social.setFollowers(0);
            social.setFollowing(0);
        }

        profileDTO.setSocial(social);

        return profileDTO;
    }

    @Override
    public UserProfileDTO updateUserProfile(Long userId, Map<String, Object> profileData) {
        //Map<String, Object> updatedUser = dataLoader.updateUserProfile(userId, profileData);
        UserDTO userDTO = new UserDTO();

        // 更新允许修改的字段
        if (profileData.containsKey("avatarUrl")) {
            userDTO.setAvatarUrl(profileData.get("avatarUrl").toString());
        }
        if (profileData.containsKey("bio")) {
            userDTO.setBio(profileData.get("bio").toString());
        }
        if (profileData.containsKey("tags")) {
            userDTO.setTags((List<String>) profileData.get("tags"));
        }
        userDataService.updateUser(userId, userDTO);
        return getUserProfile(userId);
    }

    @Override
    public Map<String, Object> getUserContents(Long userId, String associationType, String knowledgeTypeCode, Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;
        PageRequest pageRequest = new PageRequest();
        pageRequest.setSize(pageSize);
        pageRequest.setPage(page);
        // 获取知识的类型(MCP、提示词、文章、工具)，在知识接口中

        //knowledgeService.getKnowledgeById()

        // 从知识表中获取默认获取作者为本人的知识集合，支持按上述类型筛选
        // 从收藏表中获取收藏的知识集合，支持按上述类型筛选
        // 从点赞表中获取点赞的知识集合，支持按上述类型筛选
        GetUserFavoritesRequest getUserFavoritesRequest = new GetUserFavoritesRequest();
        getUserFavoritesRequest.setUserId(userId);
        getUserFavoritesRequest.setPageRequest(pageRequest);
        Result<PageResult<FavoriteDTO>> favoritesResult = favoriteDataService.getUserFavorites(getUserFavoritesRequest);
        if (favoritesResult != null && favoritesResult.isSuccess() && favoritesResult.getData() != null) {
         PageResult<FavoriteDTO> favorites = favoritesResult.getData();
         List<FavoriteDTO> favoriteList = favorites.getRecords();
         for (FavoriteDTO favorite : favoriteList) {
             System.out.println(favorite.getContentId());
         }
        }
        GetUserLikesRequest getUserLikesRequest = new GetUserLikesRequest();
        getUserLikesRequest.setUserId(userId);
        getUserLikesRequest.setPageRequest(pageRequest);
        Result<PageResult<LikeDTO>> likesResult = likeDataService.getUserLikes(getUserLikesRequest);

        GetKnowledgeListRequest getKnowledgeListRequest = new GetKnowledgeListRequest();
        getKnowledgeListRequest.setAuthorId(String.valueOf(userId));
        Result<PageResult<KnowledgeDTO>> knowledgeResult = knowledgeService.getKnowledgeList(pageRequest,getKnowledgeListRequest);


        List<Map<String, Object>> allContents = dataLoader.getUserContents(userId, associationType, knowledgeTypeCode);
        int total = allContents.size();
        // 手动分页
        int fromIndex = (page - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        List<Map<String, Object>> pagedContents = allContents.subList(fromIndex, toIndex);

        // 统计各类型数量
        Map<String, Integer> countsByType = new HashMap<>();
        Map<String, Object> allUserContents = new HashMap<>();
        allUserContents.put("all", dataLoader.getUserContents(userId, associationType, null).size());


        // 统计各知识类型的数量
        List<Map<String, Object>> allUserContentsList = dataLoader.getUserContents(userId, associationType, null);
        for (Map<String, Object> content : allUserContentsList) {
            String type = (String) content.get("knowledgeTypeCode");
            // 处理null值，避免Jackson序列化错误
            String typeKey = type != null ? type : "unknown";
            countsByType.put(typeKey, countsByType.getOrDefault(typeKey, 0) + 1);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("page", page);
        result.put("pageSize", pageSize);
        result.put("total", total);
        result.put("list", pagedContents);
        result.put("countsByType", countsByType);
        return result;
    }

    @Override
    public List<TeamByUserResult> getUserTeams(Long userId) {
        Result<List<Long>> teams = userDataService.getUserTeams(userId);
        List<Long> teamsId = teams.getData();
        List<TeamByUserResult> teamDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(teamsId)) {
            return new ArrayList<>();
        } else {
            for (Long teamId : teamsId) {
                TeamByUserResult teamByUserResult =new TeamByUserResult();
                BeanUtils.copyProperties(teamDataService.getTeamById(teamId).getData(), teamByUserResult);
                teamDTOList.add(teamByUserResult);
                PageRequest pageRequestMembers = new PageRequest();
                pageRequestMembers.setPage(1);
                pageRequestMembers.setSize(10);
                Result<PageResult<UserDTO>> teamMembersResult = userDataService.getTeamMembers(teamId, pageRequestMembers);
                teamByUserResult.setUserCount(teamMembersResult.getData().getPagination().getTotalElements());
            }
        }
        return teamDTOList;
    }

    @Override
    public Map<String, Object> getUserLearnings(Long userId) {
        Map<String, Object> learningData = dataLoader.getUserLearnings(userId);

        if (learningData != null) {
            return learningData;
        } else {
            // 返回默认学习数据
            Map<String, Object> defaultLearning = new HashMap<>();
            defaultLearning.put("userId", userId);
            defaultLearning.put("totalLearningHours", 0.0);
            defaultLearning.put("coursesCompleted", 0);
            defaultLearning.put("consecutiveLearningDays", 0);
            defaultLearning.put("inProgress", new ArrayList<>());
            defaultLearning.put("weeklyGoals", new ArrayList<>());
            return defaultLearning;
        }
    }

    /**
     * 根据用户成就数据动态生成徽章
     * @param achievements 用户成就数据
     * @return 徽章列表
     */
    private List<UserProfileDTO.BadgeDTO> generateBadges(UserProfileDTO.AchievementsDTO achievements) {
        List<UserProfileDTO.BadgeDTO> badges = new ArrayList<>();

        // 获取统计数据
        int articlesPublished = achievements.getArticlesPublished() != null ? achievements.getArticlesPublished() : 0;
        long totalViews = achievements.getTotalViews() != null ? achievements.getTotalViews() : 0L;
        long totalLikes = achievements.getTotalLikes() != null ? achievements.getTotalLikes() : 0L;
        long totalFavorites = achievements.getTotalFavorites() != null ? achievements.getTotalFavorites() : 0L;

        // 新手作者徽章 - 发布第一篇文章
        if (articlesPublished >= 1) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("新手作者");
            badge.setType("bronze");
            badge.setDescription("发布了第一篇文章");
            badge.setIconUrl("fas fa-pen");
            badges.add(badge);
        }

        // 活跃作者徽章 - 发布5篇文章
        if (articlesPublished >= 5) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("活跃作者");
            badge.setType("silver");
            badge.setDescription("发布了5篇文章");
            badge.setIconUrl("fas fa-edit");
            badges.add(badge);
        }

        // 高产作者徽章 - 发布20篇文章
        if (articlesPublished >= 20) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("高产作者");
            badge.setType("gold");
            badge.setDescription("发布了20篇文章");
            badge.setIconUrl("fas fa-feather-alt");
            badges.add(badge);
        }

        // 专业作者徽章 - 发布50篇文章
        if (articlesPublished >= 50) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("专业作者");
            badge.setType("platinum");
            badge.setDescription("发布了50篇文章");
            badge.setIconUrl("fas fa-crown");
            badges.add(badge);
        }

        // 人气新星徽章 - 总阅读量达到1000
        if (totalViews >= 1000) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("人气新星");
            badge.setType("bronze");
            badge.setDescription("总阅读量达到1000");
            badge.setIconUrl("fas fa-star");
            badges.add(badge);
        }

        // 人气作者徽章 - 总阅读量达到10000
        if (totalViews >= 10000) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("人气作者");
            badge.setType("silver");
            badge.setDescription("总阅读量达到10000");
            badge.setIconUrl("fas fa-fire");
            badges.add(badge);
        }

        // 影响力作者徽章 - 总阅读量达到50000
        if (totalViews >= 50000) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("影响力作者");
            badge.setType("gold");
            badge.setDescription("总阅读量达到50000");
            badge.setIconUrl("fas fa-trophy");
            badges.add(badge);
        }

        // 超级影响力徽章 - 总阅读量达到100000
        if (totalViews >= 100000) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("超级影响力");
            badge.setType("diamond");
            badge.setDescription("总阅读量达到100000");
            badge.setIconUrl("fas fa-medal");
            badges.add(badge);
        }

        // 受欢迎作者徽章 - 总点赞数达到100
        if (totalLikes >= 100) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("受欢迎作者");
            badge.setType("bronze");
            badge.setDescription("总点赞数达到100");
            badge.setIconUrl("fas fa-heart");
            badges.add(badge);
        }

        // 点赞达人徽章 - 总点赞数达到500
        if (totalLikes >= 500) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("点赞达人");
            badge.setType("silver");
            badge.setDescription("总点赞数达到500");
            badge.setIconUrl("fas fa-thumbs-up");
            badges.add(badge);
        }

        // 点赞之王徽章 - 总点赞数达到2000
        if (totalLikes >= 2000) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("点赞之王");
            badge.setType("gold");
            badge.setDescription("总点赞数达到2000");
            badge.setIconUrl("fas fa-gem");
            badges.add(badge);
        }

        // 收藏新手徽章 - 总收藏数达到50
        if (totalFavorites >= 50) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("收藏新手");
            badge.setType("bronze");
            badge.setDescription("总收藏数达到50");
            badge.setIconUrl("fas fa-bookmark");
            badges.add(badge);
        }

        // 收藏达人徽章 - 总收藏数达到200
        if (totalFavorites >= 200) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("收藏达人");
            badge.setType("silver");
            badge.setDescription("总收藏数达到200");
            badge.setIconUrl("fas fa-star");
            badges.add(badge);
        }

        // 收藏之星徽章 - 总收藏数达到1000
        if (totalFavorites >= 1000) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("收藏之星");
            badge.setType("gold");
            badge.setDescription("总收藏数达到1000");
            badge.setIconUrl("fas fa-certificate");
            badges.add(badge);
        }

        // 全能作者徽章 - 发布文章>=10 且 总阅读量>=5000 且 总点赞数>=200
        if (articlesPublished >= 10 && totalViews >= 5000 && totalLikes >= 200) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("全能作者");
            badge.setType("platinum");
            badge.setDescription("发布文章>=10 且 总阅读量>=5000 且 总点赞数>=200");
            badge.setIconUrl("fas fa-award");
            badges.add(badge);
        }

        // 社区之星徽章 - 发布文章>=20 且 总阅读量>=20000 且 总点赞数>=1000 且 总收藏数>=500
        if (articlesPublished >= 20 && totalViews >= 20000 && totalLikes >= 1000 && totalFavorites >= 500) {
            UserProfileDTO.BadgeDTO badge = new UserProfileDTO.BadgeDTO();
            badge.setName("社区之星");
            badge.setType("diamond");
            badge.setDescription("发布文章>=20 且 总阅读量>=20000 且 总点赞数>=1000 且 总收藏数>=500");
            badge.setIconUrl("fas fa-users");
            badges.add(badge);
        }

        return badges;
    }

    @Override
    public Map<String, Object> getUserLikes(Long userId, String knowledgeTypeCode, Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;

        PageRequest pageRequest = new PageRequest();
        pageRequest.setSize(pageSize);
        pageRequest.setPage(page);

        try {
            // 获取用户点赞的内容
            GetUserLikesRequest getUserLikesRequest = new GetUserLikesRequest();
            getUserLikesRequest.setUserId(userId);
            getUserLikesRequest.setPageRequest(pageRequest);

            Result<PageResult<LikeDTO>> likesResult = likeDataService.getUserLikes(getUserLikesRequest);

            if (likesResult != null && likesResult.isSuccess() && likesResult.getData() != null) {
                PageResult<LikeDTO> likesPage = likesResult.getData();
                List<LikeDTO> likesList = likesPage.getRecords();

                // 转换为前端需要的格式
                List<Map<String, Object>> contents = new ArrayList<>();
                Map<String, Integer> countsByType = new HashMap<>();

                for (LikeDTO like : likesList) {
                    // 根据contentId获取知识详情
                    try {
                        Result<KnowledgeDTO> knowledgeResult = knowledgeService.getKnowledgeById(like.getContentId());
                        if (knowledgeResult != null && knowledgeResult.isSuccess() && knowledgeResult.getData() != null) {
                            KnowledgeDTO knowledge = knowledgeResult.getData();

                            // 如果指定了知识类型，进行过滤
                            if (knowledgeTypeCode != null && !knowledgeTypeCode.equals(KnowledgeTypeUtils.idToCode(knowledge.getKnowledgeTypeId()))) {
                                continue;
                            }
                            Map<String, Object> content = new HashMap<>();
                            content.put("id", knowledge.getId());
                            content.put("title", knowledge.getTitle());
                            content.put("description", knowledge.getDescription());

                            // 处理知识类型：优先使用Code，如果为空则从ID转换
                            String typeCode = knowledge.getKnowledgeTypeCode();
                            if (typeCode == null && knowledge.getKnowledgeTypeId() != null) {
                                typeCode = KnowledgeTypeUtils.idToCode(knowledge.getKnowledgeTypeId());
                            }
                            content.put("knowledgeTypeCode", typeCode);

                            content.put("authorId", knowledge.getAuthorId());
                            content.put("authorName", knowledge.getAuthorName());
                            content.put("createdAt", knowledge.getCreatedAt());
                            content.put("updatedAt", knowledge.getUpdatedAt());
                            content.put("likedAt", like.getCreatedAt()); // 点赞时间

                            contents.add(content);

                            // 统计各类型数量，使用转换后的typeCode
                            String typeKey = typeCode != null ? typeCode : "unknown";
                            countsByType.put(typeKey, countsByType.getOrDefault(typeKey, 0) + 1);
                        }
                    } catch (Exception e) {
                        System.err.println("获取知识详情失败，contentId: " + like.getContentId() + ", error: " + e.getMessage());
                    }
                }

                Map<String, Object> result = new HashMap<>();
                result.put("page", page);
                result.put("pageSize", pageSize);
                result.put("total", likesPage.getPagination().getTotalElements());
                result.put("list", contents);
                result.put("countsByType", countsByType);
                return result;
            }
        } catch (Exception e) {
            System.err.println("获取用户点赞列表失败: " + e.getMessage());
        }

        // 返回空结果
        Map<String, Object> emptyResult = new HashMap<>();
        emptyResult.put("page", page);
        emptyResult.put("pageSize", pageSize);
        emptyResult.put("total", 0);
        emptyResult.put("list", new ArrayList<>());
        emptyResult.put("countsByType", new HashMap<>());
        return emptyResult;
    }

    @Override
    public Map<String, Object> getUserFavorites(Long userId, String knowledgeTypeCode, Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;

        PageRequest pageRequest = new PageRequest();
        pageRequest.setSize(pageSize);
        pageRequest.setPage(page);

        try {
            // 获取用户收藏的内容
            GetUserFavoritesRequest getUserFavoritesRequest = new GetUserFavoritesRequest();
            getUserFavoritesRequest.setUserId(userId);
            getUserFavoritesRequest.setPageRequest(pageRequest);

            Result<PageResult<FavoriteDTO>> favoritesResult = favoriteDataService.getUserFavorites(getUserFavoritesRequest);

            if (favoritesResult != null && favoritesResult.isSuccess() && favoritesResult.getData() != null) {
                PageResult<FavoriteDTO> favoritesPage = favoritesResult.getData();
                List<FavoriteDTO> favoritesList = favoritesPage.getRecords();

                // 转换为前端需要的格式
                List<Map<String, Object>> contents = new ArrayList<>();
                Map<String, Integer> countsByType = new HashMap<>();

                for (FavoriteDTO favorite : favoritesList) {
                    // 根据contentId获取知识详情
                    try {
                        Result<KnowledgeDTO> knowledgeResult = knowledgeService.getKnowledgeById(favorite.getContentId());
                        if (knowledgeResult != null && knowledgeResult.isSuccess() && knowledgeResult.getData() != null) {
                            KnowledgeDTO knowledge = knowledgeResult.getData();

                            // 如果指定了知识类型，进行过滤
                            if (knowledgeTypeCode != null && !knowledgeTypeCode.equals(KnowledgeTypeUtils.idToCode(knowledge.getKnowledgeTypeId()))) {
                                continue;
                            }

                            Map<String, Object> content = new HashMap<>();
                            content.put("id", knowledge.getId());
                            content.put("title", knowledge.getTitle());
                            content.put("description", knowledge.getDescription());

                            // 处理知识类型：优先使用Code，如果为空则从ID转换
                            String typeCode = knowledge.getKnowledgeTypeCode();
                            if (typeCode == null && knowledge.getKnowledgeTypeId() != null) {
                                typeCode = KnowledgeTypeUtils.idToCode(knowledge.getKnowledgeTypeId());
                            }
                            content.put("knowledgeTypeCode", typeCode);

                            content.put("authorId", knowledge.getAuthorId());
                            content.put("authorName", knowledge.getAuthorName());
                            content.put("createdAt", knowledge.getCreatedAt());
                            content.put("updatedAt", knowledge.getUpdatedAt());
                            content.put("favoritedAt", favorite.getCreatedAt()); // 收藏时间

                            contents.add(content);

                            // 统计各类型数量，使用转换后的typeCode
                            String typeKey = typeCode != null ? typeCode : "unknown";
                            countsByType.put(typeKey, countsByType.getOrDefault(typeKey, 0) + 1);
                        }
                    } catch (Exception e) {
                        System.err.println("获取知识详情失败，contentId: " + favorite.getContentId() + ", error: " + e.getMessage());
                    }
                }

                Map<String, Object> result = new HashMap<>();
                result.put("page", page);
                result.put("pageSize", pageSize);
                result.put("total", favoritesPage.getPagination().getTotalElements());
                result.put("list", contents);
                result.put("countsByType", countsByType);
                return result;
            }
        } catch (Exception e) {
            System.err.println("获取用户收藏列表失败: " + e.getMessage());
        }

        // 返回空结果
        Map<String, Object> emptyResult = new HashMap<>();
        emptyResult.put("page", page);
        emptyResult.put("pageSize", pageSize);
        emptyResult.put("total", 0);
        emptyResult.put("list", new ArrayList<>());
        emptyResult.put("countsByType", new HashMap<>());
        return emptyResult;
    }

    @Override
    public Map<String, Object> getUserKnowledge(Long userId, String knowledgeTypeCode, Integer page, Integer pageSize) {
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1) pageSize = 10;

        PageRequest pageRequest = new PageRequest();
        pageRequest.setSize(pageSize);
        pageRequest.setPage(page);

        try {
            // 获取用户创建的知识内容
            GetKnowledgeListRequest getKnowledgeListRequest = new GetKnowledgeListRequest();
            getKnowledgeListRequest.setAuthorId(String.valueOf(userId));

            // 将前端传来的knowledgeTypeCode转换为ID，用于调用其他服务
            Long knowledgeTypeId = KnowledgeTypeUtils.convertCodeToIdForService(knowledgeTypeCode);
            if (knowledgeTypeId != null) {
                getKnowledgeListRequest.setKnowledgeTypeId(knowledgeTypeId);
            }


            Result<PageResult<KnowledgeDTO>> knowledgeResult = knowledgeService.getKnowledgeList(pageRequest, getKnowledgeListRequest);

            if (knowledgeResult != null && knowledgeResult.isSuccess() && knowledgeResult.getData() != null) {
                PageResult<KnowledgeDTO> knowledgePage = knowledgeResult.getData();
                List<KnowledgeDTO> knowledgeList = knowledgePage.getRecords();



                // 转换为前端需要的格式
                List<Map<String, Object>> contents = new ArrayList<>();
                Map<String, Integer> countsByType = new HashMap<>();

                for (KnowledgeDTO knowledge : knowledgeList) {
                    Map<String, Object> content = new HashMap<>();
                    content.put("knowledgeTypeId", knowledge.getKnowledgeTypeId());
                    content.put("id", knowledge.getId());
                    content.put("title", knowledge.getTitle());
                    content.put("description", knowledge.getDescription());

                    // 处理知识类型：优先使用Code，如果为空则从ID转换
                    String typeCode = knowledge.getKnowledgeTypeCode();
                    if (typeCode == null && knowledge.getKnowledgeTypeId() != null) {
                        typeCode = KnowledgeTypeUtils.idToCode(knowledge.getKnowledgeTypeId());
                    }
                    content.put("knowledgeTypeCode", typeCode);

                    content.put("authorId", knowledge.getAuthorId());
                    content.put("authorName", knowledge.getAuthorName());
                    content.put("createdAt", knowledge.getCreatedAt());
                    content.put("updatedAt", knowledge.getUpdatedAt());
                    content.put("status", knowledge.getStatus());
                    content.put("viewCount", knowledge.getReadCount());
                    content.put("likeCount", knowledge.getLikeCount());
                    content.put("favoriteCount", knowledge.getForkCount());

                    contents.add(content);

                    // 统计各类型数量，使用转换后的typeCode
                    String typeKey = typeCode != null ? typeCode : "unknown";
                    countsByType.put(typeKey, countsByType.getOrDefault(typeKey, 0) + 1);
                }

                Map<String, Object> result = new HashMap<>();
                result.put("page", page);
                result.put("pageSize", pageSize);
                result.put("total", knowledgePage.getPagination().getTotalElements());
                result.put("list", contents);
                result.put("countsByType", countsByType);
                return result;
            }
        } catch (Exception e) {
            System.err.println("获取用户知识列表失败: " + e.getMessage());
        }
        // 返回空结果
        Map<String, Object> emptyResult = new HashMap<>();
        emptyResult.put("page", page);
        emptyResult.put("pageSize", pageSize);
        emptyResult.put("total", 0);
        emptyResult.put("list", new ArrayList<>());
        emptyResult.put("countsByType", new HashMap<>());
        return emptyResult;
    }
}
