package com.jdl.aic.portal.space.controller;

import com.jdl.aic.portal.common.dto.UserProfileDTO;
import com.jdl.aic.portal.common.result.Result;
import com.jdl.aic.portal.common.result.TeamByUserResult;
import com.jdl.aic.portal.space.service.UserProfileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户个人空间控制器
 */
@RestController
@RequestMapping("/api/v1/users")
@CrossOrigin(origins = "*")
public class UserProfileController {

    @Autowired
    private UserProfileService userProfileService;

    /**
     * 获取用户完整信息
     */
    @GetMapping("/{userId}/profile")
    public Result<UserProfileDTO> getUserProfile(@PathVariable Long userId) {
        try {
            UserProfileDTO userProfile = userProfileService.getUserProfile(userId);
            return Result.success(userProfile, "获取用户信息成功");
        } catch (Exception e) {
            return Result.error("获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户资料
     */
    @PutMapping("/{userId}/profile")
    public Result<UserProfileDTO> updateUserProfile(@PathVariable Long userId, @RequestBody Map<String, Object> profileData) {
        try {
            UserProfileDTO updatedProfile = userProfileService.updateUserProfile(userId, profileData);
            return Result.success(updatedProfile, "更新用户资料成功");
        } catch (Exception e) {
            return Result.error("更新用户资料失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户关联的内容列表
     */
    @GetMapping("/{userId}/contents")
    public Result<Map<String, Object>> getUserContents(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "published") String associationType,
            @RequestParam(required = false) String knowledgeTypeCode,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        
        try {
            // 验证关联类型
            if (!isValidAssociationType(associationType)) {
                return Result.error("关联类型无效");
            }

            Map<String, Object> result = userProfileService.getUserContents(userId, associationType, knowledgeTypeCode, page, pageSize);
            return Result.success(result, "获取用户内容成功");
        } catch (Exception e) {
            return Result.error("获取用户内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户加入的团队列表
     */
    @GetMapping("/{userId}/teams")
    public Result<List<TeamByUserResult>> getUserTeams(@PathVariable Long userId) {
        try {
            List<TeamByUserResult> teams = userProfileService.getUserTeams(userId);
            return Result.success(teams, "获取用户团队列表成功");
        } catch (Exception e) {
            return Result.error("获取用户团队列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户学习信息
     */
    @GetMapping("/{userId}/learnings")
    public Result<Map<String, Object>> getUserLearnings(@PathVariable Long userId) {
        try {
            Map<String, Object> learnings = userProfileService.getUserLearnings(userId);
            return Result.success(learnings, "获取用户学习信息成功");
        } catch (Exception e) {
            return Result.error("获取用户学习信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户喜欢列表
     */
    @GetMapping("/{userId}/likes")
    public Result<Map<String, Object>> getUserLikes(
            @PathVariable Long userId,
            @RequestParam(required = false) String knowledgeTypeCode,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            Map<String, Object> likes = userProfileService.getUserLikes(userId, knowledgeTypeCode, page, pageSize);
            return Result.success(likes, "获取用户喜欢列表成功");
        } catch (Exception e) {
            return Result.error("获取用户喜欢列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户收藏列表
     */
    @GetMapping("/{userId}/favorites")
    public Result<Map<String, Object>> getUserFavorites(
            @PathVariable Long userId,
            @RequestParam(required = false) String knowledgeTypeCode,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            Map<String, Object> favorites = userProfileService.getUserFavorites(userId, knowledgeTypeCode, page, pageSize);
            return Result.success(favorites, "获取用户收藏列表成功");
        } catch (Exception e) {
            return Result.error("获取用户收藏列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户知识列表
     */
    @GetMapping("/{userId}/knowledge")
    public Result<Map<String, Object>> getUserKnowledge(
            @PathVariable Long userId,
            @RequestParam(required = false) String knowledgeTypeCode,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            Map<String, Object> knowledge = userProfileService.getUserKnowledge(userId, knowledgeTypeCode, page, pageSize);
            return Result.success(knowledge, "获取用户知识列表成功");
        } catch (Exception e) {
            return Result.error("获取用户知识列表失败: " + e.getMessage());
        }
    }

    /**
     * 验证关联类型是否有效
     */
    private boolean isValidAssociationType(String associationType) {
        return "published".equals(associationType) || 
               "favorited".equals(associationType) || 
               "liked".equals(associationType);
    }
}
