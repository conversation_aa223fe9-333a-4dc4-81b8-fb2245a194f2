package com.jdl.aic.portal.space.controller;

import com.jdl.aic.portal.common.dto.TeamProfileDTO;
import com.jdl.aic.portal.common.result.Result;
import com.jdl.aic.portal.space.service.TeamService;
import com.jdl.aic.portal.recommendation.service.RecommendationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 团队空间控制器 - 重构后只包含团队核心功能
 *
 * <AUTHOR> Portal Team
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/v1/teams")
@CrossOrigin(origins = "*")
public class TeamController {

    @Autowired
    private TeamService teamService;

    @Autowired
    private RecommendationService recommendationService;

    /**
     * 获取团队基础信息和成就
     */
    @GetMapping("/{teamId}")
    public Result<TeamProfileDTO> getTeamProfile(@PathVariable Long teamId) {
        try {
            TeamProfileDTO teamProfile = teamService.getTeamProfile(teamId);
            return Result.success(teamProfile, "获取团队信息成功");
        } catch (Exception e) {
            return Result.error("获取团队信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取团队推荐的内容列表 - 重定向到推荐服务
     */
    @GetMapping("/{teamId}/recommendations")
    public Result<Map<String, Object>> getTeamRecommendations(
            @PathVariable Long teamId,
            @RequestParam(required = false) String knowledgeTypeCode,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {

        try {
            Map<String, Object> result = recommendationService.getTeamRecommendations(teamId, knowledgeTypeCode, page, pageSize);
            return Result.success(result, "获取团队推荐内容成功");
        } catch (Exception e) {
            return Result.error("获取团队推荐内容失败: " + e.getMessage());
        }
    }

    /**
     * 获取团队成员列表
     */
    @GetMapping("/{teamId}/members")
    public Result<Map<String, Object>> getTeamMembers(
            @PathVariable Long teamId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        
        try {
            Map<String, Object> result = teamService.getTeamMembers(teamId, page, pageSize);
            return Result.success(result, "获取团队成员列表成功");
        } catch (Exception e) {
            return Result.error("获取团队成员列表失败: " + e.getMessage());
        }
    }

    /**
     * 创建团队空间
     */
    @PostMapping
    public Result<Map<String, Object>> createTeam(@RequestBody Map<String, Object> teamData) {
        try {
            // 基础验证
            String name = (String) teamData.get("name");
            String description = (String) teamData.get("description");
            
            if (name == null || name.trim().isEmpty()) {
                return Result.error("团队名称不能为空");
            }
            if (description == null || description.trim().isEmpty()) {
                return Result.error("团队描述不能为空");
            }

            // TODO: 从认证信息中获取当前用户ID，这里暂时使用固定值
            Long creatorId = 1L;
            
            Map<String, Object> result = teamService.createTeam(teamData, creatorId);
            return Result.success(result, "创建团队成功");
        } catch (Exception e) {
            return Result.error("创建团队失败: " + e.getMessage());
        }
    }

    /**
     * 申请加入团队
     */
    @PostMapping("/{teamId}/apply")
    public Result<Boolean> applyToJoinTeam(@PathVariable Long teamId, @RequestBody Map<String, Object> requestData) {
        try {
            // TODO: 从认证信息中获取当前用户ID，这里暂时使用固定值
            Long userId = 1L;
            String reason = (String) requestData.get("reason");
            
            boolean result = teamService.applyToJoinTeam(teamId, userId, reason);
            return Result.success(result, "申请提交成功");
        } catch (Exception e) {
            return Result.error("申请失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有团队列表
     */
    @GetMapping
    public Result<Map<String, Object>> getAllTeams(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            Map<String, Object> result = teamService.getAllTeams(page, pageSize);
            return Result.success(result, "获取团队列表成功");
        } catch (Exception e) {
            return Result.error("获取团队列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取团队活动记录
     */
    @GetMapping("/{teamId}/activities")
    public Result<Map<String, Object>> getTeamActivities(
            @PathVariable Long teamId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            Map<String, Object> result = teamService.getTeamActivities(teamId, page, pageSize);
            return Result.success(result, "获取团队活动记录成功");
        } catch (Exception e) {
            return Result.error("获取团队活动记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取团队贡献者排行
     */
    @GetMapping("/{teamId}/contributors")
    public Result<Map<String, Object>> getTeamContributors(
            @PathVariable Long teamId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            Map<String, Object> result = teamService.getTeamContributors(teamId, page, pageSize);
            return Result.success(result, "获取团队贡献者排行成功");
        } catch (Exception e) {
            return Result.error("获取团队贡献者排行失败: " + e.getMessage());
        }
    }

    /**
     * 推荐内容到团队 - 重定向到推荐服务
     */
    @PostMapping("/recommend")
    public Result<Boolean> recommendContents(@RequestBody Map<String, Object> requestData) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> teamIds = (List<Long>) requestData.get("teamIds");
            @SuppressWarnings("unchecked")
            List<Long> contentIds = (List<Long>) requestData.get("contentIds");
            String reason = (String) requestData.get("reason");

            if (teamIds == null || teamIds.isEmpty()) {
                return Result.error("请选择要推荐的团队");
            }
            if (contentIds == null || contentIds.isEmpty()) {
                return Result.error("请选择要推荐的内容");
            }

            // TODO: 从认证信息中获取当前用户ID，这里暂时使用固定值
            Long recommenderId = 1L;

            boolean result = recommendationService.recommendContentsToTeams(teamIds, contentIds, recommenderId, reason);
            return Result.success(result, "推荐成功");
        } catch (Exception e) {
            return Result.error("推荐失败: " + e.getMessage());
        }
    }

    /**
     * 收藏团队
     */
    @PostMapping("/{teamId}/star")
    public Result<Boolean> starTeam(@PathVariable Long teamId) {
        try {
            // TODO: 从认证信息中获取当前用户ID，这里暂时使用固定值
            Long userId = 1L;

            boolean result = teamService.starTeam(teamId, userId);
            return Result.success(result, "收藏团队成功");
        } catch (Exception e) {
            return Result.error("收藏团队失败: " + e.getMessage());
        }
    }

    /**
     * 取消收藏团队
     */
    @DeleteMapping("/{teamId}/star")
    public Result<Boolean> unstarTeam(@PathVariable Long teamId) {
        try {
            // TODO: 从认证信息中获取当前用户ID，这里暂时使用固定值
            Long userId = 1L;

            boolean result = teamService.unstarTeam(teamId, userId);
            return Result.success(result, "取消收藏团队成功");
        } catch (Exception e) {
            return Result.error("取消收藏团队失败: " + e.getMessage());
        }
    }
}
