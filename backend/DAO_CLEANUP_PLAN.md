# 🧹 后端Dao层清理计划

## 📊 当前架构分析

### 现有Dao层文件
```
backend/dao/src/main/java/com/jdl/aic/portal/dao/
├── entity/
│   ├── Content.java                  # 内容实体
│   ├── JwtToken.java                # JWT令牌实体
│   ├── LoginLog.java                # 登录日志实体
│   ├── User.java                    # 用户实体
│   └── UserLearning.java            # 用户学习实体
└── mapper/
    ├── ContentMapper.java           # 内容Mapper
    ├── JwtTokenMapper.java          # JWT令牌Mapper
    ├── LoginLogMapper.java          # 登录日志Mapper
    ├── TeamMapper.java              # 团队Mapper
    ├── TeamMemberMapper.java        # 团队成员Mapper
    ├── TeamRecommendationMapper.java # 团队推荐Mapper
    ├── UserLearningMapper.java      # 用户学习Mapper
    └── UserMapper.java              # 用户Mapper
```

### 现有Mapper XML文件
```
backend/dao/src/main/resources/mapper/
├── JwtTokenMapper.xml               # JWT令牌映射
├── LoginLogMapper.xml               # 登录日志映射
├── TeamMapper.xml                   # 团队映射
└── UserMapper.xml                   # 用户映射
```

## 🎯 清理策略

### 保留的核心功能
由于认证和基础用户功能仍需要数据库支持，我们需要保留：
- **JWT令牌管理**：用户登录状态管理
- **登录日志**：安全审计需要
- **基础用户信息**：用户认证和基本信息

### 需要清理的个人空间和团队空间相关
- **个人空间相关**：Content、UserLearning相关的Dao
- **团队空间相关**：Team、TeamMember、TeamRecommendation相关的Dao
- **用户详细信息**：复杂的用户资料管理（改为外部服务）

## 📋 清理计划

### Phase 1: 清理个人空间相关Dao
**删除文件：**
- `dao/entity/Content.java`
- `dao/entity/UserLearning.java`
- `dao/mapper/ContentMapper.java`
- `dao/mapper/UserLearningMapper.java`

### Phase 2: 清理团队空间相关Dao
**删除文件：**
- `dao/mapper/TeamMapper.java`
- `dao/mapper/TeamMemberMapper.java`
- `dao/mapper/TeamRecommendationMapper.java`
- `dao/src/main/resources/mapper/TeamMapper.xml`

### Phase 3: 简化用户Dao
**保留但简化：**
- `dao/entity/User.java` - 只保留认证必需字段
- `dao/mapper/UserMapper.java` - 只保留基础查询方法
- `dao/src/main/resources/mapper/UserMapper.xml` - 简化SQL映射

### Phase 4: 保留认证相关Dao
**完全保留：**
- `dao/entity/JwtToken.java`
- `dao/entity/LoginLog.java`
- `dao/mapper/JwtTokenMapper.java`
- `dao/mapper/LoginLogMapper.java`
- `dao/src/main/resources/mapper/JwtTokenMapper.xml`
- `dao/src/main/resources/mapper/LoginLogMapper.xml`

### Phase 5: 更新配置文件
**需要更新的配置：**
- 移除MyBatis扫描中不需要的Mapper
- 更新Spring配置中的Mapper扫描路径
- 清理数据库初始化脚本中的相关表

## 🔧 实施步骤

### Step 1: 备份当前代码
确保有完整的代码备份，以防需要回滚

### Step 2: 删除个人空间和团队空间Dao
按照计划删除相关的Entity、Mapper和XML文件

### Step 3: 简化用户相关Dao
保留认证必需的用户信息，删除复杂的用户资料字段

### Step 4: 更新Service层
修改Service实现，移除对已删除Dao的依赖

### Step 5: 更新配置文件
更新MyBatis和Spring配置，移除不存在的Mapper引用

### Step 6: 测试验证
确保认证功能正常，其他功能通过外部服务获取数据

## ⚠️ 注意事项

### 保留的功能
- **用户认证**：登录、注册、JWT管理
- **安全审计**：登录日志记录
- **基础用户信息**：用户ID、用户名、邮箱等认证必需信息

### 外部服务获取的数据
- **个人空间数据**：用户详细资料、内容、学习进度
- **团队空间数据**：团队信息、成员、推荐内容
- **统计数据**：各种统计和分析数据

### 架构变化
- **数据获取方式**：从直接数据库查询改为外部服务调用
- **Service层职责**：从数据访问改为服务编排和数据转换
- **Controller层**：保持不变，继续提供REST API

这个清理计划将显著简化后端架构，提高系统的可维护性和扩展性。
