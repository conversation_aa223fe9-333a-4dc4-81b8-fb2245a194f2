-- 个人空间和团队空间数据库表结构

-- 扩展用户表，添加个人空间相关字段
ALTER TABLE sys_user 
ADD COLUMN display_name VARCHAR(100) COMMENT '显示名称',
ADD COLUMN bio TEXT COMMENT '个人简介',
ADD COLUMN department VARCHAR(100) COMMENT '部门',
ADD COLUMN tags JSON COMMENT '标签列表',
ADD COLUMN articles_published INT DEFAULT 0 COMMENT '发布文章数',
ADD COLUMN total_views BIGINT DEFAULT 0 COMMENT '总浏览量',
ADD COLUMN total_likes BIGINT DEFAULT 0 COMMENT '总点赞数',
ADD COLUMN total_favorites BIGINT DEFAULT 0 COMMENT '总收藏数',
ADD COLUMN followers INT DEFAULT 0 COMMENT '粉丝数',
ADD COLUMN following INT DEFAULT 0 COMMENT '关注数';

-- 创建团队表
CREATE TABLE team (
    team_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '团队ID',
    name VARCHAR(100) NOT NULL COMMENT '团队名称',
    avatar_url VARCHAR(500) COMMENT '团队头像URL',
    description TEXT COMMENT '团队描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    privacy VARCHAR(20) DEFAULT 'private' COMMENT '隐私设置：public, private',
    invite_setting VARCHAR(30) DEFAULT 'admin_approval' COMMENT '邀请设置：admin_approval, member_invite, open',
    tags JSON COMMENT '标签列表',
    member_count INT DEFAULT 0 COMMENT '成员数量',
    articles_recommended INT DEFAULT 0 COMMENT '推荐文章数',
    total_views BIGINT DEFAULT 0 COMMENT '总浏览量',
    total_likes BIGINT DEFAULT 0 COMMENT '总点赞数',
    total_favorites BIGINT DEFAULT 0 COMMENT '总收藏数',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='团队表';

-- 创建团队成员表
CREATE TABLE team_member (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    team_id BIGINT NOT NULL COMMENT '团队ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role VARCHAR(20) DEFAULT 'member' COMMENT '角色：admin, member',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_team_user (team_id, user_id),
    KEY idx_team_id (team_id),
    KEY idx_user_id (user_id)
) COMMENT='团队成员表';

-- 创建内容表
CREATE TABLE content (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '内容ID',
    title VARCHAR(200) NOT NULL COMMENT '标题',
    description TEXT COMMENT '描述',
    knowledge_type_code VARCHAR(50) COMMENT '知识类型代码：prompt, article, tool, course',
    author_id BIGINT NOT NULL COMMENT '作者ID',
    author_name VARCHAR(100) COMMENT '作者名称',
    content LONGTEXT COMMENT '内容详情',
    tags JSON COMMENT '标签列表',
    views BIGINT DEFAULT 0 COMMENT '浏览量',
    likes BIGINT DEFAULT 0 COMMENT '点赞数',
    favorites BIGINT DEFAULT 0 COMMENT '收藏数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    KEY idx_author_id (author_id),
    KEY idx_knowledge_type (knowledge_type_code),
    KEY idx_created_at (created_at)
) COMMENT='内容表';

-- 创建团队推荐表
CREATE TABLE team_recommendation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '推荐ID',
    team_id BIGINT NOT NULL COMMENT '团队ID',
    content_id BIGINT NOT NULL COMMENT '内容ID',
    recommender_id BIGINT NOT NULL COMMENT '推荐人ID',
    recommender_name VARCHAR(100) COMMENT '推荐人名称',
    recommender_avatar VARCHAR(500) COMMENT '推荐人头像',
    recommended_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '推荐时间',
    reason TEXT COMMENT '推荐理由',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_team_content (team_id, content_id),
    KEY idx_team_id (team_id),
    KEY idx_content_id (content_id),
    KEY idx_recommender_id (recommender_id)
) COMMENT='团队推荐表';

-- 创建用户学习信息表
CREATE TABLE user_learning (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    total_learning_hours DECIMAL(10,2) DEFAULT 0.00 COMMENT '总学习时长',
    courses_completed INT DEFAULT 0 COMMENT '完成课程数',
    consecutive_learning_days INT DEFAULT 0 COMMENT '连续学习天数',
    in_progress_data JSON COMMENT '进行中的学习数据',
    weekly_goals_data JSON COMMENT '周目标数据',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_user_id (user_id)
) COMMENT='用户学习信息表';

-- 插入示例数据
-- 更新用户表示例数据
UPDATE sys_user SET 
    display_name = '魏立明',
    bio = '专注于AI工程化实践，热爱探索大语言模型在实际业务中的应用。',
    department = '技术部',
    tags = '["大语言模型", "AI工程化", "Python", "Vue.js"]',
    articles_published = 58,
    total_views = 125000,
    total_likes = 8900,
    total_favorites = 1200,
    followers = 999,
    following = 120
WHERE id = 1;

-- 插入团队示例数据
INSERT INTO team (name, avatar_url, description, privacy, invite_setting, tags, member_count, articles_recommended, total_views, total_likes, total_favorites) VALUES
('AI创新探索小组', 'https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=150&h=150&fit=crop', '探索前沿AI技术和落地场景，分享最新的AI工程化实践经验。', 'private', 'admin_approval', '["AIGC", "多模态", "Agent", "大语言模型"]', 15, 250, 500000, 25000, 8000),
('产品设计团队', 'https://images.unsplash.com/photo-1552664730-d307ca884978?w=150&h=150&fit=crop', '专注于产品设计和用户体验优化，打造更好的产品。', 'public', 'member_invite', '["产品设计", "用户体验", "原型设计", "交互设计"]', 8, 120, 180000, 12000, 3500),
('技术分享社区', 'https://images.unsplash.com/photo-1556075798-4825dfaaf498?w=150&h=150&fit=crop', '技术人员交流分享的平台，涵盖前端、后端、AI等各个技术领域。', 'public', 'open', '["技术分享", "前端", "后端", "AI", "开源"]', 25, 380, 720000, 45000, 15000);

-- 插入团队成员示例数据
INSERT INTO team_member (team_id, user_id, role) VALUES
(1, 1, 'admin'),
(3, 1, 'member');

-- 插入内容示例数据
INSERT INTO content (title, description, knowledge_type_code, author_id, author_name, content, tags, views, likes, favorites) VALUES
('专业邮件写作助手', '帮助用户撰写专业、礼貌且有效的商务邮件，提升职场沟通效率。', 'prompt', 1, '魏立明', '你是一个专业的邮件写作助手...', '["邮件写作", "商务沟通", "职场技能"]', 1590, 245, 123),
('代码审查清单生成器', '根据编程语言和项目类型，生成详细的代码审查清单。', 'prompt', 1, '魏立明', '请为以下编程语言和项目类型生成代码审查清单...', '["代码审查", "软件开发", "质量保证"]', 890, 156, 89);

-- 插入团队推荐示例数据
INSERT INTO team_recommendation (team_id, content_id, recommender_id, recommender_name, recommender_avatar, reason) VALUES
(1, 1, 1, '魏立明', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face', '这个邮件写作助手非常实用，可以帮助团队成员提升商务沟通效率。'),
(1, 2, 1, '魏立明', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face', '代码审查是保证代码质量的重要环节，这个清单生成器很有价值。');

-- 插入用户学习信息示例数据
INSERT INTO user_learning (user_id, total_learning_hours, courses_completed, consecutive_learning_days, in_progress_data, weekly_goals_data) VALUES
(1, 156.5, 12, 7, '[{"courseId": 1, "courseName": "AI工程化实践", "progress": 75}]', '[{"goal": "完成AI课程", "progress": 60, "target": 100}]');
