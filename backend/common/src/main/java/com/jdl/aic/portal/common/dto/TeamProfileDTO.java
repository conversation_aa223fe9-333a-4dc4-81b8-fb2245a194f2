package com.jdl.aic.portal.common.dto;

import java.util.Date;
import java.util.List;

/**
 * 团队资料DTO
 */
public class TeamProfileDTO {
    private BasicInfoDTO basicInfo;
    private AchievementsDTO achievements;

    public static class BasicInfoDTO {
        private Long teamId;
        private String name;
        private String avatarUrl;
        private String description;
        private Date createdAt;
        private String privacy;
        private Integer memberCount;
        private List<String> tags;

        // Getters and Setters
        public Long getTeamId() {
            return teamId;
        }

        public void setTeamId(Long teamId) {
            this.teamId = teamId;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getAvatarUrl() {
            return avatarUrl;
        }

        public void setAvatarUrl(String avatarUrl) {
            this.avatarUrl = avatarUrl;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public Date getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(Date createdAt) {
            this.createdAt = createdAt;
        }

        public String getPrivacy() {
            return privacy;
        }

        public void setPrivacy(String privacy) {
            this.privacy = privacy;
        }

        public Integer getMemberCount() {
            return memberCount;
        }

        public void setMemberCount(Integer memberCount) {
            this.memberCount = memberCount;
        }

        public List<String> getTags() {
            return tags;
        }

        public void setTags(List<String> tags) {
            this.tags = tags;
        }
    }

    public static class AchievementsDTO {
        private Integer articlesRecommended;
        private Long totalViews;
        private Long totalLikes;
        private Long totalFavorites;

        // Getters and Setters
        public Integer getArticlesRecommended() {
            return articlesRecommended;
        }

        public void setArticlesRecommended(Integer articlesRecommended) {
            this.articlesRecommended = articlesRecommended;
        }

        public Long getTotalViews() {
            return totalViews;
        }

        public void setTotalViews(Long totalViews) {
            this.totalViews = totalViews;
        }

        public Long getTotalLikes() {
            return totalLikes;
        }

        public void setTotalLikes(Long totalLikes) {
            this.totalLikes = totalLikes;
        }

        public Long getTotalFavorites() {
            return totalFavorites;
        }

        public void setTotalFavorites(Long totalFavorites) {
            this.totalFavorites = totalFavorites;
        }
    }

    // Main class getters and setters
    public BasicInfoDTO getBasicInfo() {
        return basicInfo;
    }

    public void setBasicInfo(BasicInfoDTO basicInfo) {
        this.basicInfo = basicInfo;
    }

    public AchievementsDTO getAchievements() {
        return achievements;
    }

    public void setAchievements(AchievementsDTO achievements) {
        this.achievements = achievements;
    }
}
